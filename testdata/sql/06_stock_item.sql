-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_stock_items (
    id SERIAL PRIMARY KEY,
    stock_location_id integer,
    variant_id integer,
    count_on_hand integer NOT NULL DEFAULT 0,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    backorderable boolean DEFAULT false,
    deleted_at timestamp without time zone,
    price numeric(10,2),
    cost numeric(10,2),
    popularity double precision DEFAULT 0.0,
    boosting_point double precision DEFAULT 0.0,
    in_stock boolean NOT NULL DEFAULT true,
    max_order_quantity integer,
    normal_cost numeric(10,2),
    normal_price numeric(10,2),
    criteo_uploaded_at timestamp without time zone,
    fb_uploaded_at timestamp without time zone,
    unit_price numeric(10,2),
    checked_at timestamp without time zone,
    weight_feedback_total numeric DEFAULT 0.0,
    weight_feedback_count integer DEFAULT 0,
    promotion_id integer,
    client_type_promo_price hstore NOT NULL DEFAULT ''::hstore,
    original_promo_price numeric(10,2),
    promotion_code character varying(255)
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_stock_items_pkey ON spree_stock_items(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_items_on_backorderable ON spree_stock_items(backorderable bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_items_on_deleted_at ON spree_stock_items(deleted_at timestamp_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_items_on_popularity ON spree_stock_items(popularity float8_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_items_on_stock_location_id ON spree_stock_items(stock_location_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_items_on_in_stock ON spree_stock_items(in_stock bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_items_on_variant_id ON spree_stock_items(variant_id int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS unique_index_stock_items__stock_location_id__variant_id ON spree_stock_items(stock_location_id int4_ops,variant_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_items_on_promotion_id ON spree_stock_items(promotion_id int4_ops);

INSERT INTO "spree_stock_items"("id","stock_location_id","variant_id","price","normal_price","cost","normal_cost","original_promo_price","client_type_promo_price","in_stock", "popularity")
VALUES
(11, 3, 1, 12000, 17000, 10000, 15000, 13000, E'"android"=>"12000", "ios"=>"12000", "webapp"=>"12000"', true, 1.0),
(12, 3, 2, 22000, 27000, 20000, 25000, 23000, E'"android"=>"22000", "ios"=>"22000", "webapp"=>"22000"', true, 3.0),
(13, 3, 3, 22000, 27000, 20000, 25000, 23000, E'"android"=>"22000", "ios"=>"22000", "webapp"=>"22000"', false, 2.0),
(14, 3, 4, 220000, 270000, 200000, 250000, 230000, E'"android"=>"220000", "ios"=>"220000", "webapp"=>"220000"', false, 2.0);
