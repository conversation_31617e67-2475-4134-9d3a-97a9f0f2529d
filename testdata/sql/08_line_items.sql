-- Table Definition ----------------------------------------------

CREATE TABLE spree_line_items (
    id SERIAL PRIMARY KEY,
    variant_id integer,
    order_id integer,
    quantity integer NOT NULL,
    price numeric(10,2) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    currency character varying(255),
    cost_price numeric(10,2),
    tax_category_id integer,
    adjustment_total numeric(10,2) DEFAULT 0.0,
    additional_tax_total numeric(10,2) DEFAULT 0.0,
    promo_total numeric(10,2) DEFAULT 0.0,
    included_tax_total numeric(10,2) NOT NULL DEFAULT 0.0,
    pre_tax_amount numeric(10,2) DEFAULT 0,
    stock_location_id integer,
    normal_price numeric(10,2),
    normal_cost_price numeric(10,2),
    replacement_type integer DEFAULT 1,
    actual_weight double precision,
    found_weight double precision,
    shopper_notes text,
    shopper_notes_fulfilled boolean DEFAULT false,
    is_product_mismatch boolean,
    weight_different boolean NOT NULL DEFAULT false,
    promotion_id integer,
    client_type character varying(255),
    original_promo_price numeric(10,2),
    normal_quantity integer,
    promo_quantity integer,
    normal_price_total numeric(10,2),
    promo_price_total numeric(10,2),
    promotion_code character varying(255)
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_line_items_pkey ON spree_line_items(id int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_line_items__order_id__variant_id__stock_location_id ON spree_line_items(order_id int4_ops,variant_id int4_ops,stock_location_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_line_items_on_order_id ON spree_line_items(order_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_line_items_on_stock_location_id ON spree_line_items(stock_location_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_line_items_on_tax_category_id ON spree_line_items(tax_category_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_line_items_on_variant_id ON spree_line_items(variant_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_line_items_on_promotion_id ON spree_line_items(promotion_id int4_ops);


INSERT INTO "spree_line_items"("id", "variant_id", "order_id", "quantity", "price")
VALUES
(111, 1, 1111, 4, 4000.00),
(112, 1, 1112, 5, 4000.00),
(113, 2, 1113, 6, 5000.00),
(114, 3, 1114, 7, 6000.00);