-- Table Definition
CREATE TABLE spree_assets (
    "id" SERIAL PRIMARY KEY,
    "viewable_id" int4,
    "viewable_type" varchar,
    "attachment_width" int4,
    "attachment_height" int4,
    "attachment_file_size" int4,
    "position" int4,
    "attachment_content_type" varchar,
    "attachment_file_name" varchar,
    "type" varchar,
    "attachment_updated_at" timestamp,
    "alt" text,
    "created_at" timestamp,
    "updated_at" timestamp,
    "global_id" int4,
    "properties" hstore DEFAULT ''::hstore
);

CREATE UNIQUE INDEX spree_assets_pkey_2 ON spree_assets(id int4_ops);
CREATE INDEX index_assets_on_viewable_id ON spree_assets(viewable_id int4_ops);
CREATE INDEX index_assets_on_viewable_type_and_type ON spree_assets(viewable_type text_ops,type text_ops);
CREATE UNIQUE INDEX index_spree_assets_on_global_id ON spree_assets(global_id int4_ops);

INSERT INTO "spree_assets"("id","global_id","viewable_id","viewable_type","position","attachment_file_name","attachment_updated_at","properties")
VALUES
(881, 9881, 1, E'Spree::Variant', 0, '881.png', '2015-11-28 09:20:04.128668', E'"is_price_regulated"=>"true"'),
(882, 9882, 2, E'Spree::Variant', 0, '882.png', '2015-11-28 09:20:04.128668', E'"is_price_regulated"=>"false"'),
(883, 9883, 3, E'Spree::Variant', 0, '883.png', '2015-11-28 09:20:04.128668', E'"is_price_regulated"=>"false"'),
(884, 9884, 4, E'Spree::Variant', 0, '884.png', '2015-11-28 09:20:04.128668', E'"is_price_regulated"=>"false"'),
(885, 9885, 4, E'Spree::Variant', 0, '885.png', '2015-11-28 09:20:04.128668', E'"is_price_regulated"=>"false"');
