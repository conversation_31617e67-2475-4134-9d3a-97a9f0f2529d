CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_users (
    id SERIAL PRIMARY KEY,
    encrypted_password character varying(128),
    password_salt character varying(128),
    email character varying(255),
    remember_token character varying(255),
    persistence_token character varying(255),
    reset_password_token character varying(255),
    perishable_token character varying(255),
    sign_in_count integer NOT NULL DEFAULT 0,
    failed_attempts integer NOT NULL DEFAULT 0,
    last_request_at timestamp without time zone,
    current_sign_in_at timestamp without time zone,
    last_sign_in_at timestamp without time zone,
    current_sign_in_ip character varying(255),
    last_sign_in_ip character varying(255),
    login character varying(255),
    ship_address_id integer,
    bill_address_id integer,
    authentication_token character varying(255),
    unlock_token character varying(255),
    locked_at timestamp without time zone,
    reset_password_sent_at timestamp without time zone,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    spree_api_key character varying(48),
    remember_created_at timestamp without time zone,
    code character varying(255),
    inviter_id integer,
    first_name character varying(255),
    last_name character varying(255),
    cash_id integer,
    point_id integer,
    email_verified boolean NOT NULL DEFAULT true,
    phone character varying(255),
    phone_verification_code character varying(255),
    phone_verification_code_sent_count integer NOT NULL DEFAULT 0,
    verified boolean NOT NULL DEFAULT false,
    phone_verification_code_sent_at timestamp without time zone,
    facebook_id character varying(255),
    birthday date,
    preferences text,
    gender integer,
    min_age integer,
    max_age integer,
    last_selected_cc_reference character varying(255),
    normalized_email character varying(255),
    email_status integer,
    properties hstore NOT NULL DEFAULT ''::hstore,
    company_id integer,
    grab_token character varying(255),
    grab_associated_at timestamp without time zone,
    facebook_associated_at timestamp without time zone,
    sign_up_client_type character varying(255) DEFAULT 'default'::character varying,
    profile_picture_file_name character varying(255),
    profile_picture_content_type character varying(255),
    profile_picture_file_size integer,
    profile_picture_updated_at timestamp without time zone,
    google_id character varying(255),
    google_associated_at timestamp without time zone,
    ab_markup character varying(255)
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_users_pkey ON spree_users(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_spree_api_key ON spree_users(spree_api_key text_ops);
CREATE UNIQUE INDEX IF NOT EXISTS email_idx_unique ON spree_users(email text_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_users_on_code ON spree_users(code text_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_inviter_id ON spree_users(inviter_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_cash_id ON spree_users(cash_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_point_id ON spree_users(point_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_phone_verification_code ON spree_users(phone_verification_code text_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_phone_and_verified ON spree_users(phone text_ops,verified bool_ops) WHERE verified = true;
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_users_on_facebook_id ON spree_users(facebook_id text_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_reset_password_token ON spree_users(reset_password_token text_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_email_verified ON spree_users(email_verified bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_phone ON spree_users(phone text_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_normalized_email ON spree_users(normalized_email text_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_email_status ON spree_users(email_status int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_company_id ON spree_users(company_id int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_users_on_grab_token ON spree_users(grab_token text_ops);
CREATE INDEX IF NOT EXISTS index_spree_users_on_email ON spree_users USING GIN (email gin_trgm_ops);


INSERT INTO "spree_users"("id", "spree_api_key", "ab_markup")
VALUES
(6,E'ecd65021997ed79ded87a9c5db882494d9fb2aa920889e87', 'B-experiment-123');