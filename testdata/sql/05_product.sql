-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_products (
    id SERIAL PRIMARY KEY,
    name character varying(255) NOT NULL DEFAULT ''::character varying,
    description text,
    available_on timestamp without time zone,
    deleted_at timestamp without time zone,
    slug character varying(255),
    meta_description text,
    meta_keywords character varying(255),
    tax_category_id integer,
    shipping_category_id integer,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    product_type_id integer,
    brand_id integer,
    properties hstore NOT NULL DEFAULT ''::hstore,
    ean_id integer,
    product_attribute_ids integer[] NOT NULL DEFAULT '{}'::integer[],
    supplier_code character varying(255),
    global_id integer,
    sellable_items character varying
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_products_pkey ON spree_products(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_available_on ON spree_products(available_on timestamp_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_deleted_at ON spree_products(deleted_at timestamp_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_name ON spree_products(name text_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_shipping_category_id ON spree_products(shipping_category_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_slug ON spree_products(slug text_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_tax_category_id ON spree_products(tax_category_id int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS permalink_idx_unique ON spree_products(slug text_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_product_type_id ON spree_products(product_type_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_brand_id ON spree_products(brand_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_properties ON spree_products USING GIN (properties gin_hstore_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_supplier_code ON spree_products(supplier_code text_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_on_ean_id ON spree_products(ean_id int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_products_on_global_id ON spree_products(global_id int4_ops);


INSERT INTO "spree_products"("id","global_id","name","description","product_type_id","brand_id","properties")
VALUES
(1, 91, E'P01', E'Product 01 - apple', 101, 1, E'"size"=>"1", "avg_weight"=>"0.5", "unit_pieces"=>"1", "natural_unit"=>"each", "sell_natural"=>"true", "supermarket_unit"=>"kg", "max_order_quantity"=>"5"'),
(2, 92, E'P02', E'Product 02 - orange', 102, 1, E'"size"=>"1", "avg_weight"=>"0.5", "unit_pieces"=>"1", "natural_unit"=>"each", "sell_natural"=>"true", "supermarket_unit"=>"kg", "max_order_quantity"=>"5"'),
(3, 93, E'P03', E'Product 03 - watermelon', 201, 1, E'"size"=>"1", "avg_weight"=>"0.5", "unit_pieces"=>"1", "natural_unit"=>"each", "sell_natural"=>"true", "supermarket_unit"=>"kg", "max_order_quantity"=>"5"'),
(4, 94, E'P04', E'Product 04 - grape', 202, 1, E'"size"=>"1", "avg_weight"=>"0.5", "unit_pieces"=>"1", "natural_unit"=>"each", "sell_natural"=>"true", "supermarket_unit"=>"kg", "max_order_quantity"=>"5"'),
(5, 95, E'P05', E'Product 05', null, null, E'"size"=>"1", "avg_weight"=>"0.5", "unit_pieces"=>"1", "natural_unit"=>"each", "sell_natural"=>"true", "supermarket_unit"=>"kg", "max_order_quantity"=>"5"');


-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_product_translations (
    id SERIAL PRIMARY KEY,
    spree_product_id integer,
    locale character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    name character varying(255),
    description text,
    meta_description character varying(255),
    meta_keywords character varying(255),
    slug character varying(255)
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_product_translations_pkey ON spree_product_translations(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_product_translations_on_spree_product_id ON spree_product_translations(spree_product_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_product_translations_on_locale ON spree_product_translations(locale text_ops);

INSERT INTO "spree_product_translations"("id","spree_product_id","locale","name","description")
VALUES
(11, 1, E'id', E'P01-ID', E'P01-ID Desc'),
(14, 1, E'en', E'P01-EN', E'P01-EN Desc'),
(21, 2, E'id', E'P02-ID', E'P02-ID Desc'),
(24, 2, E'en', E'P02-EN', E'P02-EN Desc'),
(31, 3, E'id', E'P03-ID', E'P03-ID Desc'),
(34, 3, E'en', E'P03-EN', E'P03-EN Desc'),
(41, 4, E'id', E'P04-ID', E'P04-ID Desc'),
(44, 4, E'en', E'P04-EN', E'P04-EN Desc');


-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_products_taxons (
    product_id integer,
    taxon_id integer,
    id SERIAL PRIMARY KEY,
    position integer
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_products_taxons_pkey ON spree_products_taxons(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_taxons_on_position ON spree_products_taxons(position int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_taxons_on_product_id ON spree_products_taxons(product_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_products_taxons_on_taxon_id ON spree_products_taxons(taxon_id int4_ops);

INSERT INTO "spree_products_taxons"("id","product_id","taxon_id")
VALUES
(1, 1, 101),
(2, 2, 102),
(3, 3, 201),
(4, 4, 202);


-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_variants (
    id SERIAL PRIMARY KEY,
    sku character varying(255) NOT NULL DEFAULT ''::character varying,
    weight numeric(8,2) DEFAULT 0.0,
    height numeric(8,2),
    width numeric(8,2),
    depth numeric(8,2),
    deleted_at timestamp without time zone,
    is_master boolean DEFAULT false,
    product_id integer,
    cost_price numeric(10,2),
    position integer,
    cost_currency character varying(255),
    track_inventory boolean DEFAULT true,
    tax_category_id integer,
    updated_at timestamp without time zone,
    images_count smallint DEFAULT 0,
    global_id integer
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_variants_pkey ON spree_variants(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_variants_on_deleted_at ON spree_variants(deleted_at timestamp_ops);
CREATE INDEX IF NOT EXISTS index_spree_variants_on_is_master ON spree_variants(is_master bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_variants_on_position ON spree_variants(position int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_variants_on_product_id ON spree_variants(product_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_variants_on_sku ON spree_variants(sku text_ops);
CREATE INDEX IF NOT EXISTS index_spree_variants_on_tax_category_id ON spree_variants(tax_category_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_variants_on_track_inventory ON spree_variants(track_inventory bool_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_variants_on_global_id ON spree_variants(global_id int4_ops);

INSERT INTO "spree_variants"("id","global_id","sku","product_id","is_master")
VALUES
(1, 91, E'SKU01', 1, TRUE),
(2, 92, E'SKU02', 2, TRUE),
(3, 93, E'SKU03', 3, TRUE),
(4, 94, E'SKU04', 4, TRUE);
