CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Table Definition ----------------------------------------------

CREATE TABLE spree_promotions (
    id SERIAL PRIMARY KEY,
    description character varying(255),
    expires_at timestamp without time zone,
    starts_at timestamp without time zone,
    name character varying(255),
    type character varying(255),
    usage_limit integer,
    match_policy character varying(255) DEFAULT 'all'::character varying,
    code character varying(255),
    advertise boolean DEFAULT false,
    path character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    promo_type integer DEFAULT 0,
    enable_promotion_combination boolean DEFAULT false,
    evaluated_by_delivered_items boolean DEFAULT false,
    country_id integer,
    source integer NOT NULL,
    display_name text
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX spree_activators_pkey ON spree_promotions(id int4_ops);
CREATE INDEX index_spree_promotions_on_id_and_type ON spree_promotions(id int4_ops,type text_ops);
CREATE INDEX index_spree_promotions_on_code ON spree_promotions(code text_ops);
CREATE INDEX index_spree_promotions_on_expires_at ON spree_promotions(expires_at timestamp_ops);
CREATE INDEX index_spree_promotions_on_starts_at ON spree_promotions(starts_at timestamp_ops);
CREATE INDEX index_spree_promotions_on_advertise ON spree_promotions(advertise bool_ops);
CREATE INDEX index_spree_promotions_on_usage_limit ON spree_promotions(usage_limit int4_ops);
CREATE INDEX index_spree_promotions_on_promo_type ON spree_promotions(promo_type int4_ops);
CREATE INDEX index_spree_promotions_on_enable_promotion_combination ON spree_promotions(enable_promotion_combination bool_ops);
CREATE INDEX index_spree_promotions_on_evaluated_by_delivered_items ON spree_promotions(evaluated_by_delivered_items bool_ops);
CREATE INDEX idx_spree_promotions_lower_code ON spree_promotions((lower(code::text)) text_ops);
CREATE INDEX index_spree_promotions_on_country_id ON spree_promotions(country_id int4_ops);
CREATE INDEX index_spree_promotions_on_source ON spree_promotions(source int4_ops);
CREATE INDEX index_spree_promotions_on_name ON spree_promotions USING GIN (name gin_trgm_ops);

-- Table Definition ----------------------------------------------

CREATE TABLE spree_promotion_rules (
    id SERIAL PRIMARY KEY,
    promotion_id integer,
    user_id integer,
    product_group_id integer,
    type character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    code character varying(255),
    preferences text
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX spree_promotion_rules_pkey_2 ON spree_promotion_rules(id int4_ops);
CREATE INDEX index_promotion_rules_on_product_group_id ON spree_promotion_rules(product_group_id int4_ops);
CREATE INDEX index_promotion_rules_on_user_id ON spree_promotion_rules(user_id int4_ops);
CREATE INDEX index_spree_promotion_rules_on_promotion_id ON spree_promotion_rules(promotion_id int4_ops);
CREATE INDEX index_spree_promotion_rules_on_type ON spree_promotion_rules(type text_ops);



-- Table Definition ----------------------------------------------

CREATE TABLE spree_stock_locations_promotion_rules (
    id SERIAL PRIMARY KEY,
    stock_location_id integer,
    promotion_rule_id integer
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX spree_stock_locations_promotion_rules_pkey_2 ON spree_stock_locations_promotion_rules(id int4_ops);
CREATE INDEX stock_locations_promotion_rules_stock_location_id ON spree_stock_locations_promotion_rules(stock_location_id int4_ops);
CREATE INDEX stock_locations_promotion_rules_promotion_rule_id ON spree_stock_locations_promotion_rules(promotion_rule_id int4_ops);



-- Table Definition ----------------------------------------------

CREATE TABLE spree_products_promotion_rules (
    product_id integer,
    promotion_rule_id integer,
    id SERIAL PRIMARY KEY
);

-- Indices -------------------------------------------------------

CREATE INDEX index_products_promotion_rules_on_product_id ON spree_products_promotion_rules(product_id int4_ops);
CREATE INDEX index_products_promotion_rules_on_promotion_rule_id ON spree_products_promotion_rules(promotion_rule_id int4_ops);
CREATE UNIQUE INDEX spree_products_promotion_rules_pkey_2 ON spree_products_promotion_rules(id int4_ops);



-- Table Definition ----------------------------------------------

CREATE TABLE spree_taxons_promotion_rules (
    id SERIAL PRIMARY KEY,
    taxon_id integer,
    promotion_rule_id integer
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX spree_taxons_promotion_rules_pkey_2 ON spree_taxons_promotion_rules(id int4_ops);
CREATE INDEX index_taxons_promotion_rules_on_taxon_id ON spree_taxons_promotion_rules(taxon_id int4_ops);
CREATE INDEX index_taxons_promotion_rules_on_promotion_rule_id ON spree_taxons_promotion_rules(promotion_rule_id int4_ops);


INSERT INTO "spree_promotions"("id","name", "source")
VALUES
(1, 'Promo Apa Ini', 10),
(2, 'Promo Apa Ini - 2', 10),
(3, 'Promo Apa Ini - 3', 10),
(4, 'Promo Apa Ini - 4', 10),
(5, 'Promo Apa Ini - 5', 10),
(6, 'Promo Apa Ini - 6', 10),
(7, 'Promo Apa Ini - 7', 10),
(8, 'Promo Apa Ini - 8', 10),
(9, 'Promo Apa Ini - 9', 10);


INSERT INTO "spree_promotion_rules"("id", "promotion_id", "type", "preferences")
VALUES
(1, 1, 'Spree::Promotion::Rules::StockLocationRule', 'match_policy: any'),
(2, 1, 'Spree::Promotion::Rules::TaxonRule', 'match_policy: any'),
(3, 2, 'Spree::Promotion::Rules::StockLocationRule', 'match_policy: any'),
(4, 2, 'Spree::Promotion::Rules::Product', 'match_policy: any'),
(5, 3, 'Spree::Promotion::Rules::Product', 'match_policy: any'),
(6, 4, 'Spree::Promotion::Rules::TaxonRule', 'match_policy: none'),
(7, 4, 'Spree::Promotion::Rules::StockLocationRule', 'match_policy: any'),
(8, 5, 'Spree::Promotion::Rules::Product', 'match_policy: none'),
(9, 5, 'Spree::Promotion::Rules::StockLocationRule', 'match_policy: any'),
(10, 6, 'Spree::Promotion::Rules::Product', 'match_policy: any'),
(11, 6, 'Spree::Promotion::Rules::TaxonRule', 'match_policy: any'),
(12, 6, 'Spree::Promotion::Rules::StockLocationRule', 'match_policy: any'),
(13, 7, 'Spree::Promotion::Rules::Product', 'match_policy: any'),
(14, 7, 'Spree::Promotion::Rules::TaxonRule', 'match_policy: none'),
(15, 7, 'Spree::Promotion::Rules::StockLocationRule', 'match_policy: any'),
(16, 8, 'Spree::Promotion::Rules::Product', 'match_policy: none'),
(17, 8, 'Spree::Promotion::Rules::TaxonRule', 'match_policy: any'),
(18, 8, 'Spree::Promotion::Rules::StockLocationRule', 'match_policy: any'),
(19, 9, 'Spree::Promotion::Rules::Product', 'match_policy: none'),
(20, 9, 'Spree::Promotion::Rules::TaxonRule', 'match_policy: none'),
(21, 9, 'Spree::Promotion::Rules::StockLocationRule', 'match_policy: any');


INSERT INTO "spree_stock_locations_promotion_rules"("id", "stock_location_id", "promotion_rule_id")
VALUES
(1, 3, 1),
(2, 3, 3),
(3, 3, 7),
(4, 3, 9),
(5, 3, 12),
(6, 3, 15),
(7, 3, 18),
(8, 3, 21);


INSERT INTO "spree_taxons_promotion_rules"("id", "taxon_id", "promotion_rule_id")
VALUES
(1, 101, 2),
(2, 201, 2),
(3, 202, 6),
(4, 201, 11),
(5, 202, 11),
(6, 202, 14),
(7, 202, 17),
(8, 202, 20);


INSERT INTO "spree_products_promotion_rules"("id", "product_id", "promotion_rule_id")
VALUES
(1, 2, 4),
(2, 4, 5),
(3, 2, 8),
(4, 1, 10),
(5, 1, 13),
(6, 2, 13),
(7, 1, 16),
(8, 1, 19);
