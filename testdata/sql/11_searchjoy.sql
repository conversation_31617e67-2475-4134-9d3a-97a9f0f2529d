-- Table Definition ----------------------------------------------

CREATE TABLE searchjoy_searches (
    id SERIAL PRIMARY KEY,
    search_type character varying(255),
    query character varying(255),
    normalized_query character varying(255),
    results_count integer,
    created_at timestamp without time zone,
    convertable_id integer,
    convertable_type character varying(255),
    converted_at timestamp without time zone,
    country_iso character varying(255),
    city_id integer,
    language character varying(255),
    stock_location_id integer,
    user_id integer,
    external_id text,
    properties hstore
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX searchjoy_searches_pkey_2 ON searchjoy_searches(id int4_ops);
CREATE INDEX index_searchjoy_searches_on_created_at ON searchjoy_searches(created_at timestamp_ops);
CREATE INDEX index_searchjoy_searches_on_search_type_and_created_at ON searchjoy_searches(search_type text_ops,created_at timestamp_ops);
CREATE INDEX index_searchjoy_searches_on_search_type_and_normalized_query_an ON searchjoy_searches(search_type text_ops,normalized_query text_ops,created_at timestamp_ops);
CREATE INDEX index_searchjoy_searches_on_convertable_id_and_convertable_type ON searchjoy_searches(convertable_id int4_ops,convertable_type text_ops);
CREATE INDEX index_searchjoy_searches_on_stock_location_id ON searchjoy_searches(stock_location_id int4_ops);
CREATE INDEX index_searchjoy_searches_on_user_id ON searchjoy_searches(user_id int4_ops);
