CREATE EXTENSION IF NOT EXISTS hstore;

-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_countries (
    id SERIAL PRIMARY KEY,
    iso_name character varying(255),
    iso character varying(255),
    iso3 character varying(255),
    name character varying(255),
    numcode integer,
    states_required boolean DEFAULT false,
    updated_at timestamp without time zone,
    currency character varying(255),
    preferences text,
    active boolean DEFAULT true,
    taxonomy_id integer,
    nearby_store_strategy integer DEFAULT 0,
    nearby_store_slot_threshold integer DEFAULT 168
);

-- Indices -------------------------------------------------------

CREATE INDEX IF NOT EXISTS  index_spree_countries_on_iso_name ON spree_countries(iso_name text_ops);
CREATE INDEX IF NOT EXISTS  index_spree_countries_on_currency ON spree_countries(currency text_ops);
CREATE INDEX IF NOT EXISTS index_spree_countries_on_taxonomy_id ON spree_countries(taxonomy_id int4_ops);


INSERT INTO "spree_countries"("id","iso_name","name","currency")
VALUES
(1,E'ID',E'Indonesia',E'IDR'),
(2,E'MY',E'Malaysia',E'MYR'),
(3,E'TH',E'Thailand',E'THB'),
(4,E'TW',E'Taiwan',E'IDR'),
(5,E'PH',E'Philippines',E'IDR');


-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_stock_locations (
    id SERIAL PRIMARY KEY,
    name character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    address1 character varying(255),
    address2 character varying(255),
    city character varying(255),
    state_id integer,
    state_name character varying(255),
    country_id integer,
    zipcode character varying(255),
    phone character varying(255),
    active boolean DEFAULT true,
    backorderable_default boolean DEFAULT false,
    propagate_all_variants boolean DEFAULT true,
    admin_name character varying(255),
    store_id integer,
    supplier_id integer,
    lat double precision,
    lon double precision,
    photo_file_name character varying(255),
    photo_content_type character varying(255),
    photo_file_size integer,
    photo_updated_at timestamp without time zone,
    shopper_count integer,
    driver_count integer,
    start_hour integer DEFAULT 0,
    end_hour integer DEFAULT 13,
    track_stock boolean NOT NULL DEFAULT false,
    preferences text,
    oos_email_recipients text,
    oos_email_hour integer,
    oos_email_day_of_month integer,
    oos_email_day_of_week integer,
    oos_email_active boolean,
    cluster_id integer,
    max_delivery_volume double precision DEFAULT 100,
    max_delivery_number integer DEFAULT 2,
    max_uniq_item integer DEFAULT 12,
    store_type integer DEFAULT 0,
    minimum_to_spend numeric(10,2) NOT NULL DEFAULT 0.0,
    oos_qty_threshold integer DEFAULT 0,
    average_picking_time_per_uniq_item numeric NOT NULL DEFAULT 1.0,
    queue_and_replacement_time numeric NOT NULL DEFAULT 0.0,
    handover_time numeric NOT NULL DEFAULT 0.0,
    delivery_pooling_type character varying(255) DEFAULT 'sub_district_based'::character varying,
    delivery_pooling_max_roundtrip_time integer DEFAULT 0,
    delivery_pooling_max_handover_time integer DEFAULT 0,
    additional_shipping_distance_rate numeric(10,2) NOT NULL DEFAULT 0,
    offset_order_push_time integer NOT NULL DEFAULT 0,
    ship_distance_threshold double precision,
    tpl_enabled boolean DEFAULT false,
    average_item_count integer NOT NULL DEFAULT 0,
    hybrid_store boolean DEFAULT false,
    shopper_offset_order_push_time integer NOT NULL DEFAULT 0,
    enable_crossday_prepicking boolean NOT NULL DEFAULT false,
    enable_grab_express boolean DEFAULT false,
    grab_express_offset_time integer DEFAULT 0,
    meeting_point_name character varying(255),
    meeting_point_lat double precision,
    meeting_point_lon double precision,
    meeting_point_remark text,
    grab_express_delay_time integer DEFAULT 0,
    is_chat_enabled boolean DEFAULT false,
    enable_grab_express_cod boolean DEFAULT false,
    minimum_to_spend_for_first_time_order numeric(10,2) NOT NULL DEFAULT 0.0,
    slug character varying(255)
);

-- Indices -------------------------------------------------------

CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_active ON spree_stock_locations(active bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_backorderable_default ON spree_stock_locations(backorderable_default bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_country_id ON spree_stock_locations(country_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_propagate_all_variants ON spree_stock_locations(propagate_all_variants bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_state_id ON spree_stock_locations(state_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_store_id ON spree_stock_locations(store_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_supplier_id ON spree_stock_locations(supplier_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_cluster_id ON spree_stock_locations(cluster_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_track_stock ON spree_stock_locations(track_stock bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_store_type ON spree_stock_locations(store_type int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_tpl_enabled ON spree_stock_locations(tpl_enabled bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_stock_locations_on_ship_distance_threshold ON spree_stock_locations(ship_distance_threshold float8_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_stock_locations_on_slug ON spree_stock_locations(slug text_ops);

INSERT INTO "spree_stock_locations"("id","store_id","country_id","name")
VALUES
(3,1003,1,E'Ranch Market Pondok Indah'),
(5,1005,1,E'Ranch Market Grand Indonesia'),
(6,1006,1,E'Farmers Market SM Serpong'),
(9,1009,1,E'Farmers Market Bintaro Exchange'),
(45,10045,1,E'Farmers Market Baywalk'),
(46,10046,1,E'Ranch Market The Breeze'),
(47,10047,1,E'Farmers Market Epicentrum'),
(48,10048,1,E'Farmers Market Grand Metropolitan'),
(49,10049,1,E'Farmers Market Kelapa Gading'),
(50,10050,1,E'Farmers Market Citra 6'),
(51,2999,2,E'Tesco Ampang'),
(52,3999,3,E'Siam Paragon');
