-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_taxons (
    id SERIAL PRIMARY KEY,
    parent_id integer,
    position integer DEFAULT 0,
    name character varying(255) NOT NULL,
    permalink character varying(255),
    taxonomy_id integer,
    lft integer,
    rgt integer,
    icon_file_name character varying(255),
    icon_content_type character varying(255),
    icon_file_size integer,
    icon_updated_at timestamp without time zone,
    description text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    meta_title character varying(255),
    meta_description character varying(255),
    meta_keywords character varying(255),
    depth integer,
    preferences text,
    sorting_unit_price boolean DEFAULT false,
    popularity_position integer,
    slug character varying(255),
    id_meta_title character varying(255),
    my_meta_title character varying(255),
    th_meta_title character varying(255),
    id_meta_description character varying(255),
    my_meta_description character varying(255),
    th_meta_description character varying(255),
    global_id integer
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_taxons_pkey ON spree_taxons(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_taxons_on_position ON spree_taxons(position int4_ops);
CREATE INDEX IF NOT EXISTS index_taxons_on_parent_id ON spree_taxons(parent_id int4_ops);
CREATE INDEX IF NOT EXISTS index_taxons_on_permalink ON spree_taxons(permalink text_ops);
CREATE INDEX IF NOT EXISTS index_taxons_on_taxonomy_id ON spree_taxons(taxonomy_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_taxons_on_name ON spree_taxons(name text_ops);
CREATE INDEX IF NOT EXISTS index_spree_taxons_on_lft ON spree_taxons(lft int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_taxons_on_rgt ON spree_taxons(rgt int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_taxons_on_global_id ON spree_taxons(global_id int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_taxons_on_slug ON spree_taxons(slug text_ops);

INSERT INTO "spree_taxons"("id","parent_id","global_id","name","lft","rgt","depth")
VALUES
(999,NULL,999,E'Root',1,50,0),
(100,999,100,E'Taxon-01',1,4,1),
(101,100,101,E'Taxon-01-01',1,2,2),
(102,100,102,E'Taxon-01-02',3,4,2),
(200,999,200,E'Taxon-02',5,8,1),
(201,200,201,E'Taxon-02-01',5,6,2),
(202,200,202,E'Taxon-02-02',7,8,2);


-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_taxon_translations (
    id SERIAL PRIMARY KEY,
    spree_taxon_id integer,
    locale character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    name character varying(255),
    description text,
    meta_title character varying(255),
    meta_description character varying(255),
    meta_keywords character varying(255),
    permalink character varying(255),
    id_meta_title character varying(255),
    my_meta_title character varying(255),
    th_meta_title character varying(255),
    id_meta_description character varying(255),
    my_meta_description character varying(255),
    th_meta_description character varying(255)
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_taxon_translations_pkey ON spree_taxon_translations(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_taxon_translations_on_spree_taxon_id ON spree_taxon_translations(spree_taxon_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_taxon_translations_on_locale ON spree_taxon_translations(locale text_ops);

INSERT INTO "spree_taxon_translations"("id","spree_taxon_id","locale","name")
VALUES
(10001,100,E'id',E'Taxon-01-ID'),
(10004,100,E'en',E'Taxon-01-EN'),
(10101,101,E'id',E'Taxon-01-01-ID'),
(10104,101,E'en',E'Taxon-01-01-EN'),
(10201,102,E'id',E'Taxon-01-02-ID'),
(10204,102,E'en',E'Taxon-01-02-EN'),
(20001,200,E'id',E'Taxon-02-ID'),
(20004,200,E'en',E'Taxon-02-EN'),
(20101,201,E'id',E'Taxon-02-01-ID'),
(20104,201,E'en',E'Taxon-02-01-EN'),
(20201,202,E'id',E'Taxon-02-02-ID'),
(20203,202,E'th',E'Taxon-02-02-TH'),
(20204,202,E'en',E'Taxon-02-02-EN');
