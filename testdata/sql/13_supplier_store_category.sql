-- Table Definition ----------------------------------------------

CREATE TABLE spree_supplier_store_categories (
    id SERIAL PRIMARY KEY,
    supplier_id integer,
    store_category_id integer
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX spree_supplier_store_categories_pkey_2 ON spree_supplier_store_categories(id int4_ops);
CREATE INDEX index_spree_supplier_store_categories_on_store_category_id ON spree_supplier_store_categories(store_category_id int4_ops);
CREATE UNIQUE INDEX supplier_id_and_store_category_id ON spree_supplier_store_categories(supplier_id int4_ops,store_category_id int4_ops);

INSERT INTO "spree_supplier_store_categories" ("id","supplier_id","store_category_id")
VALUES
(113,11,1),
(114,11,2),
(115,11,3),
(116,11,4),
(117,11,5);
