-- Table Definition ----------------------------------------------

CREATE SCHEMA IF NOT EXISTS analytics;

CREATE TABLE IF NOT EXISTS analytics.stock_item_promotions_v5 (
    store_id integer,
    sku character varying(255),
    type character varying(255),
    buy_x_quantity integer,
    get_y_quantity integer,
    promotion_id integer,
    get_p_percent float,
    max_quantity integer,
    is_exclusive boolean
);

INSERT INTO "analytics"."stock_item_promotions_v5"("store_id","sku","type","buy_x_quantity","get_y_quantity","promotion_id", "get_p_percent", "max_quantity", "is_exclusive")
VALUES
(1003, 'SKU01', 'discount', 0, 0,0, 0.0, null, null),
(1003, 'SKU02', 'discount', 0, 0,0, 0.0, null, null),
(1003, 'SKU03', 'discount', 0, 0,0, 0.0, null, null),
(1003, 'SKU02', 'free_shipping', 0, 0, 123, 0.0, null, null),
(1003, 'SKU01', 'buy_x_get_y', 2, 1, 123, 0.0, null, null),
(441, 'S21', 'buy_x_get_y', 2, 1, 123, 0.0, null, null),
(442, 'S21', 'buy_x_get_y', 2, 1, 123, 0.0, null, null),
(442, 'DUMMYSKU', 'buy_x_get_p', 2, 0, 1234, 0.0, null, null),
(442, 'DUMMYSKU', 'buy_x_get_p', 2, 0, 1234, 25.0, null, null),
(1992, 'wow1', 'free_shipping', 0, 0, 1111, 0.0, null, true),
(1992, 'wow2', 'buy_x_get_y', 2, 1, 1112, 0.0, null, true),
(1992, 'wow3', 'buy_x_get_p', 2, 1, 1113, 0.0, null, true);