-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_brands (
    id SERIAL PRIMARY KEY,
    name character varying(255),
    display_name_translations hstore,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    global_id integer
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_brands_pkey ON spree_brands(id int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_brands_on_name ON spree_brands(name text_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_brands_on_global_id ON spree_brands(global_id int4_ops);

INSERT INTO "spree_brands"("id","global_id","name","display_name_translations")
VALUES
(1,91,E'Bintang',E'"id"=>"Bintang", "en"=>"Star"'),
(2,92,E'Oreo',E'"id"=>"Oreo", "en"=>"Oreo"'),
(3,93,E'Chiki',E'"id"=>"Chiki", "en"=>"Chiki"');