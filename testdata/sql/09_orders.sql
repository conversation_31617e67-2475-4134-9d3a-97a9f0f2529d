-- Table Definition ----------------------------------------------

CREATE TABLE spree_orders (
    id SERIAL PRIMARY KEY,
    number character varying(32),
    item_total numeric(10,2) NOT NULL DEFAULT 0.0,
    total numeric(10,2) NOT NULL DEFAULT 0.0,
    state character varying(255),
    adjustment_total numeric(10,2) NOT NULL DEFAULT 0.0,
    user_id integer,
    completed_at timestamp without time zone,
    bill_address_id integer,
    ship_address_id integer,
    payment_total numeric(10,2) DEFAULT 0.0,
    shipping_method_id integer,
    shipment_state character varying(255),
    payment_state character varying(255),
    email character varying(255),
    special_instructions text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    currency character varying(255),
    last_ip_address character varying(255),
    created_by_id integer,
    shipment_total numeric(10,2) NOT NULL DEFAULT 0.0,
    additional_tax_total numeric(10,2) DEFAULT 0.0,
    promo_total numeric(10,2) DEFAULT 0.0,
    channel character varying(255) DEFAULT 'spree'::character varying,
    included_tax_total numeric(10,2) NOT NULL DEFAULT 0.0,
    item_count integer DEFAULT 0,
    approver_id integer,
    approved_at timestamp without time zone,
    confirmation_delivered boolean DEFAULT false,
    considered_risky boolean DEFAULT false,
    guest_token character varying(255),
    country_id integer,
    client_type character varying(255) NOT NULL DEFAULT 'default'::character varying,
    tax_correction numeric(10,2) NOT NULL DEFAULT 0.0,
    cancel_reason integer,
    checkout_client_type character varying(255) DEFAULT 'default'::character varying,
    canceled_at timestamp without time zone,
    confirmation_amendment_delivered integer DEFAULT 0,
    last_edited_at timestamp without time zone,
    ship_distance double precision,
    ship_distance_in_traffic double precision,
    account_abuse_score double precision,
    payment_abuse_score double precision,
    item_saving_total numeric(10,2) DEFAULT 0.0,
    company_id integer,
    erp_sent boolean DEFAULT false,
    erp_sent_at timestamp without time zone,
    company_invoiced_at timestamp without time zone,
    total_saving numeric(10,2) DEFAULT 0.0,
    cancel_reason_note text,
    checkout_client_version character varying(255),
    chat_status character varying(255) DEFAULT 'inactive'::character varying,
    is_chat_preferred boolean DEFAULT false,
    chat_channel_url character varying(255),
    chat_preferences hstore,
    appsflyer_id character varying(255),
    payment_version integer,
    invoice_delivered boolean DEFAULT false
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_orders_pkey ON spree_orders(id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_approver_id ON spree_orders(approver_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_bill_address_id ON spree_orders(bill_address_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_client_type ON spree_orders(client_type text_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_completed_at ON spree_orders(completed_at timestamp_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_confirmation_delivered ON spree_orders(confirmation_delivered bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_considered_risky ON spree_orders(considered_risky bool_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_country_id ON spree_orders(country_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_created_by_id ON spree_orders(created_by_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_currency ON spree_orders(currency text_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_guest_token ON spree_orders(guest_token text_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_number ON spree_orders(number text_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_payment_state ON spree_orders(payment_state text_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_ship_address_id ON spree_orders(ship_address_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_ship_distance ON spree_orders(ship_distance float8_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_shipment_state ON spree_orders(shipment_state text_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_shipping_method_id ON spree_orders(shipping_method_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_state ON spree_orders(state text_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_user_id ON spree_orders(user_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_user_id_and_created_by_id ON spree_orders(user_id int4_ops,created_by_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_account_abuse_score ON spree_orders(account_abuse_score float8_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_company_id ON spree_orders(company_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_email ON spree_orders(email text_ops);
CREATE INDEX IF NOT EXISTS index_spree_orders_on_appsflyer_id ON spree_orders(appsflyer_id text_ops);


INSERT INTO "spree_orders"("id", "user_id", "state")
VALUES
(1111, 211, E'complete'),
(1112, 212, E'complete'),
(1113, 213, E'complete');
