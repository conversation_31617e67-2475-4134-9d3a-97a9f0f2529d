-- Table Definition ----------------------------------------------

CREATE TABLE IF NOT EXISTS spree_product_types (
    id SERIAL PRIMARY KEY,
    code character varying(255),
    name character varying(255),
    display_name_translations hstore,
    parent_id integer,
    lft integer NOT NULL,
    rgt integer NOT NULL,
    depth integer NOT NULL DEFAULT 0,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    allowed_product_attribute_ids integer[] NOT NULL DEFAULT '{}'::integer[],
    preferences text,
    global_id integer
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX IF NOT EXISTS spree_product_types_pkey ON spree_product_types(id int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_product_types_on_name ON spree_product_types(name text_ops);
CREATE INDEX IF NOT EXISTS index_spree_product_types_on_parent_id ON spree_product_types(parent_id int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_product_types_on_lft ON spree_product_types(lft int4_ops);
CREATE INDEX IF NOT EXISTS index_spree_product_types_on_rgt ON spree_product_types(rgt int4_ops);
CREATE UNIQUE INDEX IF NOT EXISTS index_spree_product_types_on_global_id ON spree_product_types(global_id int4_ops);

INSERT INTO "spree_product_types"("id","parent_id","global_id","code","name","display_name_translations","lft","rgt","depth")
VALUES
(999,NULL,9999,E'R',E'root',NULL,1,50,0),
(100,999,9100,E'PTY-01',E'pty-01',E'"id"=>"PTY-01-ID", "en"=>"PTY-01-EN"',1,4,1),
(101,100,9101,E'PTY-01-01',E'pty-01-01',E'"id"=>"PTY-01-01-ID", "en"=>"PTY-01-01-EN"',1,2,2),
(102,100,9102,E'PTY-01-02',E'pty-01-02',E'"id"=>"PTY-01-02-ID", "en"=>"PTY-01-02-EN"',3,4,2),
(200,999,9200,E'PTY-02',E'pty-02',E'"id"=>"PTY-02-ID", "en"=>"PTY-02-EN"',5,8,1),
(201,200,9201,E'PTY-02-01',E'pty-02-01',E'"id"=>"PTY-02-01-ID", "en"=>"PTY-02-01-EN"',5,6,2),
(202,200,9202,E'PTY-02-02',E'pty-02-02',E'"id"=>"PTY-02-02-ID", "en"=>"PTY-02-02-EN", "th"=>"PTY-02-02-TH"',7,8,2);
