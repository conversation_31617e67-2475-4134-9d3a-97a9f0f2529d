#! /bin/bash

if [ "$1" == "" ]; then
  printf "\t\033[31mPlease use option 'ubuntu' or 'mac-os' \033[0;30m\033[41mFAILED!\033[0m\n"
fi

if [ "$1" == "ubuntu" ] && [ "$2" != "--skip-prerequisite" ]; then
  apt-get update && apt-get install unzip
elif [ "$1" == "mac-os" ] && [ "$2" != "--skip-prerequisite" ]; then
  brew install unzip
fi

if [ ! -d /usr/bin/bzr ]; then
  apt-get install -y bzr
fi

# Install golangci-lint
if [ ! -f /usr/local/bin/golangci-lint ]; then
  curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.61.0
fi

# Install protoc
if [ ! -f /usr/local/bin/protoc ]; then
  curl -L https://github.com/google/protobuf/releases/download/v3.20.1/protoc-3.20.1-linux-x86_64.zip -o protoc_download.zip
  unzip protoc_download.zip -d /tmp/protoc3

  mv /tmp/protoc3/bin/* /usr/local/bin/
  mv /tmp/protoc3/include/* /usr/local/include/
fi

# Install protoc-gen-gofast
if [ ! -f /usr/local/bin/protoc-gen-gofast ]; then
  go install github.com/gogo/protobuf/protoc-gen-gofast@latest
  cp `which protoc-gen-gofast` /usr/local/bin/
fi

# Install esc
if [ ! -f /usr/local/bin/esc ]; then
  go install github.com/mjibson/esc@v0.1.0
  cp `which esc` /usr/local/bin/
fi

if [ ! -f /usr/local/bin/protoc-gen-go ]; then
  go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.28.0
  cp `which protoc-gen-go` /usr/local/bin/
fi

if [ ! -f /usr/local/bin/protoc-gen-go-grpc ]; then
  go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.3.0
  cp `which protoc-gen-go-grpc` /usr/local/bin/
fi

# Install protoc-gen-gofast
if [ ! -f /usr/local/bin/protoc-gen-gofast ]; then
  go install github.com/gogo/protobuf/protoc-gen-gofast@latest
  cp `which protoc-gen-gofast` /usr/local/bin/
fi


exit 0
