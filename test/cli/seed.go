package main

import (
	"os"

	"happyfresh.io/lib/log"
	"happyfresh.io/search/core/datastore"
)

func main() {
	log.For("main", "seed").Infof("Seeding test data ...")

	done := datastore.OpenTest(nil, datastore.LocalQuery, os.Getenv("TEST_DATABASE_URL"), nil)
	defer done()

	err := datastore.Truncate()
	if err != nil {
		log.For("main", "seed").Error(err)
	}

	err = datastore.Seed()
	if err != nil {
		log.For("main", "seed").Error(err)
	}
}
