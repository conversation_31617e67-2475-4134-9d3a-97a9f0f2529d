package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"github.com/tidwall/gjson"
)

func TestReplacementSuggestion(t *testing.T) {
	if testing.Short() {
		t.<PERSON><PERSON><PERSON>()
	}

	convey.Convey("Given the replacement suggestion API", t, func() {
		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("X-Happy-Client-Type", "android")
		header.Set("Synthetic", "true")
		header.Set("Locale", "EN")
		header.Set("Country", "ID")

		params := url.Values{}
		params.Set("page", "1")
		params.Set("per_page", "5")

		convey.Convey("When do by product ID", func() {
			searchURl := "/api/stock_locations/3/products/replacement_suggestions/241288"

			convey.Convey("Should return products with the same product type ID", func() {
				req, err := newRequest(http.MethodGet, searchURl, header, params)
				if err != nil {
					t.Error(err)
				}

				resp, err := httpClient.Do(req)
				if err != nil {
					t.Error(err)
				}

				buf := &bytes.Buffer{}
				_, err = buf.ReadFrom(resp.Body)
				defer resp.Body.Close()
				if err != nil {
					t.Error(err)
				}

				json := gjson.ParseBytes(buf.Bytes())
				products := json.Get("products").Array()
				taxonIDFilters, taxonIDFiltersIsEmpty := getTaxonIDFilter(json)

				convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusOK)
				convey.So(len(products), convey.ShouldBeGreaterThan, 0)
				convey.So(taxonIDFiltersIsEmpty, convey.ShouldBeFalse)
				convey.So(taxonIDFilters, convey.ShouldResemble, []int64{24, 27, 103, 107})

				ptyNameMap := make(map[string]bool)
				for _, product := range products {
					ptyName := product.Get("product_type_name").String()
					if ptyName == "" {
						continue
					}

					ptyNameMap[ptyName] = true
				}

				ptyNames := []string{}
				for k := range ptyNameMap {
					ptyNames = append(ptyNames, k)
				}

				convey.So(ptyNames, convey.ShouldContain, "eggs/chicken/regular")
			})
		})

		convey.Convey("When do by product SKU", func() {
			searchURl := "/api/stock_locations/3/products/replacement_suggestions_by_sku/0400324180004-ID"

			convey.Convey("Should return products with the same product type ID", func() {
				req, err := newRequest(http.MethodGet, searchURl, header, params)
				if err != nil {
					t.Error(err)
				}

				resp, err := httpClient.Do(req)
				if err != nil {
					t.Error(err)
				}

				buf := &bytes.Buffer{}
				_, err = buf.ReadFrom(resp.Body)
				defer resp.Body.Close()
				if err != nil {
					t.Error(err)
				}

				json := gjson.ParseBytes(buf.Bytes())
				products := json.Get("products").Array()
				taxonIDFilters, taxonIDFiltersIsEmpty := getTaxonIDFilter(json)

				convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusOK)
				convey.So(len(products), convey.ShouldBeGreaterThan, 0)
				convey.So(taxonIDFiltersIsEmpty, convey.ShouldBeFalse)
				convey.So(taxonIDFilters, convey.ShouldResemble, []int64{24, 27, 103, 107})

				ptyNameMap := make(map[string]bool)
				for _, product := range products {
					ptyName := product.Get("product_type_name").String()
					if ptyName == "" {
						continue
					}

					ptyNameMap[ptyName] = true
				}

				ptyNames := []string{}
				for k := range ptyNameMap {
					ptyNames = append(ptyNames, k)
				}

				convey.So(ptyNames, convey.ShouldContain, "eggs/chicken/regular")
			})
		})
	})
}
