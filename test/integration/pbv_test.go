package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"strings"
	"testing"

	ext_assert "github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/test/integration/assert"
)

func TestPBVNew(t *testing.T) {
	if testing.Short() {
		t.Skip<PERSON>ow()
	}

	for _, tc := range []struct {
		name           string
		method         string
		header         map[string]string
		param          map[string][]string
		postVariantIDs []string
		assertOption   []assert.Option
	}{
		{
			"Relevant with given Variant IDs",
			http.MethodGet,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"per_page":     {"10"},
				"q[s]":         {"popularity desc"},
				"variant_id[]": {"14196", "14859", "24631", "24632", "24681", "30661"},
			},
			nil,
			[]assert.Option{
				assert.RelevancyOption([]int64{14214, 14877, 24649, 24650, 24699, 30679}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("MD Black Organic Rice", "Black Organic Rice with Natural Fragrance"),
				assert.BrandOption(&rpc.Brand{Id: 20314, Name: "md"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods", "Grains, Rice & Dried Goods"}, []int64{34, 44}),
			},
		},
		{
			"Relevant with given Variant IDs, localized",
			http.MethodGet,
			map[string]string{
				"Locale":              "id",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"per_page":     {"10"},
				"q[s]":         {"popularity desc"},
				"variant_id[]": {"14196", "14859", "24631", "24632", "24681", "30661"},
			},
			nil,
			[]assert.Option{
				assert.RelevancyOption([]int64{14214, 14877, 24649, 24650, 24699, 30679}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("MD Black Organic Rice-Localized", "Black Organic Rice with Natural Fragrance-Localized"),
				assert.BrandOption(&rpc.Brand{Id: 20314, Name: "md-Localized"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods-Localized", "Grains, Rice & Dried Goods-Localized"}, []int64{34, 44}),
			},
		},
		{
			"Relevant with given Variant IDs, localized, paginated",
			http.MethodGet,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"page":         {"2"},
				"per_page":     {"3"},
				"q[s]":         {"popularity desc"},
				"variant_id[]": {"14196", "14859", "24631", "24632", "24681", "30661"},
			},
			nil,
			[]assert.Option{
				assert.RelevancyOption([]int64{24650, 24699, 30679}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("Si Pulen Long Grain Crystal Rice", "Long Grain Crystal Rice"),
				assert.CategoriesOption([]string{"Dry & Canned Goods", "Grains, Rice & Dried Goods"}, []int64{34, 44}),
			},
		},
		{
			"Relevant with given Variant IDs, localized, paginated, POST",
			http.MethodPost,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"page":     {"2"},
				"per_page": {"3"},
				"q[s]":     {"popularity desc"},
			},
			[]string{"14196", "14859", "24631", "24632", "24681", "30661"},
			[]assert.Option{
				assert.RelevancyOption([]int64{24650, 24699, 30679}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("Si Pulen Long Grain Crystal Rice", "Long Grain Crystal Rice"),
				assert.CategoriesOption([]string{"Dry & Canned Goods", "Grains, Rice & Dried Goods"}, []int64{34, 44}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := "/api/stock_locations/402/product_by_variants"

			header := http.Header{}
			header.Set("Content-Type", "application/x-www-form-urlencoded")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			param.Set("category", "special")
			for k, v := range tc.param {
				param[k] = v
			}

			var req *http.Request
			var err error
			if tc.method == http.MethodPost {
				data := url.Values(map[string][]string{
					"variant_id[]": tc.postVariantIDs,
				})
				reader := strings.NewReader(data.Encode())
				req, err = newRequestWithBody(tc.method, searchUrl, header, param, reader)
				if err != nil {
					t.Error(err)
				}
			} else {
				req, err = newRequest(tc.method, searchUrl, header, param)
				if err != nil {
					t.Error(err)
				}
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			ext_assert.Equal(t, http.StatusOK, resp.StatusCode)

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}
