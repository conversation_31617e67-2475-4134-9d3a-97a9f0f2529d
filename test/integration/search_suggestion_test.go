package integration_test

import (
	"bytes"
	"fmt"
	"net/http"
	"net/url"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/tidwall/gjson"
	"happyfresh.io/search/test/integration/assert"
)

func TestSearchSuggestion(t *testing.T) {
	if testing.Short() {
		t.<PERSON>()
	}

	Convey("Given search suggestion uri", t, func() {
		uri := "/api/stock_locations/3/products/search_suggestions"

		Convey("Getting search suggestion while searching for \"telur\" in ID", func() {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			header.Set("X-Happy-Client-Type", "android")
			header.Set("Synthetic", "true")
			header.Set("Locale", "ID")
			header.Set("Country", "ID")

			params := url.Values{}
			params.Set("q", "telur")

			req, err := newRequest(http.MethodGet, uri, header, params)
			if err != nil {
				t.Error(err)
				return
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
				return
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			Convey("Got status OK", func() {
				So(resp.StatusCode, ShouldEqual, http.StatusOK)
			})

			json := gjson.ParseBytes(buf.Bytes())
			brands := json.Get("brands").Array()
			Convey("Got multiple brand suggestion", func() {
				So(len(brands), ShouldBeGreaterThan, 0)
			})

			taxons := json.Get("taxons").Array()
			Convey("Got multiple taxon suggestion", func() {
				So(len(taxons), ShouldBeGreaterThan, 0)
			})
		})

		Convey("Getting search suggestion while searching for \"telur\" in MY", func() {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			header.Set("X-Happy-Client-Type", "android")
			header.Set("Synthetic", "true")
			header.Set("Locale", "MY")
			header.Set("Country", "MY")

			params := url.Values{}
			params.Set("q", "telur")

			req, err := newRequest(http.MethodGet, uri, header, params)
			if err != nil {
				t.Error(err)
				return
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
				return
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			Convey("Got status OK", func() {
				So(resp.StatusCode, ShouldEqual, http.StatusOK)
			})

			json := gjson.ParseBytes(buf.Bytes())
			brands := json.Get("brands").Array()
			Convey("Got 1 brand suggestion", func() {
				So(len(brands), ShouldEqual, 1)
			})

			taxons := json.Get("taxons").Array()
			Convey("Got 1 taxon suggestion", func() {
				So(len(taxons), ShouldEqual, 1)
			})
		})

		Convey("Getting search suggestion while searching for \"telur\" in TH", func() {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			header.Set("X-Happy-Client-Type", "android")
			header.Set("Synthetic", "true")
			header.Set("Locale", "TH")
			header.Set("Country", "TH")

			params := url.Values{}
			params.Set("q", "telur")

			req, err := newRequest(http.MethodGet, uri, header, params)
			if err != nil {
				t.Error(err)
				return
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
				return
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			Convey("Got status InternalServerError", func() {
				So(resp.StatusCode, ShouldEqual, http.StatusInternalServerError)
			})
		})
	})
}

func TestIntegrationSearchSuggestion(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	for _, tc := range []struct {
		name            string
		stockLocationID int
		header          map[string]string
		param           map[string]string
		assertOption    []assert.Option
	}{
		{
			"Search suggestions have forbidden words but not returned",
			116,
			map[string]string{
				"country":             "id",
				"locale":              "en",
				"x-happy-client-type": "webapp",
			},
			map[string]string{
				"q": "mineral watery",
			},
			[]assert.Option{
				assert.KeywordsOption([]string{}),
			},
		},
		{
			"Search suggestions have forbidden words but returned without forbidden words",
			5,
			map[string]string{
				"country":             "id",
				"locale":              "id",
				"x-happy-client-type": "webapp",
			},
			map[string]string{
				"q": "seafood",
			},
			[]assert.Option{
				assert.KeywordsOption([]string{"seafood", "seafoodking", "frozen meat & seafood", "seafood king", "fiesta seafood"}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param.Set(k, v)
			}

			searchUrl := fmt.Sprintf("/api/stock_locations/%d/products/search_suggestions", tc.stockLocationID)
			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}
