package integration_test

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"testing"
	"time"

	"github.com/go-chi/chi"
	"github.com/go-redis/redis/v7"
	"happyfresh.io/lib/log"
	ssrv "happyfresh.io/search/api/handler"
	"happyfresh.io/search/api/handler/gateway"
	"happyfresh.io/search/api/router"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/config"
	"happyfresh.io/search/core/catalog"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/core/idconv"
	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/index/es/search"
	_ "happyfresh.io/search/core/index/es/search"
	"happyfresh.io/search/core/index/pq"
	"happyfresh.io/search/core/pricing"
	"happyfresh.io/search/core/session"
	"happyfresh.io/search/core/synonym"
	"happyfresh.io/search/core/taxon"
	"happyfresh.io/search/core/translation"
	w "happyfresh.io/search/lib/coco"
	_ "happyfresh.io/search/lib/coco/broker/redis"
	"happyfresh.io/search/lib/paperclip-go"
	catalogTest "happyfresh.io/search/test/integration/catalog"
	hubbleTest "happyfresh.io/search/test/integration/hubble"
	orionTest "happyfresh.io/search/test/integration/orion"
	pricingTest "happyfresh.io/search/test/integration/pricing"
	"happyfresh.io/search/worker"
)

var (
	searchService       rpc.SearchServiceServer
	catalogService      catalog.Client
	taxonService        taxon.TaxonService
	httpClient          *http.Client
	swiftypeClient      index.Client
	elasticsearchClient index.Client
	pqClient            index.Client
	redisClient         *redis.Client
	idConv              idconv.IDConv
	serverURL           string
	coco                *w.Worker
	suggesterShards     index.SuggesterShards
)

func TestMain(m *testing.M) {
	p, err := preparePaperClip()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	dsn := config.ReadDatabaseTestGatewayDSN()
	done := datastore.OpenTest(m, datastore.LocalQuery, dsn, p)
	defer done()

	pqClient, err = preparePQClient()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	hubbleClient, err := hubbleTest.New()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	idConv := idconv.New(hubbleClient)

	orionClient := orionTest.New()
	synonym.Init(synonym.WithOrionClient(orionClient))

	catalogService, err = catalogTest.New()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	index.Register("elasticsearch", search.NewDialer())

	elasticsearchClient, err = prepareElasticsearchClient()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	pricing.ProductPriceGetter = pricingTest.ProductPriceGetterMock

	err = pricingTest.InitAlohaClient()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	suggesterShards, err = prepareSuggesterShards()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	taxonService, err = prepareTaxonRank()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	taxonService, err = prepareTaxonRank()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	translator := &translation.TranslatorMock{}

	searchService = ssrv.New(
		ssrv.WithPQClient(pqClient),
		ssrv.WithElasticsearchClient(elasticsearchClient),
		ssrv.WithDisableSync(true),
		ssrv.WithIDConverter(idConv),
		ssrv.WithCatalogClient(catalogService),
		ssrv.WithSuggesterShards(suggesterShards),
		ssrv.WithOrionClient(orionClient),
		ssrv.WithMaxGlobalSLI(config.MaxGlobalSLI()),
		ssrv.WithMaxGlobalProduct(config.MaxGlobalProduct()),
		ssrv.WithDefaultMaxMOQ(config.MaxMOQ()),
		ssrv.WithDefaultMaxMOQHC(config.MaxMOQHC()),
		ssrv.WithHubbleClient(hubbleClient),
		ssrv.WithTranslator(translator),
	)

	gateway.New(
		gateway.WithSearchService(searchService),
		gateway.WithCatalogService(catalogService),
		gateway.WithIDConverver(idConv),
		gateway.WithDefaultMaxMOQ(config.MaxMOQ()),
		gateway.WithDefaultMaxMOQHC(config.MaxMOQHC()),
		gateway.WithTaxonService(taxonService),
		gateway.WithHubbleService(hubbleClient),
	)

	redisClient, err = prepareRedis()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}
	defer redisClient.FlushDB()

	session.Setup(config.RedisDSNTest(), 1*time.Hour)

	coco, err = prepareCoco()
	if err != nil {
		log.For("integration_test", "TestMain").Error(err)
		os.Exit(1)
	}

	r := chi.NewRouter()
	r.Use(worker.WorkerContext(coco))
	r.Mount("/", router.Default(map[string]http.HandlerFunc{}))

	testServer := httptest.NewServer(r)
	defer testServer.Close()

	httpClient = testServer.Client()
	serverURL = testServer.URL

	os.Exit(m.Run())
}

func prepareAppsearchClient() (index.Client, error) {
	index.UseTaxonSuggestion = config.UseTaxonSuggestion()

	opts := make([]index.Option, 0)
	for k, v := range config.IndexShardsTest() {
		opts = append(opts, index.WithShard(k, v))
	}

	return index.New("swiftype", opts...)
}

func prepareElasticsearchClient() (index.Client, error) {
	index.UseTaxonSuggestion = config.UseTaxonSuggestion()

	opts := make([]index.Option, 0)
	for k, v := range config.IndexESShardsTest() {
		opts = append(opts, index.WithShard(k, v))
	}

	return index.New("elasticsearch", opts...)
}

func preparePQClient() (index.Client, error) {
	return pq.New(), nil
}

func prepareRedis() (*redis.Client, error) {
	opts, err := redis.ParseURL(config.RedisDSNTest())
	if err != nil {
		return nil, err
	}

	client := redis.NewClient(opts)
	client.FlushAll()

	return client, nil
}

func prepareCoco() (*w.Worker, error) {
	coco := w.NewWorker("ssrv",
		w.WithWorkerCount(1),
	)

	if err := coco.Open(config.RedisDSNTest()); err != nil {
		return nil, err
	}

	return coco, nil
}

func preparePaperClip() (paperclip.Paperclip, error) {
	cfg := map[string]interface{}{
		"cdn":        "https://icdn.happyfresh.com",
		"secret_key": "5e60e14f757f10673bbeb70e0dcec19a",
		"styles": []string{
			"mini", "small", "product", "product_hq", "large", "wide",
		},
		"attachment": map[string]string{
			"name": "attachment",
			"path": "attachments",
		},
		"class": map[string]string{
			"name": "Spree::Image",
			"path": "spree/images",
		},
		"format": map[string]string{
			"hash_data": ":class/:attachment/:id/:style/:updated_at",
			"path":      "/t/:transform/:class/:attachment/:hash-:style.:extension",
		},
		"transform": map[string]string{
			"composition": "fit",
		},
	}

	return paperclip.New(cfg)
}

func newRequest(method, path string, header http.Header, params url.Values) (*http.Request, error) {
	return newRequestWithBody(method, path, header, params, nil)
}

func newRequestWithBody(method, path string, header http.Header, params url.Values, reader io.Reader) (*http.Request, error) {
	u := fmt.Sprintf("%s%s?%s", serverURL, path, params.Encode())

	r, err := http.NewRequest(method, u, reader)
	if err != nil {
		return nil, err
	}
	r.Header = header

	return r, nil
}
