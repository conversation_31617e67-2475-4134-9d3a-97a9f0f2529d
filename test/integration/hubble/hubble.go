package hubble

import (
	"bytes"
	"context"
	"encoding/csv"
	"io"
	"os"
	"strings"

	"google.golang.org/grpc"
	"happyfresh.io/hubble/lib/rpc"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/hubble"
)

//go:generate esc -o hubble.generated.go -pkg hubble  ../../../testdata/integration/hubble

// TODO: implement hubble with sprinkles DB
var (
	hubbleDataMock = map[int64]*rpc.TranslatedID{
		283956: {
			GlobalProductId:   283956,
			SpreeProductId:    284196,
			SpreeVariantId:    281208,
			SpreeBrandId:      27483,
			DeliveredQuantity: 10,
		},
		283957: {
			GlobalProductId:   283957,
			SpreeProductId:    284197,
			SpreeVariantId:    281209,
			SpreeBrandId:      27483,
			DeliveredQuantity: 11,
		},
		283948: {
			GlobalProductId:   283948,
			SpreeProductId:    284088,
			SpreeVariantId:    281101,
			SpreeBrandId:      13698,
			DeliveredQuantity: 12,
		},
		43548: {
			GlobalProductId:   43548,
			SpreeProductId:    43548,
			SpreeVariantId:    43522,
			SpreeBrandId:      4176,
			DeliveredQuantity: 13,
		},
		32193: {
			GlobalProductId:   32193,
			SpreeProductId:    32193,
			SpreeVariantId:    32175,
			SpreeBrandId:      12728,
			DeliveredQuantity: 29,
		},
		283077: {
			GlobalProductId: 283077,
			SpreeProductId:  283077,
			SpreeVariantId:  280102,
			SpreeBrandId:    -1,
		},
		283221: {
			GlobalProductId: 283221,
			SpreeProductId:  283221,
			SpreeVariantId:  280234,
			SpreeBrandId:    77,
		},
		283076: {
			GlobalProductId: 283076,
			SpreeProductId:  283076,
			SpreeVariantId:  280101,
			SpreeBrandId:    -1,
		},
		283214: {
			GlobalProductId: 283214,
			SpreeProductId:  283214,
			SpreeVariantId:  280233,
			SpreeBrandId:    -1,
		},
		283204: {
			GlobalProductId: 283204,
			SpreeProductId:  283204,
			SpreeVariantId:  280229,
			SpreeBrandId:    -1,
		},
		283082: {
			GlobalProductId: 283082,
			SpreeProductId:  283082,
			SpreeVariantId:  280107,
			SpreeBrandId:    -1,
		},
		283083: {
			GlobalProductId: 283083,
			SpreeProductId:  283083,
			SpreeVariantId:  280108,
			SpreeBrandId:    -1,
		},
		25094: {
			GlobalProductId: 25094,
			SpreeProductId:  25094,
			SpreeVariantId:  25076,
			SpreeBrandId:    -1,
		},
		27468: {
			GlobalProductId: 27468,
			SpreeProductId:  27468,
			SpreeVariantId:  27450,
			SpreeBrandId:    -1,
		},
		283990: {
			GlobalProductId: 283990,
			SpreeProductId:  284679,
			SpreeVariantId:  281690,
			SpreeBrandId:    -1,
		},
		283985: {
			GlobalProductId: 283985,
			SpreeProductId:  284577,
			SpreeVariantId:  281589,
			SpreeBrandId:    -1,
		},
		283088: {
			GlobalProductId: 283088,
			SpreeProductId:  283088,
			SpreeVariantId:  280113,
			SpreeBrandId:    -1,
		},
		283090: {
			GlobalProductId: 283090,
			SpreeProductId:  283090,
			SpreeVariantId:  280115,
			SpreeBrandId:    -1,
		},
		283089: {
			GlobalProductId: 283089,
			SpreeProductId:  283089,
			SpreeVariantId:  280114,
			SpreeBrandId:    -1,
		},
		283092: {
			GlobalProductId: 283092,
			SpreeProductId:  283092,
			SpreeVariantId:  280117,
			SpreeBrandId:    -1,
		},
	}

	countryDataMock = map[string]*rpc.Country{
		"id": {
			CountryId:            1,
			IsoName:              "ID",
			Name:                 "Indonesia",
			Currency:             "IDR",
			PreferencesNormalMoq: 50,
			PreferencesHcMoq:     50,
		},
	}
)

func New() (*hubble.Client, error) {
	err := prepareData()
	if err != nil {
		return nil, err
	}

	hubbleClient, err := hubble.New("", os.Getenv("SSRV_REDIS_TEST_DSN"), hubble.WithgRPCClient(&hubble.ClientMock{
		TranslateIDFunc: func(ctx context.Context, in *rpc.TranslateIdRequest, opts ...grpc.CallOption) (*rpc.TranslateIdResponse, error) {
			translatedIDs := make([]*rpc.TranslatedID, len(in.ProductIds))
			for i, productID := range in.ProductIds {
				translatedID, ok := hubbleDataMock[productID]
				if !ok {
					translatedID = &rpc.TranslatedID{
						GlobalProductId: -1,
						SpreeProductId:  -1,
						SpreeVariantId:  -1,
						SpreeBrandId:    -1,
					}
				}

				translatedIDs[i] = translatedID
			}

			return &rpc.TranslateIdResponse{TranslatedIds: translatedIDs}, nil
		},
		GetCountryFunc: func(ctx context.Context, in *rpc.GetCountryRequest, opts ...grpc.CallOption) (*rpc.Country, error) {
			return countryDataMock[strings.ToLower(in.IsoName)], err
		},
	}))
	if err != nil {
		return nil, err
	}

	return hubbleClient, nil
}

func prepareData() error {
	b, err := FSByte(false, "/testdata/integration/hubble/global-id.csv")
	if err != nil {
		return err
	}

	r := csv.NewReader(bytes.NewReader(b))
	r.Comma = '|'

	i := 0
	for {
		record, err := r.Read()
		if i == 0 {
			i++
			continue
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		sp := &rpc.TranslatedID{
			GlobalProductId: str.String(record[0]).Int64(),
			SpreeProductId:  str.String(record[1]).Int64(),
			SpreeVariantId:  str.String(record[2]).Int64(),
			SpreeBrandId:    str.String(record[3]).Int64(),
		}

		hubbleDataMock[sp.GlobalProductId] = sp

		i++
	}

	return nil
}
