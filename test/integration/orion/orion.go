package orion

import (
	"context"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes/wrappers"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"happyfresh.io/orion/lib/rpc"
	orionRPCLib "happyfresh.io/orion/lib/rpc/client"
	"happyfresh.io/search/config"
)

var (
	synonymData = map[string]string{
		"synonymberas": "beras",
	}

	productTypeData = map[string][]*rpc.ProductType{
		"beras": {
			{
				ProductType: 5432,
				Fraction:    3.0,
				Ids:         []int64{5432, 5431, 5426},
			},
		},
	}

	psnData = map[string][]*rpc.ProductBoost{
		"beras": {
			{
				ProductId: 30679,
				Fraction:  10.0,
				Sku:       "8997025140144-ID",
			},
			{
				ProductId: 24649,
				Fraction:  9.0,
				Sku:       "8885001727017-ID",
			},
		},
	}

	queryCorrectionData = map[string]string{
		"typobrs":  "beras",
		"typobrs2": "synonymberas",
		"typooreo": "oreo",
	}

	searchIntentionAttributes = map[string]*rpc.SearchIntentionResponse{
		"oreo": {
			ProcessedTerm:  "oreo",
			CollectionUsed: "search_intention_static",
			AttributeCombinations: []*rpc.AttributeCombination{
				{
					TaxonIds:       []int64{},
					ProductTypeIds: []int64{2820},
					BrandIds:       []int64{22015},
					Score:          1.0,
				},
				{
					TaxonIds:       []int64{},
					ProductTypeIds: []int64{},
					BrandIds:       []int64{16844},
					Score:          1.0,
				},
			},
		},
	}
)

func getSynonym(ctx context.Context, in *rpc.SynonymRequest, opts ...grpc.CallOption) (*rpc.SynonymResponse, error) {
	response := &rpc.SynonymResponse{
		Result: in.GetQuery(),
	}

	synonym, ok := synonymData[in.GetQuery()]
	if ok {
		response.ValidSynonym = true
		response.QueryTransformed = false
		response.Result = fmt.Sprintf(`"%s" "%s"`, synonym, in.Query)
		response.Terms = []string{in.Query, synonym}
	}

	productTypes, ok := productTypeData[in.GetQuery()]
	if !ok && synonym != "" {
		productTypes, ok = productTypeData[synonym]
		if !ok {
			response.ProductTypes = []*rpc.ProductType{}
		}
	}

	if ok {
		response.ProductTypes = productTypes
	}

	psn, ok := psnData[in.GetQuery()]
	if !ok && synonym != "" {
		psn, ok = psnData[synonym]
		if !ok {
			response.ProductBoosts = []*rpc.ProductBoost{}
		}
	}
	if ok {
		response.ProductBoosts = psn
	}

	return response, nil
}

func correctQuery(ctx context.Context, in *rpc.CorrectQueryRequest, opts ...grpc.CallOption) (*wrappers.StringValue, error) {
	correction := queryCorrectionData[in.Query]
	return &wrappers.StringValue{
		Value: correction,
	}, nil
}

func convertTermToSearchIntention(ctx context.Context, in *rpc.SearchIntentionRequest, opts ...grpc.CallOption) (*rpc.SearchIntentionResponse, error) {
	searchIntention, ok := searchIntentionAttributes[in.SearchTerm]
	if !ok {
		return nil, status.Error(codes.NotFound, "not found")
	}

	return searchIntention, nil
}

func New() *orionRPCLib.Orion {
	orionClient := &orionRPCLib.Mock{
		GetSynonymFunc:      getSynonym,
		CorrectQueryFunc:    correctQuery,
		SearchIntentionFunc: convertTermToSearchIntention,
	}

	return orionRPCLib.NewOrionClient("",
		orionRPCLib.WithSecureConnection(true),
		orionRPCLib.WithRpcClient(orionClient),
		orionRPCLib.WithCachedSearchIntention(config.RedisDSNTest(), 1*time.Millisecond),
		orionRPCLib.WithCachedGetSynonym(config.RedisDSNTest(), 1*time.Millisecond),
		orionRPCLib.WithCachedCorrectQuery(config.RedisDSNTest(), 1*time.Millisecond),
	)
}
