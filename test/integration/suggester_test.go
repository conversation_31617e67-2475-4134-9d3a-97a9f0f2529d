package integration_test

import (
	"context"
	"errors"

	"happyfresh.io/search/config"
	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/index/es/suggester"
)

var (
	integrationTestSuggesterShards index.SuggesterShards
	suggesterIntegrationTestMap    = map[string]index.Suggester{
		"id": nil,
	}
)

func prepareSuggesterShards() (index.SuggesterShards, error) {
	return suggesterShardsMock{}, nil
}

type suggesterShardsMock struct{}

func (ss suggesterShardsMock) Register(shardsURL map[string]string) error {
	return nil
}

func (ss suggesterShardsMock) Get(key string) (suggester index.Suggester, err error) {
	suggester, ok := suggesterMockMap[key]
	if !ok {
		suggester, ok = suggesterIntegrationTestMap[key]
		if ok {
			if integrationTestSuggesterShards == nil {
				integrationTestSuggesterShards = getIntegrationTestSuggesterShards(config.IndexSuggesterESShardsTest())
			}

			if suggester == nil {
				suggesterIntegrationTestMap[key], err = integrationTestSuggesterShards.Get(key)
			}

			return suggesterIntegrationTestMap[key], err
		}

		return nil, errors.New("Suggester mock not found")
	}

	return suggester, nil
}

var suggesterMockMap = map[string]index.Suggester{
	"ID": &suggesterMockID{},
	"MY": &suggesterMockMY{},
}

type suggesterMockID struct{}

func (sID *suggesterMockID) Suggest(ctx context.Context, options ...index.SuggestOption) (*index.SuggestResponse, error) {
	return &index.SuggestResponse{
		Taxons: []*index.IDNameSuggestion{
			{
				ID:   27,
				Name: "telur",
			},
			{
				ID:   24,
				Name: "Olahan Susu & Telur",
			},
			{
				ID:   133,
				Name: "Produk Olahan Susu dan Telur - Lainnya",
			},
		},
		Brands: []*index.IDNameSuggestion{
			{
				ID:   12728,
				Name: "Telur Prima",
			},
			{
				ID:   22539,
				Name: "Telur Emas",
			},
			{
				ID:   19263,
				Name: "Telur Ultra",
			},
		},
	}, nil
}

type suggesterMockMY struct{}

func (sID *suggesterMockMY) Suggest(ctx context.Context, options ...index.SuggestOption) (*index.SuggestResponse, error) {
	return &index.SuggestResponse{
		Taxons: []*index.IDNameSuggestion{
			{
				ID:   27,
				Name: "telur",
			},
		},
		Brands: []*index.IDNameSuggestion{
			{
				ID:   12728,
				Name: "Telur Prima",
			},
		},
	}, nil
}

func getIntegrationTestSuggesterShards(esShards map[string]string) index.SuggesterShards {
	suggesterShards, err := suggester.NewShards()
	if err != nil {
		return nil
	}

	err = suggesterShards.Register(esShards)
	if err != nil {
		return nil
	}

	return suggesterShards
}
