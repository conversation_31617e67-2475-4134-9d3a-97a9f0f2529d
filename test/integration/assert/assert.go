package assert

import (
	"context"
	"strings"
	"testing"
	"time"

	ext_assert "github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/core/pricing"
)

type Option func(*testing.T, gjson.Result)

func PricingOption(expected []*pricing.Product, clientType string) Option {
	return func(t *testing.T, js gjson.Result) {
		products := js.Get("products").Array()
		for i, exp := range expected {
			sku := products[i].Get("variants").Array()[0].Get("sku").String()
			inStock := products[i].Get("in_stock").Bool()
			price := products[i].Get("price").Float()
			normalPrice := products[i].Get("normal_price").Float()
			storeNormalPrice := products[i].Get("store_normal_price").Float()
			storePromoPrice := products[i].Get("store_promo_price").Float()
			costPrice := products[i].Get("cost_price").Float()
			normalCostPrice := products[i].Get("normal_cost_price").Float()

			ext_assert.Contains(t, sku, exp.Code)
			ext_assert.Equal(t, exp.InStock, inStock)
			ext_assert.Equal(t, exp.Price.PromoPrice, price)
			ext_assert.Equal(t, exp.Price.NormalPrice, normalPrice)
			ext_assert.Equal(t, exp.Price.StoreNormalPrice, storeNormalPrice)
			ext_assert.Equal(t, exp.Price.StorePromoPrice, storePromoPrice)
			ext_assert.Equal(t, exp.Price.StoreNormalPrice, normalCostPrice)
			ext_assert.Equal(t, exp.Price.StorePromoPrice, costPrice)
		}
	}
}

func NameAndDecriptionOption(expectedName, expectedDesc string) Option {
	return func(t *testing.T, js gjson.Result) {
		product := js.Get("products").Array()[0]
		name := product.Get("name").String()
		desc := product.Get("description").String()

		ext_assert.Equal(t, expectedName, name)
		ext_assert.Equal(t, expectedDesc, desc)
	}
}

func BrandOption(expect *rpc.Brand) Option {
	return func(t *testing.T, js gjson.Result) {
		brand := js.Get("products").Array()[0].Get("brand")
		brandID := brand.Get("id").Int()
		brandName := brand.Get("name").String()

		ext_assert.Equal(t, expect.Id, brandID)
		ext_assert.Equal(t, expect.Name, brandName)
	}
}

func CategoriesOption(expectedCategoryNames []string, expectedCategoryIDs []int64) Option {
	return func(t *testing.T, js gjson.Result) {
		product := js.Get("products").Array()[0]
		catJs := product.Get("categories").Array()
		categories := []string{}
		for _, c := range catJs {
			categories = append(categories, c.String())
		}

		catIDJs := product.Get("taxon_ids").Array()
		taxonIDs := []int64{}
		for _, t := range catIDJs {
			taxonIDs = append(taxonIDs, t.Int())
		}

		ext_assert.ElementsMatch(t, expectedCategoryNames, categories)
		ext_assert.ElementsMatch(t, expectedCategoryIDs, taxonIDs)
	}
}

func VendorOption(expect *rpc.Vendor) Option {
	return func(t *testing.T, js gjson.Result) {
		vendor := js.Get("products").Array()[0].Get("vendor")

		ext_assert.Equal(t, expect.Id, vendor.Get("id").Int())
		ext_assert.Equal(t, expect.Name, vendor.Get("name").String())
		ext_assert.Equal(t, expect.Description, vendor.Get("description").String())
		ext_assert.Equal(t, expect.ImageUrl, vendor.Get("image_url").String())
	}
}

func RelevancyOption(expect []int64) Option {
	return func(t *testing.T, js gjson.Result) {
		productsResult := js.Get("products").Array()

		ext_assert.GreaterOrEqual(t, len(expect), len(productsResult))

		resultProductIDs := []int64{}
		for i, p := range productsResult {
			if i >= 10 {
				break
			}

			resultProductIDs = append(resultProductIDs, p.Get("id").Int())
		}

		ext_assert.Equal(t, expect, resultProductIDs)
	}
}

func GlobalSearchRelevancyOption(expected map[int64][]int64) Option {
	return func(t *testing.T, js gjson.Result) {
		groupedProducts := map[int64][]int64{}
		productsResult := js.Get("products").Array()

		for _, productJs := range productsResult {
			stockLocationID := productJs.Get("stock_location_id").Int()
			_, ok := groupedProducts[stockLocationID]
			if !ok {
				groupedProducts[stockLocationID] = []int64{}
			}

			groupedProducts[stockLocationID] = append(groupedProducts[stockLocationID], productJs.Get("id").Int())
		}

		ext_assert.Equal(t, len(expected), len(groupedProducts))
		for stockLocationID, products := range expected {
			result, ok := groupedProducts[stockLocationID]
			ext_assert.True(t, ok)
			ext_assert.Equal(t, products, result)
		}
	}
}

func GlobalSearchVariantOption(expected []int64) Option {
	return func(t *testing.T, js gjson.Result) {
		stockLocationIDs := map[int64]bool{}
		productsResult := js.Get("products").Array()

		for _, productJs := range productsResult {
			stockLocationID := productJs.Get("stock_location_id").Int()
			stockLocationIDs[stockLocationID] = true
		}

		ext_assert.Equal(t, len(expected), len(stockLocationIDs))
		for _, stockLocationID := range expected {
			exist := stockLocationIDs[stockLocationID]
			ext_assert.True(t, exist)
		}
	}
}

func SearchjoyNotTrackedOption() Option {
	return func(t *testing.T, js gjson.Result) {
		searchID := js.Get("search_id").Int()
		ext_assert.Equal(t, int64(0), searchID)
	}
}

func SearchjoyOption(expect *datastore.SearchjoyRecord) Option {
	return func(t *testing.T, js gjson.Result) {
		time.Sleep(1 * time.Second)
		searchID := js.Get("search_id").Int()
		searchjoyRecord, err := datastore.FindSearchjoyByID(context.Background(), searchID)
		if err != nil {
			t.Error(err)
		}

		ext_assert.Equal(t, expect.Query, searchjoyRecord.Query)
		ext_assert.Equal(t, expect.NormalizedQuery, searchjoyRecord.NormalizedQuery)
		ext_assert.Equal(t, expect.CountryIso, searchjoyRecord.CountryIso)
		ext_assert.Equal(t, expect.Locale, searchjoyRecord.Locale)
		ext_assert.Equal(t, expect.StockLocationId, searchjoyRecord.StockLocationId)
		ext_assert.Equal(t, expect.UserId, searchjoyRecord.UserId)
		ext_assert.Equal(t, expect.CountryIso, searchjoyRecord.CountryIso)
		for k, v := range expect.Properties {
			if k == "product_type_id" {
				expectedIDs := strings.Split(v, ",")
				resultIDs := strings.Split(searchjoyRecord.Properties[k], ",")
				ext_assert.ElementsMatch(t, expectedIDs, resultIDs)

				continue
			}
			if k == "product_type_leaves" {
				expectedIDs := strings.Split(v, ",")
				resultIDs := strings.Split(searchjoyRecord.Properties[k], ",")
				ext_assert.ElementsMatch(t, expectedIDs, resultIDs)

				continue
			}
			if k == "user_history_product" {
				expectedIDs := strings.Split(v, ",")
				resultIDs := strings.Split(searchjoyRecord.Properties[k], ",")
				ext_assert.ElementsMatch(t, expectedIDs, resultIDs)

				continue
			}

			ext_assert.Equalf(t, v, searchjoyRecord.Properties[k], "Comparing %s", k)
		}
	}
}

func QueryCorrectionOption(expect map[string]string) Option {
	return func(t *testing.T, js gjson.Result) {
		query := js.Get("query")
		before := query.Get("before").String()
		after := query.Get("after").String()

		ext_assert.Equal(t, expect["before"], before)
		ext_assert.Equal(t, expect["after"], after)
	}
}

func TaxonFilterOrderOption(expectedFirstN []int64) Option {
	return func(t *testing.T, js gjson.Result) {
		tjs := js.Get("filters").Get("taxon_ids").Array()
		taxonIDs := []int64{}
		for _, t := range tjs {
			taxonIDs = append(taxonIDs, t.Int())
		}

		ext_assert.Equal(t, expectedFirstN, taxonIDs[:len(expectedFirstN)])
	}
}

func TaxonFilterOption(expect []int64) Option {
	return func(t *testing.T, js gjson.Result) {
		tjs := js.Get("filters").Get("taxon_ids").Array()
		taxonIDs := []int64{}
		for _, t := range tjs {
			taxonIDs = append(taxonIDs, t.Int())
		}

		if len(expect) == 0 {
			ext_assert.Equal(t, 0, len(tjs))
		}
		ext_assert.Subset(t, taxonIDs, expect)
	}
}

func TaxonNoneFilter(expectedNone []int64) Option {
	return func(t *testing.T, js gjson.Result) {
		tjs := js.Get("filters").Get("taxon_ids").Array()
		taxonIDs := []int64{}
		for _, t := range tjs {
			taxonIDs = append(taxonIDs, t.Int())
		}

		for _, e := range expectedNone {
			ext_assert.NotContains(t, taxonIDs, e)
		}
	}
}

func BrandFilterOption(expect []map[string]interface{}) Option {
	return func(t *testing.T, js gjson.Result) {
		bjs := js.Get("filters").Get("brands").Array()
		brandIDs := []int64{}
		brandNames := []string{}
		for _, b := range bjs {
			brandID := b.Get("id").Int()
			brandName := strings.ToLower(b.Get("name").String())

			brandIDs = append(brandIDs, brandID)
			brandNames = append(brandNames, brandName)
		}

		if len(expect) == 0 {
			ext_assert.Equal(t, 0, len(bjs))
		}
		for _, e := range expect {
			ext_assert.Contains(t, brandIDs, str.MaybeString(e["id"]).Int64())
			ext_assert.Contains(t, brandNames, str.MaybeString(e["name"]).String())
		}
	}
}

func KeywordsOption(expect []string) Option {
	return func(t *testing.T, js gjson.Result) {
		keywords := js.Get("keywords").Array()
		ext_assert.GreaterOrEqual(t, len(expect), len(keywords))

		resultKeywords := []string{}
		for i, keyword := range keywords {
			if i >= 5 {
				break
			}

			resultKeywords = append(resultKeywords, keyword.String())
		}

		ext_assert.Equal(t, expect, resultKeywords)
	}
}
