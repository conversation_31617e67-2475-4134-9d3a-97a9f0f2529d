package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	ext_assert "github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"happyfresh.io/search/test/integration/assert"
)

func TestThemesList(t *testing.T) {
	if testing.Short() {
		t.Ski<PERSON>ow()
	}

	convey.Convey("Themes list response", t, func() {
		searchUrl := "/api/v3/stock_locations/3/themes"

		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("Locale", "ID")
		header.Set("Country", "ID")

		params := url.Values{}

		req, err := newRequest(http.MethodGet, searchUrl, header, params)
		if err != nil {
			t.Error(err)
		}

		resp, err := httpClient.Do(req)
		if err != nil {
			t.Error(err)
		}

		convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusOK)

		buf := &bytes.Buffer{}
		_, err = buf.ReadFrom(resp.Body)
		defer resp.Body.Close()
		if err != nil {
			t.Error(err)
		}

		json := gjson.ParseBytes(buf.Bytes())
		themes := json.Get("themes").Array()

		convey.So(themes[0].Get("theme_id").Int(), convey.ShouldEqual, 2)
		convey.So(themes[0].Get("title").String(), convey.ShouldEqual, "Theme 2")
		convey.So(themes[0].Get("position").Int(), convey.ShouldEqual, 1)
		convey.So(themes[0].Get("translated_title").String(), convey.ShouldEqual, "Theme 2 ID")
		convey.So(themes[0].Get("image_url").String(), convey.ShouldEqual, "/theme/display_images/5d557ced83842f5ac226100966897975600958e0-mini.jpg")

		convey.So(themes[1].Get("theme_id").Int(), convey.ShouldEqual, 1)
		convey.So(themes[1].Get("title").String(), convey.ShouldEqual, "Theme 1")
		convey.So(themes[1].Get("position").Int(), convey.ShouldEqual, 2)
		convey.So(themes[1].Get("translated_title").String(), convey.ShouldEqual, "Theme 1 ID")
		convey.So(themes[1].Get("image_url").String(), convey.ShouldEqual, "/theme/display_images/5d557ced83842f5ac226100966897975600958e0-mini.jpg")
	})
}

func TestBrowseTheme(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	for _, tc := range []struct {
		name         string
		themeId      int64
		header       map[string]string
		param        map[string][]string
		assertOption []assert.Option
	}{
		{
			"Relevant with given theme IDs",
			1,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"page":     {"1"},
				"per_page": {"10"},
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{284197, 284196, 284088}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
			},
		},
		{
			"Relevant with given theme IDs, paginated",
			1,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"page":     {"2"},
				"per_page": {"2"},
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{284088}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := "/api/v3/stock_locations/3/themes/1"

			header := http.Header{}
			header.Set("Content-Type", "application/x-www-form-urlencoded")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param[k] = v
			}

			var req *http.Request
			var err error
			req, err = newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			ext_assert.Equal(t, http.StatusOK, resp.StatusCode)

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}
