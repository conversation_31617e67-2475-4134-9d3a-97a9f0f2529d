package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"testing"

	"github.com/tidwall/gjson"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/test/integration/assert"
)

func TestGlobalSearch(t *testing.T) {
	// Currently skipped because of inconsistent tf-idf result
	t.Ski<PERSON>ow()

	searchUrl := "/api/v3/products/search"

	for _, tc := range []struct {
		name         string
		header       map[string]string
		param        map[string][]string
		assertOption []assert.Option
	}{
		{
			"Relevant with grouped products",
			map[string]string{
				"Locale":              "en",
				"X-Country-Code":      "ID",
				"x-happy-client-type": "android",
			},
			map[string][]string{
				"q":     {"beras"},
				"sli[]": {"402", "163"},
			},
			[]assert.Option{
				assert.GlobalSearchRelevancyOption(map[int64][]int64{
					402: {150449, 203224, 251462, 24650, 24699},
					163: {150449, 17157, 19991, 237061, 98428},
				}),
				assert.NameAndDecriptionOption("Hoki <PERSON> Rice", "Qualified White Rice From Selected Rice Plant"),
				assert.BrandOption(&rpc.Brand{Id: 23077, Name: "hoki"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods", "Grains, Rice & Dried Goods"}, []int64{34, 44}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with grouped products, localized",
			map[string]string{
				"Locale":              "id",
				"X-Country-Code":      "ID",
				"x-happy-client-type": "android",
			},
			map[string][]string{
				"q":     {"beras"},
				"sli[]": {"402", "163"},
			},
			[]assert.Option{
				assert.GlobalSearchRelevancyOption(map[int64][]int64{
					402: {150449, 203224, 251462, 24650, 24699},
					163: {150449, 17157, 19991, 237061, 98428},
				}),
				assert.NameAndDecriptionOption("Hoki White Rice-Localized", "Qualified White Rice From Selected Rice Plant-Localized"),
				assert.BrandOption(&rpc.Brand{Id: 23077, Name: "hoki-Localized"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods-Localized", "Grains, Rice & Dried Goods-Localized"}, []int64{34, 44}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.SearchjoyNotTrackedOption(),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param[k] = v
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}

func TestGlobalSearchVariant(t *testing.T) {
	// Currently skipped. Need to complete store products and other complementary testing data
	t.SkipNow()

	searchUrl := "/api/v3/products/search"

	for _, tc := range []struct {
		name         string
		header       map[string]string
		param        map[string][]string
		assertOption []assert.Option
	}{
		{
			"With default global_search_variant",
			map[string]string{
				"Locale":              "en",
				"X-Country-Code":      "ID",
				"x-happy-client-type": "android",
			},
			map[string][]string{
				"q":     {"chicken"},
				"sli[]": {"3", "5", "46", "51", "56", "58", "105", "108", "114", "117", "118", "121", "163", "119", "217", "308", "340", "341"},
			},
			[]assert.Option{
				assert.GlobalSearchVariantOption([]int64{3, 5, 46, 51, 56, 58, 105, 108, 114, 117}),
			},
		},
		{
			"With global_search_variant B",
			map[string]string{
				"Locale":              "en",
				"X-Country-Code":      "ID",
				"x-happy-client-type": "android",
			},
			map[string][]string{
				"q":                     {"chicken"},
				"sli[]":                 {"3", "5", "46", "51", "56", "58", "105", "108", "114", "117", "118", "121", "163", "119", "217", "308", "340", "341"},
				"global_search_variant": {"B"},
			},
			[]assert.Option{
				assert.GlobalSearchVariantOption([]int64{3, 5, 46, 51, 56, 58, 105, 108, 114, 117, 118, 121, 163, 119, 217}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param[k] = v
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}

func TestGlobalBrowse(t *testing.T) {
	// Currently skipped. Need to complete store products and other complementary testing data
	t.SkipNow()

	searchUrl := "/api/v3/products/browse"

	for _, tc := range []struct {
		name         string
		header       map[string]string
		param        map[string][]string
		assertOption []assert.Option
	}{
		{
			"Relevant with grouped products",
			map[string]string{
				"Locale":              "en",
				"X-Country-Code":      "ID",
				"x-happy-client-type": "android",
			},
			map[string][]string{
				"taxon_id": {"34"},
				"sli[]":    {"402"},
			},
			[]assert.Option{
				assert.NameAndDecriptionOption("Save L Corn Vermicelli", "Instant Corn Vermicelli"),
				assert.BrandOption(&rpc.Brand{Id: 14891, Name: "save l"}),
				assert.CategoriesOption([]string{"Instant Foods.", "Choice L Save", "Dry & Canned Goods", "Private Brand"}, []int64{43, 459, 34, 433}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param[k] = v
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}
