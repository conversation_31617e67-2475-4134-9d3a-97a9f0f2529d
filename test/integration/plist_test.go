package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"strconv"
	"testing"

	ext_assert "github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"happyfresh.io/search/test/integration/assert"
)

func TestVendorProductList(t *testing.T) {
	if testing.Short() {
		t.<PERSON><PERSON>ow()
	}

	for _, tc := range []struct {
		name         string
		header       map[string]string
		param        map[string]string
		assertOption []assert.Option
	}{
		{
			"Relevant with vendor ID",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":  "10",
				"q[s]":      "popularity desc",
				"vendor_id": "9999",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{241124, 30679, 19604}),
				assert.TaxonFilterOption([]int64{55, 44, 215}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 21806, "name": "hotel-en"},
					{"id": 7941, "name": "brand name of id 7941-en"},
				}),
				assert.NameAndDecriptionOption("Rice Cake Nodles", "Rice Cake Noodles with Shrimp Petis Spices and Shrimp Topping"),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with vendor ID, localized",
			map[string]string{
				"Locale":              "id",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":  "10",
				"q[s]":      "popularity desc",
				"vendor_id": "9999",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{241124, 30679, 19604}),
				assert.TaxonFilterOption([]int64{55, 44, 215}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 21806, "name": "hotel-id"},
					{"id": 7941, "name": "brand name of id 7941-id"},
				}),
				assert.NameAndDecriptionOption("Rice Cake Nodles-Localized", "Rice Cake Noodles with Shrimp Petis Spices and Shrimp Topping-Localized"),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with vendor ID, paginated",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"page":      "2",
				"per_page":  "2",
				"q[s]":      "popularity desc",
				"vendor_id": "9999",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{19604}),
				assert.TaxonFilterOption([]int64{55, 44, 215}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 21806, "name": "hotel-en"},
					{"id": 7941, "name": "brand name of id 7941-en"},
				}),
				assert.NameAndDecriptionOption("Tropicana Slim Lychee Zero Sugar Syrup", "Lychee Zero Sugar Syrup"),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with vendor ID, with brand filter",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":   "10",
				"q[s]":       "popularity desc",
				"vendor_id":  "9999",
				"brand_id[]": "21806",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679}),
				assert.TaxonFilterOption([]int64{44}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 21806, "name": "hotel-en"},
				}),
				assert.NameAndDecriptionOption("Hotel Organic Black Rice", "Organic Black Rice"),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with vendor ID, with taxon filter",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":   "10",
				"q[s]":       "popularity desc",
				"vendor_id":  "9999",
				"taxon_id[]": "215",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{241124}),
				assert.TaxonFilterOption([]int64{215}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("Rice Cake Nodles", "Rice Cake Noodles with Shrimp Petis Spices and Shrimp Topping"),
				assert.SearchjoyNotTrackedOption(),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := "/api/stock_locations/402/products"

			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			param.Set("category", "vendor")
			for k, v := range tc.param {
				param.Set(k, v)
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			ext_assert.Equal(t, http.StatusOK, resp.StatusCode)

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}

func TestBrandProductList(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	for _, tc := range []struct {
		name       string
		brandParam string
		returnCode int
	}{
		{
			"Returns filtered product list by brand",
			"21806",
			http.StatusOK,
		},
		{
			"Returns no products and bad request status",
			"",
			http.StatusBadRequest,
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := "/api/stock_locations/402/products/"

			header := http.Header{}
			header.Set("Content-Type", "application/json")
			header.Set("X-Happy-Client-Type", "android")
			header.Set("Synthetic", "true")
			header.Set("Locale", "EN")
			header.Set("Country", "ID")

			params := url.Values{}
			params.Set("page", "1")
			params.Set("per_page", "1")
			params.Set("category", "brand")
			params.Set("brand_id[]", tc.brandParam)

			req, err := newRequest(http.MethodGet, searchUrl, header, params)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			if resp.StatusCode != tc.returnCode {
				t.Errorf("expectation: %v; reality: %v\n", tc.returnCode, resp.StatusCode)
			}

			if tc.returnCode != http.StatusOK {
				return
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			brandParam, err := strconv.ParseInt(tc.brandParam, 10, 64)
			if err != nil {
				t.Error(err)
			}

			json := gjson.ParseBytes(buf.Bytes())
			products := json.Get("products").Array()
			if productsLen := len(products); productsLen == 0 {
				t.Errorf("expectation: %v; reality: %v\n", "> 0", productsLen)
			}

			brandFilters := json.Get("filters").Get("brands").Array()
			if brandResult := brandFilters[0].Get("id").Int(); brandResult != brandParam {
				t.Errorf("expectation: %v; reality: %v\n", brandParam, brandResult)
			}
		})
	}
}
