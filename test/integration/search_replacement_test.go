package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"strings"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	ext_assert "github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/test/integration/assert"
)

func TestReplacementSearch(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	searchUrl := "/api/stock_locations/402/products/replacements/search"

	for _, tc := range []struct {
		name         string
		header       map[string]string
		param        map[string]string
		assertOption []assert.Option
	}{
		{
			"Relevant with product type, personalization, and raw popularity boosting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "beras",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.SearchjoyNotTrackedOption(),
				assert.NameAndDecriptionOption("Hotel Organic Black Rice", "Organic Black Rice"),
				assert.BrandOption(&rpc.Brand{Id: 21806, Name: "hotel"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods", "Grains, Rice & Dried Goods"}, []int64{34, 44}),
				assert.VendorOption(&rpc.Vendor{Id: 9999, Name: "LotteVendor", Description: "LotteVendor Description", ImageUrl: "http://url/LotteVendor.jpg"}),
			},
		},
		{
			"Relevant with product type, personalization, raw popularity boosting and localized",
			map[string]string{
				"Locale":              "ID",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "beras",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.SearchjoyNotTrackedOption(),
				assert.NameAndDecriptionOption("Hotel Organic Black Rice-Localized", "Organic Black Rice-Localized"),
				assert.BrandOption(&rpc.Brand{Id: 21806, Name: "hotel-Localized"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods-Localized", "Grains, Rice & Dried Goods-Localized"}, []int64{34, 44}),
				assert.VendorOption(&rpc.Vendor{Id: 9999, Name: "LotteVendor", Description: "LotteVendor Description-Localized", ImageUrl: "http://url/LotteVendor.jpg"}),
			},
		},
		{
			"Relevant with synonym, product type, personalization, and raw popularity boosting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "synonymberas",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with correction, product type, personalization, and raw popularity boosting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "typobrs",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param.Set(k, v)
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			ext_assert.Equal(t, http.StatusOK, resp.StatusCode)

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}

func TestReplacementSearchGlobalID(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	createVariantIDsParam := func(variantIDs []string) url.Values {
		params := url.Values{}
		params.Set("page", "1")
		params.Set("per_page", "10")
		params.Set("show_oos", "true")
		params.Set("q", "")
		params.Set("q[s]", "product_id")
		for _, v := range variantIDs {
			params.Add("filter[variant_id][]", v)
		}

		return params
	}

	testCases := []struct {
		name                string
		inputTaxon          int64
		inputVariantIDs     []string
		wantProductID       []int64
		wantProductGlobalID []int64
		wantVariantID       []int64
		wantVariantGlobalID []int64
		wantBrandID         []int64
		wantBrandGlobalID   []int64
		wantImageID         []int64
		wantImageOriginal   []string
	}{
		{
			"Return all legacy ID",
			1049,
			[]string{"281208", "281209"},
			[]int64{284196, 284197},
			[]int64{283956, 283957},
			[]int64{281208, 281209},
			[]int64{280969, 280970},
			[]int64{27483, 27483},
			[]int64{27260, 27260},
			[]int64{},
			[]string{},
		},
		{
			"Image is OK on different IDs",
			1029,
			[]string{"281101"},
			[]int64{284088},
			[]int64{283948},
			[]int64{281101},
			[]int64{280961},
			[]int64{13698},
			[]int64{13698},
			[]int64{1655299, 1655303},
			[]string{
				"a18e3c56acb00a1cc277af7e61dd86e033ff4b41-original.jpg",
				"1bc891cc77ba420cf68bb73bbde0a2af19221e06-original.jpeg",
			},
		},
		{
			"Image is OK",
			55,
			[]string{"43522"},
			[]int64{43548},
			[]int64{43548},
			[]int64{43522},
			[]int64{43522},
			[]int64{4176},
			[]int64{4176},
			[]int64{280733, 280734},
			[]string{
				"5d37c850ce62a77a8cb89c4e7081bfde40432759-original.jpg",
				"845ec6dfda9f7cc434b92b1d0f87988a5dfcc8f4-original.jpg",
			},
		},
	}

	header := http.Header{}
	header.Set("Content-Type", "application/json")
	header.Set("X-Happy-Client-Type", "android")
	header.Set("Synthetic", "true")
	header.Set("Locale", "EN")
	header.Set("Country", "ID")

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := "/api/stock_locations/3/products/replacements/search"
			params := createVariantIDsParam(tc.inputVariantIDs)
			req, err := newRequest(http.MethodGet, searchUrl, header, params)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			json := gjson.ParseBytes(buf.Bytes())
			products := json.Get("products").Array()
			productIDs := make([]int64, len(products))
			productGlobalIDs := make([]int64, len(products))
			variantIDs := make([]int64, len(products))
			variantGlobalIDs := make([]int64, len(products))
			brandIDs := make([]int64, len(products))
			brandGlobalIDs := make([]int64, len(products))
			imageIDs := []int64{}
			imageOriginals := []string{}
			for i, p := range products {
				productIDs[i] = p.Get("id").Int()
				productGlobalIDs[i] = p.Get("product_global_id").Int()
				variantIDs[i] = p.Get("variant_id").Int()
				variantGlobalIDs[i] = p.Get("variant_global_id").Int()
				brandIDs[i] = p.Get("brand.id").Int()
				brandGlobalIDs[i] = p.Get("brand_global_id").Int()
				images := p.Get("variants").Array()[0].Get("images").Array()
				for _, image := range images {
					arr := strings.Split(image.Get("original_url").String(), "/attachments/")
					imageIDs = append(imageIDs, image.Get("id").Int())
					imageOriginals = append(imageOriginals, arr[1])
				}
			}
			assert := convey.ShouldEqual(resp.StatusCode, http.StatusOK)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(productIDs, tc.wantProductID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(productGlobalIDs, tc.wantProductGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(variantIDs, tc.wantVariantID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(variantGlobalIDs, tc.wantVariantGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(brandIDs, tc.wantBrandID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(brandGlobalIDs, tc.wantBrandGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(imageIDs, tc.wantImageID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(imageOriginals, tc.wantImageOriginal)
			if assert != "" {
				t.Error(assert)
			}
		})
	}

}
