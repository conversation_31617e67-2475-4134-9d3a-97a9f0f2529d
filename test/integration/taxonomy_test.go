package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"github.com/tidwall/gjson"
)

func TestTaxonomyListResponse(t *testing.T) {
	if testing.Short() {
		t.<PERSON><PERSON><PERSON>()
	}

	convey.Convey("Taxonomy list response", t, func() {
		searchUrl := "/api/taxonomies/1/taxons"
		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("Locale", "EN")
		header.Set("Country", "ID")

		params := url.Values{}
		params.Set("stock_location_id", "3")

		req, err := newRequest(http.MethodGet, searchUrl, header, params)
		if err != nil {
			t.Error(err)
		}

		resp, err := httpClient.Do(req)
		if err != nil {
			t.Error(err)
		}

		convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusOK)

		buf := &bytes.Buffer{}
		_, err = buf.ReadFrom(resp.Body)
		defer resp.Body.Close()
		if err != nil {
			t.Error(err)
		}

		json := gjson.ParseBytes(buf.Bytes())
		taxons := json.Get("taxons").Array()
		convey.So(taxons[0].Get("id").Int(), convey.ShouldEqual, 96)
		convey.So(taxons[0].Get("parent_id").Int(), convey.ShouldEqual, 1)
		convey.So(taxons[0].Get("name").String(), convey.ShouldEqual, "Household")
		convey.So(taxons[0].Get("description").String(), convey.ShouldEqual, "household description")
		convey.So(taxons[0].Get("permalink").String(), convey.ShouldEqual, "household")
		convey.So(taxons[0].Get("taxonomy_id").Int(), convey.ShouldEqual, 1)
		convey.So(taxons[0].Get("meta_description").String(), convey.ShouldEqual, "household meta description")
		convey.So(taxons[0].Get("meta_keywords").String(), convey.ShouldEqual, "household meta keywords")
		convey.So(taxons[0].Get("meta_title").String(), convey.ShouldEqual, "household meta title")
		convey.So(taxons[0].Get("position").Int(), convey.ShouldEqual, 330)
		convey.So(taxons[0].Get("sorting_unit_price").Bool(), convey.ShouldEqual, false)
		convey.So(taxons[0].Get("slug").String(), convey.ShouldEqual, "household-96")
		convey.So(taxons[0].Get("products_count").Int(), convey.ShouldEqual, 1)
		convey.So(taxons[0].Get("icon_url").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg")
		convey.So(taxons[0].Get("display_image").Get("id").Int(), convey.ShouldEqual, 1616998)
		convey.So(taxons[0].Get("display_image").Get("mini").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg")
		convey.So(taxons[0].Get("display_image").Get("normal").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg")

		convey.So(taxons[1].Get("id").Int(), convey.ShouldEqual, 48)
		convey.So(taxons[1].Get("parent_id").Int(), convey.ShouldEqual, 1)
		convey.So(taxons[1].Get("name").String(), convey.ShouldEqual, "Pantry")
		convey.So(taxons[1].Get("description").String(), convey.ShouldEqual, "pantry description")
		convey.So(taxons[1].Get("permalink").String(), convey.ShouldEqual, "pantry")
		convey.So(taxons[1].Get("taxonomy_id").Int(), convey.ShouldEqual, 1)
		convey.So(taxons[1].Get("meta_description").String(), convey.ShouldEqual, "pantry meta description")
		convey.So(taxons[1].Get("meta_keywords").String(), convey.ShouldEqual, "pantry meta keywords")
		convey.So(taxons[1].Get("meta_title").String(), convey.ShouldEqual, "pantry meta title")
		convey.So(taxons[1].Get("position").Int(), convey.ShouldEqual, 300)
		convey.So(taxons[1].Get("sorting_unit_price").Bool(), convey.ShouldEqual, false)
		convey.So(taxons[1].Get("slug").String(), convey.ShouldEqual, "pantry-48")
		convey.So(taxons[1].Get("products_count").Int(), convey.ShouldEqual, 0)
		convey.So(taxons[1].Get("icon_url").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg")
		convey.So(taxons[1].Get("display_image").Get("id").Int(), convey.ShouldEqual, 84806)
		convey.So(taxons[1].Get("display_image").Get("mini").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg")
		convey.So(taxons[1].Get("display_image").Get("normal").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg")

		convey.So(taxons[1].Get("taxons").Array()[0].Get("id").Int(), convey.ShouldEqual, 111)
		convey.So(taxons[1].Get("taxons").Array()[0].Get("parent_id").Int(), convey.ShouldEqual, 48)
		convey.So(taxons[1].Get("taxons").Array()[0].Get("name").String(), convey.ShouldEqual, "Oils & Vinegars")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("description").String(), convey.ShouldEqual, "oils & vinegars description")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("permalink").String(), convey.ShouldEqual, "pantry/oils-and-vinegars")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("taxonomy_id").Int(), convey.ShouldEqual, 1)
		convey.So(taxons[1].Get("taxons").Array()[0].Get("meta_description").String(), convey.ShouldEqual, "oils & vinegars meta description")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("meta_keywords").String(), convey.ShouldEqual, "oils & vinegars meta keywords")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("meta_title").String(), convey.ShouldEqual, "oils & vinegars meta title")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("position").Int(), convey.ShouldEqual, 311)
		convey.So(taxons[1].Get("taxons").Array()[0].Get("sorting_unit_price").Bool(), convey.ShouldEqual, false)
		convey.So(taxons[1].Get("taxons").Array()[0].Get("slug").String(), convey.ShouldEqual, "oils-vinegars-111")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("products_count").Int(), convey.ShouldEqual, 1)
		convey.So(taxons[1].Get("taxons").Array()[0].Get("icon_url").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("display_image").Get("id").Int(), convey.ShouldEqual, 84806)
		convey.So(taxons[1].Get("taxons").Array()[0].Get("display_image").Get("mini").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg")
		convey.So(taxons[1].Get("taxons").Array()[0].Get("display_image").Get("normal").String(), convey.ShouldEqual, "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg")

	})
}
