package pricing

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"time"

	"github.com/sony/gobreaker"
	calculatorRPC "happyfresh.io/calculator/lib/rpc"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/pricing"
	"happyfresh.io/search/core/pricing/aloha"
	"happyfresh.io/search/lib/strs"
)

//go:generate esc -o pricing.generated.go -pkg pricing  ../../../testdata/integration/pricing

var (
	priceDataMock = map[string]*pricing.Product{
		"8690558018057-1101": {
			Code:    "8690558018057",
			StoreID: 1101,
			InStock: true,
			Price: &pricing.Price{
				PromoPrice: 44000,
				ClientTypePromoPrice: map[string]float64{
					"android": 44000,
				},
				Quantity:          5,
				ThresholdQuantity: 3,
			},
			Promotions:     []*pricing.Promotion{},
			VestaInStock:   true,
			VestaUpdatedAt: "",
		},
		"093348311853-1101": {
			Code:    "093348311853",
			StoreID: 1101,
			InStock: true,
			Price: &pricing.Price{
				PromoPrice:       38400,
				StoreNormalPrice: 35500,
				StorePromoPrice:  35500,
				ClientTypePromoPrice: map[string]float64{
					"android": 38400,
				},
			},
			Promotions:     []*pricing.Promotion{},
			VestaInStock:   false,
			VestaUpdatedAt: "",
		},
		"123456-1101": {
			Code:    "123456",
			StoreID: 1101,
			InStock: true,
			Price: &pricing.Price{
				PromoPrice:       900000,
				StoreNormalPrice: 900000,
				StorePromoPrice:  900000,
				ClientTypePromoPrice: map[string]float64{
					"android": 900000,
				},
				Quantity:          5,
				ThresholdQuantity: 3,
			},
			Promotions:     []*pricing.Promotion{},
			VestaInStock:   false,
			VestaUpdatedAt: "",
		},
		"90013-9992": {
			Code:    "90013",
			StoreID: 9992,
			InStock: true,
			Price: &pricing.Price{
				PromoPrice:       900000,
				StoreNormalPrice: 900000,
				StorePromoPrice:  900000,
				ClientTypePromoPrice: map[string]float64{
					"android": 900000,
				},
				Quantity:          5,
				ThresholdQuantity: 3,
			},
			Promotions:     []*pricing.Promotion{},
			VestaInStock:   false,
			VestaUpdatedAt: "",
		},
		"90014-9992": {
			Code:    "90014",
			StoreID: 9992,
			InStock: true,
			Price: &pricing.Price{
				PromoPrice:       900000,
				StoreNormalPrice: 900000,
				StorePromoPrice:  900000,
				ClientTypePromoPrice: map[string]float64{
					"android": 900000,
				},
				Quantity:          5,
				ThresholdQuantity: 3,
			},
			Promotions:     []*pricing.Promotion{},
			VestaInStock:   false,
			VestaUpdatedAt: "",
		},
		"90015-9992": {
			Code:    "90015",
			StoreID: 9992,
			InStock: true,
			Price: &pricing.Price{
				PromoPrice:       900000,
				StoreNormalPrice: 900000,
				StorePromoPrice:  900000,
				ClientTypePromoPrice: map[string]float64{
					"android": 900000,
				},
				Quantity:          5,
				ThresholdQuantity: 3,
			},
			Promotions:     []*pricing.Promotion{},
			VestaInStock:   false,
			VestaUpdatedAt: "",
		},
		"90017-9992": {
			Code:    "90017",
			StoreID: 9992,
			InStock: true,
			Price: &pricing.Price{
				PromoPrice:       900000,
				StoreNormalPrice: 900000,
				StorePromoPrice:  900000,
				ClientTypePromoPrice: map[string]float64{
					"android": 900000,
				},
				Quantity:          5,
				ThresholdQuantity: 3,
			},
			Promotions:     []*pricing.Promotion{},
			VestaInStock:   false,
			VestaUpdatedAt: "",
		},
	}
)

func ProductPriceGetterMock(ctx context.Context, option *pricing.Option) (map[string]*pricing.Product, error) {
	products := make(map[string]*pricing.Product)
	for _, item := range option.Items {
		productCode := strs.SKU(item.SKU).ToProductCode(option.CountryCode)
		storeID := int64(item.StoreID)

		price, ok := priceDataMock[fmt.Sprintf("%s-%d", productCode, storeID)]
		if !ok {
			price = &pricing.Product{
				Code:    productCode,
				StoreID: int(storeID),
				InStock: true,
				Price: &pricing.Price{
					PromoPrice:       990000,
					StoreNormalPrice: 990000,
					StorePromoPrice:  990000,
					ClientTypePromoPrice: map[string]float64{
						"android":   990000,
						"ios":       990000,
						"webapp":    990000,
						"mobileweb": 990000,
					},
					Quantity:          5,
					ThresholdQuantity: 3,
				},
				Promotions:     []*pricing.Promotion{},
				VestaInStock:   false,
				VestaUpdatedAt: "",
			}
		}

		products[price.Code] = price
	}

	return products, nil
}

func InitAlohaClient() error {
	err := prepareData()
	if err != nil {
		return err
	}

	cb := gobreaker.NewCircuitBreaker(gobreaker.Settings{
		Name:     "SSRV-Calculator-Client",
		Timeout:  60 * time.Second,
		Interval: 60 * time.Second,
		ReadyToTrip: func(c gobreaker.Counts) bool {
			if c.ConsecutiveFailures >= uint32(5) {
				return true
			}

			failureRatio := float64(c.TotalFailures) / float64(c.Requests)
			if int(c.Requests) >= 300 && failureRatio > 0.6 {
				return true
			}

			return false
		},
	})

	c := &aloha.CalculatorClientMock{
		GetPromotedProductCodesFunc: func(in *calculatorRPC.PromotedProductRequest) (*calculatorRPC.ProductCodes, error) {
			return &calculatorRPC.ProductCodes{
				ProductCodes: []string{"0029450000", "0029297000", "0029299000"},
			}, nil
		},
	}

	aloha.SetCircuitBreaker(cb)
	aloha.SetCalculatorClient(c)

	return nil
}

func prepareData() error {
	b, err := FSByte(false, "/testdata/integration/pricing/pricing.csv")
	if err != nil {
		return err
	}

	r := csv.NewReader(bytes.NewReader(b))
	r.Comma = '|'

	i := 0
	for {
		record, err := r.Read()
		if i == 0 {
			i++
			continue
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		price := &pricing.Product{
			Price: &pricing.Price{},
		}
		price.Code = record[0]
		price.StoreID = str.String(record[1]).Int()
		price.InStock = str.String(record[2]).Bool()
		price.Price.NormalPrice = str.String(record[3]).Float64()
		price.Price.PromoPrice = str.String(record[4]).Float64()
		if price.Price.PromoPrice == 0.0 {
			price.Price.PromoPrice = price.Price.NormalPrice
		}
		price.Price.StoreNormalPrice = str.String(record[5]).Float64()
		if price.Price.StoreNormalPrice == 0.0 {
			price.Price.StoreNormalPrice = price.Price.NormalPrice
		}
		price.Price.StorePromoPrice = str.String(record[6]).Float64()
		if price.Price.StorePromoPrice == 0.0 {
			price.Price.StorePromoPrice = price.Price.StoreNormalPrice
		}
		priceAndroid := str.String(record[7]).Float64()
		if priceAndroid == 0.0 {
			priceAndroid = price.Price.PromoPrice
		}
		priceIOS := str.String(record[8]).Float64()
		if priceIOS == 0.0 {
			priceIOS = price.Price.PromoPrice
		}
		price.Price.ClientTypePromoPrice = map[string]float64{
			"android": priceAndroid,
			"ios":     priceIOS,
		}

		price.Promotions = []*pricing.Promotion{}
		price.VestaInStock = false
		price.VestaUpdatedAt = ""

		priceDataMock[fmt.Sprintf("%s-%d", price.Code, price.StoreID)] = price

		i++
	}

	return nil
}
