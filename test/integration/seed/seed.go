package main

import (
	"context"

	"happyfresh.io/lib/log"
	"happyfresh.io/search/config"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/test/integration/seed/index"
)

func main() {
	ctx := context.Background()

	url, ok := config.IndexShardsTest()["id"]
	if !ok {
		log.For("main", "seed").Error("config error")
	}

	appsearchClient, err := index.NewAppsearchClient(url)
	if err != nil {
		log.For("main", "seed").Error(err)
	}

	done := datastore.OpenTest(nil, datastore.LocalQuery, config.ReadDatabaseTestGatewayDSN(), nil)
	defer done()

	seedSearch(ctx, appsearchClient)
	seedStockItem(ctx)
}

func seedSearch(ctx context.Context, appsearchClient *index.AppsearchClient) {
	js, err := index.FSByte(false, "/testdata/integration/documents/test_search.json")
	if err != nil {
		log.For("main", "seedSearch").Error(err)
	}

	err = appsearchClient.PatchByte(ctx, js)
	if err != nil {
		log.For("main", "seedSearch").Error(err)
	}
}

func seedStockItem(ctx context.Context) {
	err := index.UpdateStockItem(ctx)
	if err != nil {
		log.For("main", "seedStockItem").Error(err)
	}
}
