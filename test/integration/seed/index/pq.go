package index

import (
	"bytes"
	"context"
	"encoding/csv"
	"io"

	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/datastore"
)

func UpdateStockItem(ctx context.Context) error {
	queryStringB, err := FSByte(false, "/testdata/integration/documents/stock-item.sql")
	if err != nil {
		return err
	}

	b, err := FSByte(false, "/testdata/integration/documents/stock-item.csv")
	if err != nil {
		return err
	}

	r := csv.NewReader(bytes.NewReader(b))
	r.Comma = '|'

	conn, _ := datastore.GetCurrentConn()

	i := 0
	for {
		record, err := r.Read()
		if i == 0 {
			i++
			continue
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		variantID := str.String(record[0]).Int64()
		stockLocationID := str.String(record[1]).Int64()
		inStock := str.String(record[2]).Bool()
		popularity := str.String(record[3]).Int64()

		row := conn.QueryRowContext(ctx, string(queryStringB), variantID, stockLocationID, inStock, popularity)
		if err := row.Err(); err != nil {
			return err
		}

		i++
	}

	return nil
}
