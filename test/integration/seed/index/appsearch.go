package index

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"time"

	"github.com/pkg/errors"
	"github.com/valyala/bytebufferpool"
)

//go:generate esc -o seed.generated.go -pkg index  ../../../../testdata/integration/documents

type AppsearchClient struct {
	baseURL    url.URL
	key        string
	httpClient *http.Client
}

func NewAppsearchClient(u string) (*AppsearchClient, error) {
	uri, err := url.Parse(u)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] Failed to parse uri")
	}

	if uri.User == nil {
		return nil, errors.New("[swiftype] backend need auth")
	}

	key, ok := uri.User.Password()
	if !ok {
		return nil, errors.New("[swiftype] backend need auth")
	}
	uri.User = nil

	return &AppsearchClient{
		baseURL: *uri,
		key:     key,
		httpClient: &http.Client{
			Timeout: 5 * time.Second,
		},
	}, nil
}

func (c *AppsearchClient) PatchByte(ctx context.Context, b []byte) error {
	req, err := c.newRequest(http.MethodPatch, "documents", bytes.NewReader(b))
	if err != nil {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	resp, err := c.httpClient.Do(req.WithContext(ctx))
	if err != nil {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	return nil
}

func (c *AppsearchClient) Patch(ctx context.Context, data ...interface{}) error {
	b := bytebufferpool.Get()
	b.Reset()
	defer bytebufferpool.Put(b)

	err := json.NewEncoder(b).Encode(data)
	if err != nil {
		return errors.Wrap(err, "[swiftype] failed to encode data")
	}

	req, err := c.newRequest(http.MethodPatch, "documents", bytes.NewReader(b.B))
	if err != nil {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	resp, err := c.httpClient.Do(req.WithContext(ctx))
	if err != nil {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	return nil
}

func (c *AppsearchClient) newRequest(method, path string, body io.Reader) (*http.Request, error) {
	path = filepath.Join(c.baseURL.Path, path)
	uri, err := c.baseURL.Parse(path)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] invalid base uri")
	}

	req, err := http.NewRequest(method, uri.String(), body)
	if err != nil {
		return req, errors.Wrap(err, "[swiftype] failed to prepare request")
	}
	req.Header.Add("content-type", "application/json")
	req.Header.Add("authorization", "Bearer "+c.key)
	req.Header.Add("accept-encoding", "gzip")

	return req, nil
}
