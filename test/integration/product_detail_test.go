package integration_test

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"github.com/tidwall/gjson"
	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/search/api/handler/gateway"
)

func TestPDPGlobalID(t *testing.T) {
	if testing.Short() {
		t.Ski<PERSON><PERSON>ow()
	}

	testCases := []struct {
		name                string
		api                 string
		inputProductID      int64
		wantProductID       int64
		wantProductGlobalID int64
		wantVariantID       int64
		wantVariantGlobalID int64
		wantBrandID         int64
		wantBrandGlobalID   int64
		wantImageID         []int64
		wantImageOriginal   []string
	}{
		{
			"Different ID and Global ID",
			"/api",
			284088,
			284088,
			283948,
			281101,
			280961,
			13698,
			13698,
			[]int64{1655329, 1655330, 1655302, 1655303, 1655299},
			[]string{
				"30651bc60c5feff4ab839f777b514725ecdb4433-original.jpg",
				"7a3f80cbd61e7b74df9d0f05661d4ef7eea910e-original.jpeg",
				"20c3eb0bb86a62415d09f0df190cf09850ee073c-original.jpg",
				"1bc891cc77ba420cf68bb73bbde0a2af19221e06-original.jpeg",
				"a18e3c56acb00a1cc277af7e61dd86e033ff4b41-original.jpg",
			},
		},
		{
			"Different ID and Global ID on v3 URL",
			"/api/v3",
			284088,
			284088,
			283948,
			281101,
			280961,
			13698,
			13698,
			[]int64{1655329, 1655330, 1655302, 1655303, 1655299},
			[]string{
				"30651bc60c5feff4ab839f777b514725ecdb4433-original.jpg",
				"7a3f80cbd61e7b74df9d0f05661d4ef7eea910e-original.jpeg",
				"20c3eb0bb86a62415d09f0df190cf09850ee073c-original.jpg",
				"1bc891cc77ba420cf68bb73bbde0a2af19221e06-original.jpeg",
				"a18e3c56acb00a1cc277af7e61dd86e033ff4b41-original.jpg",
			},
		},
		{
			"Same ID",
			"/api",
			43548,
			43548,
			43548,
			43522,
			43522,
			4176,
			4176,
			[]int64{280735, 280734, 280733},
			[]string{
				"335d4db897e3afc09c111f818cd7c9e576e09998-original.jpg",
				"845ec6dfda9f7cc434b92b1d0f87988a5dfcc8f4-original.jpg",
				"5d37c850ce62a77a8cb89c4e7081bfde40432759-original.jpg",
			},
		},
	}

	header := http.Header{}
	header.Set("Content-Type", "application/json")
	header.Set("X-Happy-Client-Type", "android")
	header.Set("Synthetic", "true")
	header.Set("Locale", "EN")
	header.Set("Country", "ID")

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := fmt.Sprintf("%s/stock_locations/3/products/%d", tc.api, tc.inputProductID)
			req, err := newRequest(http.MethodGet, searchUrl, header, nil)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			json := gjson.ParseBytes(buf.Bytes())
			productID := json.Get("id").Int()
			productGlobalID := json.Get("product_global_id").Int()
			variantID := json.Get("variants").Array()[0].Get("id").Int()
			variantGlobalID := json.Get("variant_global_id").Int()
			brandID := json.Get("brand.id").Int()
			brandGlobalID := json.Get("brand_global_id").Int()

			imageIDs := []int64{}
			imageOriginals := []string{}
			images := json.Get("variants").Array()[0].Get("images").Array()
			for _, image := range images {
				imageIDs = append(imageIDs, image.Get("id").Int())
				arr := strings.Split(image.Get("original_url").String(), "/attachments/")
				imageOriginals = append(imageOriginals, arr[1])
			}

			assert := convey.ShouldEqual(resp.StatusCode, http.StatusOK)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldEqual(productID, tc.wantProductID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldEqual(productGlobalID, tc.wantProductGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldEqual(variantID, tc.wantVariantID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldEqual(variantGlobalID, tc.wantVariantGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldEqual(brandID, tc.wantBrandID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldEqual(brandGlobalID, tc.wantBrandGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(imageIDs, tc.wantImageID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(imageOriginals, tc.wantImageOriginal)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestPDPResponse(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	convey.Convey("Response sanity check", t, func() {
		searchUrl := "/api/stock_locations/3/products/32193"

		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("X-Happy-Client-Type", "snd")
		header.Set("Locale", "ID")
		header.Set("ID", "ID")

		req, err := newRequest(http.MethodGet, searchUrl, header, nil)
		if err != nil {
			t.Error(err)
		}

		resp, err := httpClient.Do(req)
		if err != nil {
			t.Error(err)
		}

		buf := &bytes.Buffer{}
		_, err = buf.ReadFrom(resp.Body)
		defer resp.Body.Close()
		if err != nil {
			t.Error(err)
		}

		json := gjson.ParseBytes(buf.Bytes())
		variant := json.Get("variants").Array()[0]
		images := variant.Get("images").Array()
		brand := json.Get("brand")

		convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusOK)
		convey.So(json.Get("id").Int(), convey.ShouldEqual, 32193)
		convey.So(json.Get("name").String(), convey.ShouldEqual, "Telur Prima Telur Ayam Rendah Kalori")
		convey.So(json.Get("description").String(), convey.ShouldEqual, "Telur Ayam Rendah Kolesterol dengan Vitamin A (10 butir)")
		convey.So(json.Get("in_stock").Bool(), convey.ShouldEqual, true)
		convey.So(json.Get("stock_prediction").Bool(), convey.ShouldEqual, false)
		convey.So(json.Get("stock_prediction_updated_at").String(), convey.ShouldEqual, "")
		convey.So(json.Get("display_unit").String(), convey.ShouldEqual, "1 packet")
		convey.So(json.Get("supermarket_unit").String(), convey.ShouldEqual, "packet")
		convey.So(json.Get("natural_average_weight").Int(), convey.ShouldEqual, 1)
		convey.So(json.Get("display_average_weight").String(), convey.ShouldEqual, "~ 1 packet")
		convey.So(json.Get("popularity").Float(), convey.ShouldEqual, 0)
		convey.So(json.Get("taxon_ids").Array(), convey.ShouldResemble, []gjson.Result{{Type: gjson.Type(2), Raw: "27", Str: "", Num: 27, Index: 0}})
		convey.So(json.Get("categories").Array(), convey.ShouldResemble, []gjson.Result{{Type: gjson.Type(3), Raw: "\"Telur\"", Str: "Telur", Num: 0, Index: 0}})
		convey.So(json.Get("product_type_name").String(), convey.ShouldEqual, "eggs/chicken/regular")
		convey.So(json.Get("display_banner_text").Array(), convey.ShouldResemble, []gjson.Result{})
		convey.So(json.Get("display_normal_price").String(), convey.ShouldEqual, "")
		convey.So(json.Get("price").String(), convey.ShouldEqual, "38400")
		convey.So(json.Get("display_price").String(), convey.ShouldEqual, "Rp38,400")
		convey.So(json.Get("store_normal_price").String(), convey.ShouldEqual, "35500")
		convey.So(json.Get("display_store_normal_price").String(), convey.ShouldEqual, "Rp35,500")
		convey.So(json.Get("store_promo_price").String(), convey.ShouldEqual, "35500")
		convey.So(json.Get("display_store_promo_price").String(), convey.ShouldEqual, "Rp35,500")
		convey.So(json.Get("unit_price").String(), convey.ShouldEqual, "38400")
		convey.So(json.Get("display_unit_price").String(), convey.ShouldEqual, "Rp38,400 / each")
		convey.So(json.Get("supermarket_unit_cost_price").String(), convey.ShouldEqual, "35500")
		convey.So(json.Get("display_supermarket_unit_cost_price").String(), convey.ShouldEqual, "Rp35,500 / each")
		convey.So(json.Get("display_promo_price_percentage").String(), convey.ShouldEqual, "")
		convey.So(json.Get("display_promotion_actions_combination_text").String(), convey.ShouldEqual, "")
		convey.So(json.Get("promotion_actions").Array(), convey.ShouldResemble, []gjson.Result{})
		convey.So(json.Get("promotions").Array(), convey.ShouldResemble, []gjson.Result{})
		convey.So(json.Get("delivered_quantity").Int(), convey.ShouldEqual, 29)

		convey.So(variant.Get("id").Int(), convey.ShouldEqual, 32175)
		convey.So(variant.Get("name").String(), convey.ShouldEqual, "Telur Prima Telur Ayam Rendah Kalori")
		convey.So(variant.Get("description").String(), convey.ShouldEqual, "Telur Ayam Rendah Kolesterol dengan Vitamin A (10 butir)")
		convey.So(variant.Get("in_stock").Bool(), convey.ShouldEqual, true)
		convey.So(variant.Get("stock_prediction").Bool(), convey.ShouldEqual, false)
		convey.So(variant.Get("stock_prediction_updated_at").String(), convey.ShouldEqual, "")
		convey.So(variant.Get("sku").String(), convey.ShouldEqual, "093348311853-ID")
		convey.So(variant.Get("is_master").Bool(), convey.ShouldEqual, true)
		convey.So(variant.Get("track_inventory").Bool(), convey.ShouldEqual, true)

		convey.So(len(images), convey.ShouldEqual, 0)

		convey.So(brand.Get("id").Int(), convey.ShouldEqual, 12728)
		convey.So(brand.Get("name").String(), convey.ShouldEqual, "Telur Prima")
	})
}

func TestRequestHeaderAndParam(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	defer gateway.WithCatalogService(catalogService)()

	gateway.WithCatalogService(&productDetailMock{})()

	searchURl := "/api/stock_locations/3/products/32441"

	header := http.Header{}
	header.Set("Content-Type", "application/json")
	header.Set("X-Happy-Client-Type", "android")
	header.Set("X-Happy-Client-Version", "2.30")
	header.Set("X-Spree-Token", "123456")
	header.Set("Synthetic", "true")
	header.Set("Locale", "id")
	header.Set("Country", "ID")

	req, err := newRequest(http.MethodGet, searchURl, header, nil)
	if err != nil {
		t.Error(err)
	}

	resp, err := httpClient.Do(req)
	if err != nil {
		t.Error(err)
	}

	buf := &bytes.Buffer{}
	_, err = buf.ReadFrom(resp.Body)
	defer resp.Body.Close()
	if err != nil {
		t.Error(err)
	}

	json := gjson.ParseBytes(buf.Bytes())
	name := json.Get("name").String()
	if name != "local-name" {
		t.Errorf("expectation: %v\nreality: %v", "local-name", name)
	}

	price := json.Get("price").Float()
	if price != 900000 {
		t.Errorf("expectation: %v\nreality: %v", 900000, price)
	}

	// mpq := json.Get("max_promo_quantity").Int()
	// if mpq != 2 {
	// 	t.Errorf("expectation: %v\nreality: %v", 2, mpq)
	// }

	// moq := json.Get("max_order_quantity").Int()
	// if moq != 999 {
	// 	t.Errorf("expectation: %v\nreality: %v", 999, moq)
	// }

	// pop := json.Get("popularity").Float()
	// if pop != 10 {
	// 	t.Errorf("expectation: %v\nreality: %v", 10, pop)
	// }

}

func TestRequestHeaderAndParamV3(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	defer gateway.WithCatalogService(catalogService)()

	gateway.WithCatalogService(&productDetailMock{})()

	searchURl := "/api/v3/stock_locations/3/products/32441"

	header := http.Header{}
	header.Set("Content-Type", "application/json")
	header.Set("X-Happy-Client-Type", "android")
	header.Set("X-Happy-Client-Version", "2.30")
	header.Set("X-Spree-Token", "123456")
	header.Set("Synthetic", "true")
	header.Set("Locale", "id")
	header.Set("Country", "ID")

	req, err := newRequest(http.MethodGet, searchURl, header, nil)
	if err != nil {
		t.Error(err)
	}

	resp, err := httpClient.Do(req)
	if err != nil {
		t.Error(err)
	}

	buf := &bytes.Buffer{}
	_, err = buf.ReadFrom(resp.Body)
	defer resp.Body.Close()
	if err != nil {
		t.Error(err)
	}

	json := gjson.ParseBytes(buf.Bytes())
	name := json.Get("name").String()
	if name != "local-name" {
		t.Errorf("expectation: %v\nreality: %v", "local-name", name)
	}

	price := json.Get("price").Float()
	if price != 900000 {
		t.Errorf("expectation: %v\nreality: %v", 900000, price)
	}

	// mpq := json.Get("max_promo_quantity").Int()
	// if mpq != 2 {
	// 	t.Errorf("expectation: %v\nreality: %v", 2, mpq)
	// }

	// moq := json.Get("max_order_quantity").Int()
	// if moq != 999 {
	// 	t.Errorf("expectation: %v\nreality: %v", 999, moq)
	// }

	// pop := json.Get("popularity").Float()
	// if pop != 10 {
	// 	t.Errorf("expectation: %v\nreality: %v", 10, pop)
	// }

}

type productDetailMock struct{}

func (m *productDetailMock) GetProductDetail(ctx context.Context, stockLocationID int64, productID int64, channel string, locale string) (*cat.ProductDetail, error) {
	out := &cat.ProductDetail{
		Name:      "en-name",
		NameLocal: "local-name",
		StockItem: &cat.StockItem{
			Price: &cat.Price{
				ClientPrice: 100000,
			},
		},
		Variants: []*cat.Variant{
			{
				Sku: "123456",
			},
		},
		Properties: &cat.Properties{},
	}

	if locale != "en" {
		out.NameLocal = "local-name"
	}
	if channel == "android" {
		out.StockItem.Price.ClientPrice = 900000
	}

	// if in.ClientVersion(md) == "2.30" {
	// 	out.MaxPromoQuantity = 2
	// }
	// if in.UserType(md) == "hc" {
	// 	out.MaxOrderQuantity = 999
	// }
	// if in.UserToken(md) == "123456" {
	// 	out.Popularity = 10
	// }

	return out, nil
}

func (m *productDetailMock) GetStoreProductsByProductID(ctx context.Context, productID int64, locale string) (*cat.StoreProducts, error) {
	return nil, nil
}

func (m *productDetailMock) GetStoreProductsByID(ctx context.Context, storeProductID int64, locale string) (*cat.StoreProducts, error) {
	return nil, nil
}

func (c *productDetailMock) GetStoreProductsPLP(ctx context.Context, productIDs []int64, stockLocationID int64, locale string) ([]*cat.StoreProducts, error) {
	return nil, nil
}

func (c *productDetailMock) GetProductTypesByProductTypeID(ctx context.Context, productTypeID int64) (*cat.ProductTypes, error) {
	return nil, nil
}

func (c *productDetailMock) GetProductsByVariantIDs(ctx context.Context, variantIDs []int64, restrictedTaxonIDs []int64, storeID int64, sellableUserType string) ([]*cat.Product, error) {
	return nil, nil
}

func (c *productDetailMock) TrackPopularity(ctx context.Context, variantIDs []int64, stockLocationID int64, action string) error {
	return nil
}

func (c *productDetailMock) GetTaxonomy(ctx context.Context, taxonomyID int64, stockLocationID int64, locale string, country string) (*cat.Taxonomy, error) {
	return nil, nil
}

func (c *productDetailMock) GetTaxonIDs(taxons []*cat.Taxon) []int64 {
	return nil
}

func (c *productDetailMock) GetBrands(ctx context.Context, brandIDs []int64, locale string) ([]*cat.Brand, error) {
	return nil, nil
}

func (c *productDetailMock) GetRestrictedTaxons(ctx context.Context, taxonomyID int64, stockLocationID int64, channel string, isSND bool) (*cat.RestrictedTaxons, error) {
	return nil, nil
}

func (c *productDetailMock) GetStoreTaxons(ctx context.Context, taxonIDs []int64, stockLocationID int64, locale, country string) ([]*cat.StoreTaxon, error) {
	return nil, nil
}

func (c *productDetailMock) GetManualSKUBoostsByPromoCode(ctx context.Context, promoCode string, stockLocationId int64) (*cat.ManualSKUBoosts, error) {
	return nil, nil
}

func (c *productDetailMock) GetTaxonPinsByCountry(ctx context.Context, countryCode string, taxonPinMode string) (*cat.TaxonPins, error) {
	return nil, nil
}

func (c *productDetailMock) GetThemes(ctx context.Context, stockLocationId int64, locale string) (*cat.Themes, error) {
	return nil, nil
}

func (c *productDetailMock) GetVariantsByTheme(ctx context.Context, themeId int64) (*cat.ThemeVariants, error) {
	return nil, nil
}
