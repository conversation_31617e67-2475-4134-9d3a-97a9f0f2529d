package integration_test

import (
	"bytes"
	"fmt"
	"net/http"
	"net/url"
	"testing"

	ext_assert "github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"happyfresh.io/search/test/integration/assert"
)

func TestPromotionPLP(t *testing.T) {
	if testing.Short() {
		t.<PERSON><PERSON>()
	}

	for _, tc := range []struct {
		name         string
		promotionID  int64
		header       map[string]string
		param        map[string][]string
		assertOption []assert.Option
	}{
		{
			"Relevant with given promotion ID, default sorting by popularity desc",
			1986895,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"per_page": {"10"},
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{241174, 241151, 241126, 241153}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("Indonesian Vegetables Salad", "Indonesian Vegetables Salad with Peanut Dressing"),
				assert.CategoriesOption([]string{"The Party 101", "Ready to Eat - Other"}, []int64{212, 215}),
			},
		},
		{
			"Relevant with given promotion ID, default sorting by popularity desc, localized",
			1986895,
			map[string]string{
				"Locale":              "id",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"per_page": {"10"},
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{241174, 241151, 241126, 241153}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("Indonesian Vegetables Salad-Localized", "Indonesian Vegetables Salad with Peanut Dressing-Localized"),
				assert.CategoriesOption([]string{"The Party 101-Localized", "Ready to Eat - Other-Localized"}, []int64{212, 215}),
			},
		},
		{
			"Relevant given promo PLP with manual boosting, organic results sorted by popularity",
			1995084,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"per_page": {"10"},
				"q[s]":     {"popularity desc"},
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{241126, 241153, 241174, 241151}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("Fina Fried Rice", "Red Fried Rice with Shrimp Topping"),
			},
		},
		{
			"Relevant given promo PLP with manual boosting, results by price",
			1995084,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string][]string{
				"per_page": {"10"},
				"q[s]":     {"price desc"},
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{241126, 241153, 241174, 241151}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.NameAndDecriptionOption("Fina Fried Rice", "Red Fried Rice with Shrimp Topping"),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := fmt.Sprintf("/api/stock_locations/402/promotions/%d/products", tc.promotionID)

			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			param.Set("category", "special")
			for k, v := range tc.param {
				param[k] = v
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			ext_assert.Equal(t, http.StatusOK, resp.StatusCode)

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}
