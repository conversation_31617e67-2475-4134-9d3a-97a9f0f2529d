package catalog

import (
	"bytes"
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/catalog"
	_grpc "happyfresh.io/search/lib/grpc"
)

//go:generate esc -o catalog.generated.go -pkg catalog  ../../../testdata/integration/catalog

var (
	defaultName        = "Integration test product name of ID %d"
	defaultDescription = "Integration test product desc of ID %d"
	defaultSKU         = "Integration test SKU-ID"

	productDetailDataMock = map[int64]*cat.ProductDetail{
		283948: {
			ProductId: 283948,
			StockItem: nil,
			Variants: []*cat.Variant{
				{
					VariantId: 280961,
					Images: &cat.Images{Images: []*cat.Image{
						{
							Id:          1655329,
							OriginalUrl: "http://j.com/attachments/30651bc60c5feff4ab839f777b514725ecdb4433-original.jpg",
						},
						{
							Id:          1655330,
							OriginalUrl: "http://j.com/attachments/7a3f80cbd61e7b74df9d0f05661d4ef7eea910e-original.jpeg",
						},
						{
							Id:          1655302,
							OriginalUrl: "http://j.com/attachments/20c3eb0bb86a62415d09f0df190cf09850ee073c-original.jpg",
						},
						{
							Id:          1655303,
							OriginalUrl: "http://j.com/attachments/1bc891cc77ba420cf68bb73bbde0a2af19221e06-original.jpeg",
						},
						{
							Id:          1655299,
							OriginalUrl: "http://j.com/attachments/a18e3c56acb00a1cc277af7e61dd86e033ff4b41-original.jpg",
						},
					}},
				},
			},
			TaxonIds:   []int64{},
			Categories: []string{},
			Brand:      &cat.Brand{BrandId: 13698},
			Properties: &cat.Properties{},
		},
		43548: {
			ProductId: 43548,
			StockItem: nil,
			Variants: []*cat.Variant{
				{
					VariantId: 43522,
					Images: &cat.Images{Images: []*cat.Image{
						{
							Id:          280735,
							OriginalUrl: "http://j.com/attachments/335d4db897e3afc09c111f818cd7c9e576e09998-original.jpg",
						},
						{
							Id:          280734,
							OriginalUrl: "http://j.com/attachments/845ec6dfda9f7cc434b92b1d0f87988a5dfcc8f4-original.jpg",
						},
						{
							Id:          280733,
							OriginalUrl: "http://j.com/attachments/5d37c850ce62a77a8cb89c4e7081bfde40432759-original.jpg",
						},
					}},
				},
			},
			TaxonIds:   []int64{},
			Categories: []string{},
			Brand:      &cat.Brand{BrandId: 4176},
			Properties: &cat.Properties{},
		},
		32193: {
			ProductId:        32193,
			Name:             "Telur Prima Lower Cholesterol Chicken Egg",
			NameLocal:        "Telur Prima Telur Ayam Rendah Kalori",
			Description:      "Lower Cholesterol Chicken Egg with Vitamin A (10 pieces)",
			DescriptionLocal: "Telur Ayam Rendah Kolesterol dengan Vitamin A (10 butir)",
			StockItem: &cat.StockItem{
				InStock:        true,
				VestaInStock:   true,
				VestaUpdatedAt: "2020-11-13T13:07:00.507Z",
				Price: &cat.Price{
					Cost:                        35500,
					OriginalPromoCost:           35500,
					Normal:                      38400,
					ClientCost:                  35500,
					ClientPrice:                 38400,
					OriginalPromoCostWithMarkup: 38400,
					UnitPrice:                   3840,
					SupermarketUnitPrice:        3550,
				},
			},
			Properties: &cat.Properties{
				SupermarketUnit:  "packet",
				AverageWeight:    1,
				NaturalUnit:      "1 packet",
				SellNatural:      false,
				MaxOrderQuantity: 0,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 32175,
					Images:    &cat.Images{Images: []*cat.Image{}},
					Sku:       "093348311853-ID",
				},
			},
			Locale:      "id",
			ProductType: "eggs/chicken/regular",
			TaxonIds:    []int64{27},
			Categories:  []string{"Telur"},
			Brand:       &cat.Brand{BrandId: 12728, Name: "Telur Prima", NameLocal: "Telur Prima"},
		},
	}

	variantDetailDataMock = map[int64]*cat.ProductDetail{
		280970: {
			ProductId:   283957,
			TaxonIds:    []int64{1049},
			Brand:       &cat.Brand{BrandId: 27260},
			ProductType: "eggs/chicken/regular",
			Properties: &cat.Properties{
				NaturalUnit:      "5 x 10 g",
				SupermarketUnit:  "g",
				AverageWeight:    1,
				SellNatural:      false,
				MaxOrderQuantity: 2,
				Size_:            10,
				UnitPieces:       1,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 280970,
					Sku:       "FASH10006-ID",
					Images:    &cat.Images{Images: []*cat.Image{}},
				},
			},
		},
		280969: {
			ProductId: 283956,
			TaxonIds:  []int64{1049},
			Brand:     &cat.Brand{BrandId: 27260},
			Properties: &cat.Properties{
				NaturalUnit:      "5 x 10 g",
				SupermarketUnit:  "g",
				AverageWeight:    1,
				SellNatural:      false,
				MaxOrderQuantity: 0,
				Size_:            10,
				UnitPieces:       1,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 280969,
					Sku:       "FASH10005-ID",
					Images:    &cat.Images{Images: []*cat.Image{}},
				},
			},
		},
		280961: {
			ProductId: 283948,
			TaxonIds:  []int64{1029},
			Brand:     &cat.Brand{BrandId: 13698},
			Properties: &cat.Properties{
				NaturalUnit:      "5 x 10 g",
				SupermarketUnit:  "Each",
				AverageWeight:    1,
				SellNatural:      false,
				MaxOrderQuantity: 0,
				Size_:            10,
				UnitPieces:       1,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 280961,
					Sku:       "KDS10006-ID",
					Images: &cat.Images{Images: []*cat.Image{
						{
							Id:          1655299,
							OriginalUrl: "/attachments/a18e3c56acb00a1cc277af7e61dd86e033ff4b41-original.jpg",
						},
						{
							Id:          1655303,
							OriginalUrl: "/attachments/1bc891cc77ba420cf68bb73bbde0a2af19221e06-original.jpeg",
						},
					}},
				},
			},
		},
		43522: {
			ProductId:        43548,
			NameLocal:        "Dimes Jus Jeruk",
			DescriptionLocal: "Minuman Jus Jeruk Tanpa Gula Tambahan",
			Slug:             "dimes-orange-juice",
			TaxonIds:         []int64{55},
			Categories:       []string{"Jus & Minuman Kesehatan", "Minuman"},
			Brand:            &cat.Brand{BrandId: 4176, Name: "Dimes", NameLocal: "Dimes-ID"},
			Properties: &cat.Properties{
				NaturalUnit:      "1 L",
				SupermarketUnit:  "L",
				AverageWeight:    1,
				SellNatural:      false,
				MaxOrderQuantity: 0,
				Size_:            1,
				UnitPieces:       1,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 43522,
					Sku:       "8690558018057-ID",
					Images: &cat.Images{Images: []*cat.Image{
						{
							Id:          280733,
							OriginalUrl: "/attachments/5d37c850ce62a77a8cb89c4e7081bfde40432759-original.jpg",
						},
						{
							Id:          280734,
							OriginalUrl: "/attachments/845ec6dfda9f7cc434b92b1d0f87988a5dfcc8f4-original.jpg",
						},
					}},
				},
			},
		},
		280113: {
			ProductId:        283088,
			NameLocal:        "Custom Taxon Chips",
			DescriptionLocal: "Custom Taxon Chips",
			Properties: &cat.Properties{
				NaturalUnit:      "1 L",
				SupermarketUnit:  "L",
				AverageWeight:    1,
				SellNatural:      false,
				MaxOrderQuantity: 0,
				Size_:            1,
				UnitPieces:       1,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 280113,
					Sku:       "90013",
					Images:    &cat.Images{},
				},
			},
		},
		280114: {
			ProductId:        283089,
			NameLocal:        "Custom Taxon Cabbage",
			DescriptionLocal: "Custom Taxon Cabbage",
			Properties: &cat.Properties{
				NaturalUnit:      "1 L",
				SupermarketUnit:  "L",
				AverageWeight:    1,
				SellNatural:      false,
				MaxOrderQuantity: 0,
				Size_:            1,
				UnitPieces:       1,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 280114,
					Sku:       "90014",
					Images:    &cat.Images{},
				},
			},
		},
		280115: {
			ProductId:        283090,
			NameLocal:        "Custom Taxon Orange",
			DescriptionLocal: "Custom Taxon Orange",
			Properties: &cat.Properties{
				NaturalUnit:      "1 L",
				SupermarketUnit:  "L",
				AverageWeight:    1,
				SellNatural:      false,
				MaxOrderQuantity: 0,
				Size_:            1,
				UnitPieces:       1,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 280115,
					Sku:       "90015",
					Images:    &cat.Images{},
				},
			},
		},
		280117: {
			ProductId:        283092,
			NameLocal:        "Custom Taxon Beef Sausage Promo",
			DescriptionLocal: "Custom Taxon Beef Sausage Promo",
			Properties: &cat.Properties{
				NaturalUnit:      "1 L",
				SupermarketUnit:  "L",
				AverageWeight:    1,
				SellNatural:      false,
				MaxOrderQuantity: 0,
				Size_:            1,
				UnitPieces:       1,
			},
			Variants: []*cat.Variant{
				{
					VariantId: 280117,
					Sku:       "90017",
					Images:    &cat.Images{},
				},
			},
		},
	}

	storeProductDataMock = map[string]*cat.StoreProduct{
		"280970-3": {
			StoreProductId:  5872526,
			StockLocationId: 3,
			StoreId:         1101,
			VariantId:       280970,
		},
		"280969-3": {
			StoreProductId:  5872522,
			StockLocationId: 3,
			StoreId:         1101,
			VariantId:       280969,
		},
		"280961-3": {
			StoreProductId:  5872103,
			StockLocationId: 3,
			StoreId:         1101,
			VariantId:       280961,
		},
		"43522-3": {
			StoreProductId:  267029,
			StockLocationId: 3,
			StoreId:         1101,
			VariantId:       43522,
		},
		"**********": {
			StockLocationId: 520,
			StoreId:         9992,
			VariantId:       280113,
		},
		"280114-520": {
			StockLocationId: 520,
			StoreId:         9992,
			VariantId:       280114,
		},
		"**********": {
			StockLocationId: 520,
			StoreId:         9992,
			VariantId:       280115,
		},
		"**********": {
			StockLocationId: 520,
			StoreId:         9992,
			VariantId:       280117,
		},
	}

	productSearchMock = map[int64]*cat.Product{
		280113: {
			ProductId: 283088,
		},
		280115: {
			ProductId: 283090,
		},
		280114: {
			ProductId: 283089,
		},
		280117: {
			ProductId: 283092,
		},
	}

	taxonomyDataMock = map[string]*cat.Taxonomy{
		"1-3": {
			Taxons: []*cat.Taxon{
				{
					Taxons: []*cat.Taxon{
						{
							TaxonId:         111,
							Name:            "Oils & Vinegars",
							ParentId:        48,
							Description:     "oils & vinegars description",
							Permalink:       "pantry/oils-and-vinegars",
							TaxonomyId:      1,
							MetaDescription: "oils & vinegars meta description",
							MetaKeywords:    "oils & vinegars meta keywords",
							MetaTitle:       "oils & vinegars meta title",
							Position:        311,
							DisplayImage: &cat.Image{
								Id:         84806,
								MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
								ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							},
							SortingUnitPrice: false,
							Slug:             "oils-vinegars-111",
							IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							ProductsCount:    1,
						},
					},
					TaxonId:         48,
					ParentId:        1,
					Name:            "Pantry",
					Description:     "pantry description",
					Permalink:       "pantry",
					TaxonomyId:      1,
					MetaDescription: "pantry meta description",
					MetaKeywords:    "pantry meta keywords",
					MetaTitle:       "pantry meta title",
					Position:        300,
					DisplayImage: &cat.Image{
						Id:         84806,
						MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
					},
					SortingUnitPrice: false,
					Slug:             "pantry-48",
					IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
				},
				{
					Taxons:          []*cat.Taxon{},
					TaxonId:         96,
					ParentId:        1,
					Name:            "Household",
					Description:     "household description",
					Permalink:       "household",
					TaxonomyId:      1,
					MetaDescription: "household meta description",
					MetaKeywords:    "household meta keywords",
					MetaTitle:       "household meta title",
					Position:        330,
					DisplayImage: &cat.Image{
						Id:         1616998,
						MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
						ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
					},
					SortingUnitPrice: false,
					Slug:             "household-96",
					IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
					ProductsCount:    1,
				},
			},
		},
	}

	brandDataMock = map[int64]string{
		17120: "fisherman's friend",
		21806: "hotel",
		3020:  "fantastic",
		3538:  "fitti",
		4279:  "pure green",
		1934:  "brand name of id 1934",
		5518:  "buavita",
		7941:  "brand name of id 7941",
		22015: "sari roti",
		16844: "biskuat",
	}

	taxonRestrictionDataMock = map[int64][]int64{
		106: {34, 344, 55},
		402: {},
	}

	manualSkuBoostsDataMock = map[string][]*cat.ManualSKUBoost{
		"SSRV_ITG_TEST": {},
		"SSRV_ITG_TEST_PLP": {
			{
				PromotionCode:   "SSRV_ITG_TEST_PLP",
				StockLocationId: 402,
				VariantId:       238502,
				Position:        1,
			},
			{
				PromotionCode:   "SSRV_ITG_TEST_PLP",
				StockLocationId: 402,
				VariantId:       238529,
				Position:        2,
			},
		},
	}

	themesDataMock = map[int64][]*cat.Theme{
		3: {
			{
				ThemeId:  1,
				Position: 2,
				Title:    "Theme 1",
				Translations: map[string]string{
					"en": "Theme 1 EN",
					"my": "Theme 1 MY",
					"id": "Theme 1 ID",
					"th": "Theme 1 TH",
				},
				ImageUrl: "/theme/display_images/5d557ced83842f5ac226100966897975600958e0-mini.jpg",
			},
			{
				ThemeId:  2,
				Position: 1,
				Title:    "Theme 2",
				Translations: map[string]string{
					"en": "Theme 2 EN",
					"my": "Theme 2 MY",
					"id": "Theme 2 ID",
					"th": "Theme 2 TH",
				},
				ImageUrl: "/theme/display_images/5d557ced83842f5ac226100966897975600958e0-mini.jpg",
			},
		},
	}

	themeVariantsDataMock = map[int64]*cat.ThemeVariants{
		1: {
			ThemeId:    1,
			VariantIds: []int64{280970, 280969, 280961},
		},
	}
)

func New() (catalog.Client, error) {
	err := prepareData()
	if err != nil {
		return nil, err
	}

	return prepareCatalog()
}

func prepareCatalog() (catalog.Client, error) {
	catalogService, err := catalog.New("", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
		GetProductDetailFunc: func(ctx context.Context, in *cat.ProductDetailRequest, opts ...grpc.CallOption) (*cat.ProductDetail, error) {
			pd, ok := productDetailDataMock[in.ProductId]
			if !ok {
				return nil, errors.New("doesnt exist")
			}

			return pd, nil
		},
		GetStoreProductsByStockLocationIDAndVariantIDFunc: func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
			pd, ok := variantDetailDataMock[in.VariantId]
			if !ok {
				return nil, _grpc.NewStatusError(codes.NotFound, errors.New("variant doesnt exist"))
			}

			k := fmt.Sprintf("%d-%d", in.VariantId, in.StockLocationId)
			sp, ok := storeProductDataMock[k]
			if !ok {
				return nil, _grpc.NewStatusError(codes.NotFound, errors.New("store product doesnt exist"))
			}

			sp.Vendor = pd.Vendor

			return &cat.StoreProducts{
				ProductDetail: pd,
				StoreProducts: []*cat.StoreProduct{sp},
			}, nil
		},
		SearchProductsFunc: func(ctx context.Context, in *cat.SearchProductsRequest, opts ...grpc.CallOption) (*cat.Products, error) {
			products := []*cat.Product{}
			for _, v := range in.VariantIds {
				pd, ok := productSearchMock[v]
				if !ok {
					continue
				}

				products = append(products, pd)
			}

			return &cat.Products{Products: products}, nil
		},
		GetTaxonomyFunc: func(ctx context.Context, in *cat.TaxonomyRequest, opts ...grpc.CallOption) (*cat.Taxonomy, error) {
			key := fmt.Sprintf("%d-%d", in.TaxonomyId, in.StockLocationId)
			t, ok := taxonomyDataMock[key]
			if !ok {
				return nil, errors.New("does not exist")
			}
			return t, nil
		},
		GetBrandFunc: func(ctx context.Context, in *cat.BrandRequest, opts ...grpc.CallOption) (*cat.Brand, error) {
			b, ok := brandDataMock[in.BrandId]
			if !ok {
				return &cat.Brand{}, nil
			}

			return &cat.Brand{
				BrandId:   in.BrandId,
				Name:      b,
				NameLocal: fmt.Sprintf("%s-%s", b, in.Locale),
			}, nil
		},
		GetRestrictedTaxonsFunc: func(ctx context.Context, in *cat.RestrictedTaxonsRequest, opts ...grpc.CallOption) (*cat.RestrictedTaxons, error) {
			txs, ok := taxonRestrictionDataMock[in.StockLocationId]
			if !ok {
				return nil, errors.New("does not exists")
			}

			return &cat.RestrictedTaxons{
				RestrictedTaxonIds: txs,
			}, nil
		},
		GetManualSKUBoostsByPromoCodeFunc: func(ctx context.Context, in *cat.ManualSKUBoostRequest, opts ...grpc.CallOption) (*cat.ManualSKUBoosts, error) {
			boosts, ok := manualSkuBoostsDataMock[in.PromoCode]
			if !ok {
				return nil, errors.New("does not exists")
			}

			return &cat.ManualSKUBoosts{
				ManualSkuBoosts: boosts,
			}, nil
		},
		GetThemesFunc: func(ctx context.Context, in *cat.ThemeRequest, opts ...grpc.CallOption) (*cat.Themes, error) {
			themes, ok := themesDataMock[in.StockLocationId]
			if !ok {
				return nil, errors.New("does not exists")
			}

			return &cat.Themes{
				Themes: themes,
			}, nil
		},
		GetVariantsByThemeFunc: func(ctx context.Context, in *cat.ThemeRequest, opts ...grpc.CallOption) (*cat.ThemeVariants, error) {
			themeVariants, ok := themeVariantsDataMock[in.ThemeId]
			if !ok {
				return nil, errors.New("does not exists")
			}

			return themeVariants, nil
		},
	}))
	if err != nil {
		return nil, err
	}

	return catalogService, nil
}

func prepareData() error {
	err := prepareProductDetailDataMock()
	if err != nil {
		return err
	}

	err = prepareStoreProductDataMock()
	if err != nil {
		return err
	}

	return err
}

func prepareProductDetailDataMock() error {
	b, err := FSByte(false, "/testdata/integration/catalog/product-detail.csv")
	if err != nil {
		return err
	}

	r := csv.NewReader(bytes.NewReader(b))
	r.Comma = '|'

	i := 0
	for {
		record, err := r.Read()
		if i == 0 {
			i++
			continue
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		pd := &cat.ProductDetail{}
		v := &cat.Variant{}
		pd.ProductId = str.String(record[0]).Int64()
		pd.Name = fmt.Sprintf(defaultName, pd.ProductId)
		if record[1] != "" && record[1] != "-" {
			pd.Name = record[1]
		}
		pd.NameLocal = pd.Name + "-Localized"

		pd.Description = fmt.Sprintf(defaultDescription, pd.ProductId)
		if record[2] != "" && record[2] != "-" {
			pd.Description = record[2]
		}
		pd.DescriptionLocal = pd.Description + "-Localized"

		v.VariantId = str.String(record[3]).Int64()

		v.Sku = defaultSKU
		if record[4] != "" && record[4] != "-" {
			v.Sku = record[4]
		}

		v.Images = &cat.Images{Images: []*cat.Image{}}
		if record[5] != "" && record[5] != "-" && record[6] != "" && record[6] != "-" {
			imageIDs := strings.Split(record[5], ";")
			imageURLs := strings.Split(record[6], ";")
			for i, id := range imageIDs {
				v.Images.Images = append(v.Images.Images, &cat.Image{Id: str.String(id).Int64(), OriginalUrl: imageURLs[i]})
			}
		}
		pd.Variants = []*cat.Variant{v}
		pd.TaxonIds = []int64{}
		pd.Categories = []string{}
		pd.CategoriesLocal = []string{}
		if record[7] != "" && record[7] != "-" && record[8] != "" && record[8] != "-" {
			taxonIDs := strings.Split(record[7], ";")
			taxonNames := strings.Split(record[8], ";")
			for i, id := range taxonIDs {
				pd.TaxonIds = append(pd.TaxonIds, str.String(id).Int64())
				pd.Categories = append(pd.Categories, taxonNames[i])
				pd.CategoriesLocal = append(pd.CategoriesLocal, taxonNames[i]+"-Localized")
			}
		}
		if record[9] != "" && record[9] != "-" && record[10] != "" && record[10] != "-" {
			pd.Brand = &cat.Brand{
				BrandId:   str.String(record[9]).Int64(),
				Name:      record[10],
				NameLocal: record[10] + "-Localized",
			}
		}
		if record[11] != "" && record[11] != "-" {
			pd.ProductType = record[11]
		}
		pd.Properties = &cat.Properties{
			AverageWeight: str.String(record[12]).Float64(),
			SellNatural:   str.String(record[13]).Bool(),
			Size_:         str.String(record[14]).Float64(),
			UnitPieces:    str.String(record[15]).Int64(),
		}
		pd.Vendor = &cat.Vendor{
			VendorId:         str.String(record[16]).Int64(),
			Name:             str.String(record[17]).String(),
			Description:      str.String(record[17]).String() + " Description",
			DescriptionLocal: str.String(record[17]).String() + " Description-Localized",
			ImageUrl:         "http://url/" + str.String(record[17]).String() + ".jpg",
		}

		productDetailDataMock[pd.ProductId] = pd
		variantDetailDataMock[pd.Variants[0].VariantId] = pd

		i++
	}

	return nil
}

func prepareStoreProductDataMock() error {
	b, err := FSByte(false, "/testdata/integration/catalog/store-product.csv")
	if err != nil {
		return err
	}

	r := csv.NewReader(bytes.NewReader(b))
	r.Comma = '|'

	i := 0
	for {
		record, err := r.Read()
		if i == 0 {
			i++
			continue
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		sp := &cat.StoreProduct{
			StoreProductId:  str.String(record[0]).Int64(),
			StockLocationId: str.String(record[1]).Int64(),
			StoreId:         str.String(record[2]).Int64(),
			VariantId:       str.String(record[3]).Int64(),
		}

		k := fmt.Sprintf("%d-%d", sp.VariantId, sp.StockLocationId)
		storeProductDataMock[k] = sp

		i++
	}

	return nil
}
