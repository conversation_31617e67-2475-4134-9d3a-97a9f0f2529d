package integration_test

import (
	"context"

	"happyfresh.io/search/core/taxon"
)

type taxonMock struct{}

func (t *taxonMock) GetRank(ctx context.Context, stockLocationId int64, userId string) (map[int64]int64, error) {
	return map[int64]int64{
		48: 2,
		96: 1,
	}, nil
}

func (t *taxonMock) GetUnavailableTaxon(ctx context.Context, stockLocationId int64, countryCode string, taxonIDs []int64) (map[int64]bool, error) {
	return map[int64]bool{}, nil
}

func prepareTaxonRank() (taxon.TaxonService, error) {
	taxonService := &taxonMock{}
	return taxonService, nil
}
