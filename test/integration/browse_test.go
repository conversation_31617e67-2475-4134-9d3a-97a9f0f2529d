package integration_test

import (
	"bytes"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	ext_assert "github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/test/integration/assert"
)

func TestBrowse(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	for _, tc := range []struct {
		name         string
		taxonId      int64
		header       map[string]string
		param        map[string]string
		assertOption []assert.Option
	}{
		{
			"Relevant with popularity sorting",
			212,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q[s]":     "popularity desc",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{99323, 241124, 241155, 241171, 241174, 241151, 241126, 241153, 241128, 241129}),
				assert.TaxonFilterOption([]int64{212, 215, 55}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 1934, "name": "brand name of id 1934-en"},
				}),
				assert.NameAndDecriptionOption("Fried Chicken Skin", "Fried Chicken Skin"),
				assert.CategoriesOption([]string{"The Party 101", "Ready to Eat - Other"}, []int64{212, 215}),
				assert.VendorOption(&rpc.Vendor{Id: 9999, Name: "LotteVendor", Description: "LotteVendor Description", ImageUrl: "http://url/LotteVendor.jpg"}),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with popularity sorting, localized",
			212,
			map[string]string{
				"Locale":              "id",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q[s]":     "popularity desc",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{99323, 241124, 241155, 241171, 241174, 241151, 241126, 241153, 241128, 241129}),
				assert.TaxonFilterOption([]int64{212, 215}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 1934, "name": "brand name of id 1934-id"},
				}),
				assert.NameAndDecriptionOption("Fried Chicken Skin-Localized", "Fried Chicken Skin-Localized"),
				assert.CategoriesOption([]string{"The Party 101-Localized", "Ready to Eat - Other-Localized"}, []int64{212, 215}),
				assert.VendorOption(&rpc.Vendor{Id: 9999, Name: "LotteVendor", Description: "LotteVendor Description-Localized", ImageUrl: "http://url/LotteVendor.jpg"}),
			},
		},
		{
			"Relevant with brand filter",
			212,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":   "10",
				"q[s]":       "popularity desc",
				"brand_id[]": "5518",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{21303}),
				assert.TaxonFilterOption([]int64{212, 215}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 5518, "name": "buavita-en"},
				}),
			},
		},
		{
			"Relevant with pagination",
			212,
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"page":     "2",
				"per_page": "2",
				"q[s]":     "popularity desc",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{241155, 241171}),
				assert.TaxonFilterOption([]int64{212, 215}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 1934, "name": "brand name of id 1934-en"},
				}),
				assert.NameAndDecriptionOption("Cap Gomeh Rice Cake", "Indonesian Traditional Food Made of Rice Cake, Chicken, and Vegetables"),
				assert.CategoriesOption([]string{"The Party 101", "Ready to Eat - Other"}, []int64{212, 215}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := fmt.Sprintf("/api/stock_locations/402/taxons/%d/products", tc.taxonId)

			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param.Set(k, v)
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			ext_assert.Equal(t, http.StatusOK, resp.StatusCode)

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}

func TestGlobalID(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	createVariantIDsParam := func(variantIDs []string) url.Values {
		params := url.Values{}
		params.Set("page", "1")
		params.Set("per_page", "10")
		params.Set("show_oos", "true")
		params.Set("q[s]", "product_id")
		for _, v := range variantIDs {
			params.Add("filter[variant_id][]", v)
		}

		return params
	}

	testCases := []struct {
		name                string
		inputTaxon          int64
		inputVariantIDs     []string
		wantProductID       []int64
		wantProductGlobalID []int64
		wantVariantID       []int64
		wantVariantGlobalID []int64
		wantBrandID         []int64
		wantBrandGlobalID   []int64
		wantImageID         []int64
		wantImageOriginal   []string
	}{
		{
			"Return all legacy ID",
			1049,
			[]string{"281208", "281209"},
			[]int64{284196, 284197},
			[]int64{283956, 283957},
			[]int64{281208, 281209},
			[]int64{280969, 280970},
			[]int64{27483, 27483},
			[]int64{27260, 27260},
			[]int64{},
			[]string{},
		},
		{
			"Image is OK on different IDs",
			1029,
			[]string{"281101"},
			[]int64{284088},
			[]int64{283948},
			[]int64{281101},
			[]int64{280961},
			[]int64{13698},
			[]int64{13698},
			[]int64{1655299, 1655303},
			[]string{
				"a18e3c56acb00a1cc277af7e61dd86e033ff4b41-original.jpg",
				"1bc891cc77ba420cf68bb73bbde0a2af19221e06-original.jpeg",
			},
		},
		{
			"Image is OK",
			55,
			[]string{"43522"},
			[]int64{43548},
			[]int64{43548},
			[]int64{43522},
			[]int64{43522},
			[]int64{4176},
			[]int64{4176},
			[]int64{280733, 280734},
			[]string{
				"5d37c850ce62a77a8cb89c4e7081bfde40432759-original.jpg",
				"845ec6dfda9f7cc434b92b1d0f87988a5dfcc8f4-original.jpg",
			},
		},
	}

	header := http.Header{}
	header.Set("Content-Type", "application/json")
	header.Set("X-Happy-Client-Type", "android")
	header.Set("Synthetic", "true")
	header.Set("Locale", "EN")
	header.Set("Country", "ID")

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := fmt.Sprintf("/api/stock_locations/3/taxons/%d/products", tc.inputTaxon)
			params := createVariantIDsParam(tc.inputVariantIDs)
			req, err := newRequest(http.MethodGet, searchUrl, header, params)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			json := gjson.ParseBytes(buf.Bytes())
			products := json.Get("products").Array()
			productIDs := make([]int64, len(products))
			productGlobalIDs := make([]int64, len(products))
			variantIDs := make([]int64, len(products))
			variantGlobalIDs := make([]int64, len(products))
			brandIDs := make([]int64, len(products))
			brandGlobalIDs := make([]int64, len(products))
			imageIDs := []int64{}
			imageOriginals := []string{}
			for i, p := range products {
				productIDs[i] = p.Get("id").Int()
				productGlobalIDs[i] = p.Get("product_global_id").Int()
				variantIDs[i] = p.Get("variant_id").Int()
				variantGlobalIDs[i] = p.Get("variant_global_id").Int()
				brandIDs[i] = p.Get("brand.id").Int()
				brandGlobalIDs[i] = p.Get("brand_global_id").Int()
				images := p.Get("variants").Array()[0].Get("images").Array()
				for _, image := range images {
					arr := strings.Split(image.Get("original_url").String(), "/attachments/")
					imageIDs = append(imageIDs, image.Get("id").Int())
					imageOriginals = append(imageOriginals, arr[1])
				}
			}
			assert := convey.ShouldEqual(resp.StatusCode, http.StatusOK)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(productIDs, tc.wantProductID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(productGlobalIDs, tc.wantProductGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(variantIDs, tc.wantVariantID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(variantGlobalIDs, tc.wantVariantGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(brandIDs, tc.wantBrandID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(brandGlobalIDs, tc.wantBrandGlobalID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(imageIDs, tc.wantImageID)
			if assert != "" {
				t.Error(assert)
			}
			assert = convey.ShouldResemble(imageOriginals, tc.wantImageOriginal)
			if assert != "" {
				t.Error(assert)
			}
		})
	}

}

func TestProductProperties(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	tt := []struct {
		name                         string
		inputTaxon                   int64
		inputVariantIDs              []string
		inputLocale                  string
		expectedProductNames         []string
		expectedProductDescriptions  []string
		expectedSlugs                []string
		expectedMOQ                  []int64
		expectedDisplayUnit          []string
		expectedDisplayAverageWeight []string
		expectedTaxonNames           [][]string
		expectedSKU                  []string
		expectedBrandNames           []string
		expectedPrices               []string
		expectedUnitPrices           []string
		expectedInStock              []bool
	}{
		{
			"Returns properties from catalog",
			55,
			[]string{"43522"},
			"ID",
			[]string{"Dimes Jus Jeruk"},
			[]string{"Minuman Jus Jeruk Tanpa Gula Tambahan"},
			[]string{"dimes-orange-juice"},
			// // TODO: Re-enable MoQ in HFS once stock from pricing is consistent again
			// []int64{5},
			[]int64{50},
			[]string{"1 L"},
			[]string{"~ 1 L"},
			[][]string{{"Jus & Minuman Kesehatan", "Minuman"}},
			[]string{"8690558018057-ID"},
			[]string{"Dimes-ID"},
			[]string{"44000"},
			[]string{"4400"},
			[]bool{true},
		},
	}

	header := http.Header{}
	header.Set("Content-Type", "application/json")
	header.Set("X-Happy-Client-Type", "android")
	header.Set("Synthetic", "true")
	header.Set("Country", "ID")

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := fmt.Sprintf("/api/stock_locations/3/taxons/%d/products", tc.inputTaxon)
			params := createVariantIDsParam(tc.inputVariantIDs)
			header.Set("Locale", tc.inputLocale)
			req, err := newRequest(http.MethodGet, searchUrl, header, params)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			json := gjson.ParseBytes(buf.Bytes())
			products := json.Get("products").Array()

			for i, p := range products {
				name := p.Get("name").String()
				assert := convey.ShouldEqual(name, tc.expectedProductNames[i])
				if assert != "" {
					t.Error(assert)
				}
				desc := p.Get("description").String()
				assert = convey.ShouldEqual(desc, tc.expectedProductDescriptions[i])
				if assert != "" {
					t.Error(assert)
				}
				slug := p.Get("slug").String()
				assert = convey.ShouldEqual(slug, tc.expectedSlugs[i])
				if assert != "" {
					t.Error(assert)
				}
				moq := p.Get("max_order_quantity").Int()
				assert = convey.ShouldEqual(moq, tc.expectedMOQ[i])
				if assert != "" {
					t.Error(assert)
				}
				du := p.Get("display_unit").String()
				assert = convey.ShouldEqual(du, tc.expectedDisplayUnit[i])
				if assert != "" {
					t.Error(assert)
				}
				avgWeight := p.Get("display_average_weight").String()
				assert = convey.ShouldEqual(avgWeight, tc.expectedDisplayAverageWeight[i])
				if assert != "" {
					t.Error(assert)
				}
				categories := p.Get("categories").Array()
				for j, c := range categories {
					taxonName := c.String()
					assert = convey.ShouldEqual(taxonName, tc.expectedTaxonNames[i][j])
					if assert != "" {
						t.Error(assert)
					}
				}
				sku := p.Get("variants").Array()[0].Get("sku").String()
				assert = convey.ShouldEqual(sku, tc.expectedSKU[i])
				if assert != "" {
					t.Error(assert)
				}
				brand := p.Get("brand.name").String()
				assert = convey.ShouldEqual(brand, tc.expectedBrandNames[i])
				if assert != "" {
					t.Error(assert)
				}
				price := p.Get("price").String()
				assert = convey.ShouldEqual(price, tc.expectedPrices[i])
				if assert != "" {
					t.Error(assert)
				}
				unitPrice := p.Get("unit_price").String()
				assert = convey.ShouldEqual(unitPrice, tc.expectedUnitPrices[i])
				if assert != "" {
					t.Error(assert)
				}
				inStock := p.Get("in_stock").Bool()
				assert = convey.ShouldEqual(inStock, tc.expectedInStock[i])
				if assert != "" {
					t.Error(assert)
				}
			}
		})
	}
}

func createVariantIDsParam(variantIDs []string) url.Values {
	params := url.Values{}
	params.Set("page", "1")
	params.Set("per_page", "10")
	params.Set("show_oos", "true")
	params.Set("q[s]", "product_id")
	for _, v := range variantIDs {
		params.Add("filter[variant_id][]", v)
	}

	return params
}
