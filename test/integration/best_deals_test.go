package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	ext_assert "github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"happyfresh.io/search/test/integration/assert"
)

func TestBestDealsNew(t *testing.T) {
	if testing.Short() {
		t.Ski<PERSON>ow()
	}

	for _, tc := range []struct {
		name         string
		header       map[string]string
		param        map[string]string
		assertOption []assert.Option
	}{
		{
			"Relevant with popularity sorting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":             "10",
				"q[s]":                 "popularity desc",
				"promotion_type[name]": "discount",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{124172, 84287, 94515}),
				assert.TaxonFilterOption([]int64{12, 109}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 7941, "name": "brand name of id 7941-en"},
				}),
				assert.NameAndDecriptionOption("Integration test product name of ID 124172", "Integration test product desc of ID 124172"),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with popularity sorting, localized",
			map[string]string{
				"Locale":              "id",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":             "10",
				"q[s]":                 "popularity desc",
				"promotion_type[name]": "discount",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{124172, 84287, 94515}),
				assert.TaxonFilterOption([]int64{12, 109}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 7941, "name": "brand name of id 7941-id"},
				}),
				assert.NameAndDecriptionOption("Integration test product name of ID 124172-Localized", "Integration test product desc of ID 124172-Localized"),
			},
		},
		{
			"Relevant with popularity sorting, localized, paginated",
			map[string]string{
				"Locale":              "id",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"page":                 "2",
				"per_page":             "2",
				"q[s]":                 "popularity desc",
				"promotion_type[name]": "discount",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{94515}),
				assert.TaxonFilterOption([]int64{12, 109}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 7941, "name": "brand name of id 7941-id"},
				}),
			},
		},
		{
			"Relevant with popularity sorting, localized, sorted by name",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":             "10",
				"q[s]":                 "name asc",
				"promotion_type[name]": "discount",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{94515, 84287, 124172}),
			},
		},
		{
			"Relevant with popularity sorting, localized, filtered by brand",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":             "10",
				"promotion_type[name]": "discount",
				"brand_id[]":           "9010",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{124172}),
			},
		},
		{
			"Relevant with popularity sorting, localized, filtered by taxon",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":             "10",
				"promotion_type[name]": "discount",
				"taxon_id[]":           "130",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{124172}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			searchUrl := "/api/stock_locations/402/products"

			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			param.Set("category", "special")
			for k, v := range tc.param {
				param.Set(k, v)
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			ext_assert.Equal(t, http.StatusOK, resp.StatusCode)

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}

func TestBestDeals(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	convey.Convey("Given the Best Deals API", t, func() {
		searchURl := "/api/stock_locations/520/products/"

		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("X-Happy-Client-Type", "android")
		header.Set("Synthetic", "true")
		header.Set("Locale", "EN")
		header.Set("Country", "ID")

		params := url.Values{}
		params.Set("page", "1")
		params.Set("per_page", "30")

		convey.Convey("When search for Free Delivery promo type", func() {
			params.Set("promotion_type[name]", "free_shipping")

			convey.Convey("Should return products with promo type", func() {
				req, err := newRequest(http.MethodGet, searchURl, header, params)
				if err != nil {
					t.Error(err)
				}

				resp, err := httpClient.Do(req)
				if err != nil {
					t.Error(err)
				}

				buf := &bytes.Buffer{}
				_, err = buf.ReadFrom(resp.Body)
				defer resp.Body.Close()
				if err != nil {
					t.Error(err)
				}

				json := gjson.ParseBytes(buf.Bytes())
				products := json.Get("products").Array()

				convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusOK)
				convey.So(len(products), convey.ShouldEqual, 2)

				productIDs := []int{}
				for _, product := range products {
					productIDs = append(productIDs, int(product.Get("id").Int()))
				}

				for _, v := range []interface{}{283077, 283221} {
					convey.So(productIDs, convey.ShouldContain, v)
				}
			})
		})

		convey.Convey("When search for BXGY promo type", func() {
			params.Set("promotion_type[name]", "buy_x_get_y")

			convey.Convey("Should return products with promo type", func() {
				req, err := newRequest(http.MethodGet, searchURl, header, params)
				if err != nil {
					t.Error(err)
				}

				resp, err := httpClient.Do(req)
				if err != nil {
					t.Error(err)
				}

				buf := &bytes.Buffer{}
				_, err = buf.ReadFrom(resp.Body)
				defer resp.Body.Close()
				if err != nil {
					t.Error(err)
				}

				json := gjson.ParseBytes(buf.Bytes())
				products := json.Get("products").Array()

				convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusOK)
				convey.So(len(products), convey.ShouldEqual, 3)

				productIDs := []int{}
				for _, product := range products {
					productIDs = append(productIDs, int(product.Get("id").Int()))
				}

				for _, v := range []interface{}{283076, 283214, 283204} {
					convey.So(productIDs, convey.ShouldContain, v)
				}
			})
		})

		convey.Convey("When include promotion filter", func() {
			params.Set("promotion_filter", "true")

			convey.Convey("Should return promotion type filters", func() {
				req, err := newRequest(http.MethodGet, searchURl, header, params)
				if err != nil {
					t.Error(err)
				}

				resp, err := httpClient.Do(req)
				if err != nil {
					t.Error(err)
				}

				buf := &bytes.Buffer{}
				_, err = buf.ReadFrom(resp.Body)
				defer resp.Body.Close()
				if err != nil {
					t.Error(err)
				}

				json := gjson.ParseBytes(buf.Bytes())
				promotionTypeFilters := json.Get("filters").Get("promotion_types").Array()
				convey.So(len(promotionTypeFilters), convey.ShouldBeGreaterThan, 0)

				for _, promotionType := range promotionTypeFilters {
					switch promotionType.Get("type").String() {
					case "buy_x_get_y":
						count := promotionType.Get("total").Int()
						convey.So(count, convey.ShouldEqual, 3)
					case "free_shipping":
						count := promotionType.Get("total").Int()
						convey.So(count, convey.ShouldEqual, 4)
					case "discount":
						count := promotionType.Get("total").Int()
						convey.So(count, convey.ShouldEqual, 3)
					}
				}
			})
		})

		convey.Convey("When exclude promotion filter", func() {
			convey.Convey("Should not return promotion type filters", func() {
				req, err := newRequest(http.MethodGet, searchURl, header, params)
				if err != nil {
					t.Error(err)
				}

				resp, err := httpClient.Do(req)
				if err != nil {
					t.Error(err)
				}

				buf := &bytes.Buffer{}
				_, err = buf.ReadFrom(resp.Body)
				defer resp.Body.Close()
				if err != nil {
					t.Error(err)
				}

				json := gjson.ParseBytes(buf.Bytes())
				promotionTypeFilters := json.Get("filters").Get("promotion_types").Array()

				convey.So(len(promotionTypeFilters), convey.ShouldEqual, 0)
			})
		})
	})
}
