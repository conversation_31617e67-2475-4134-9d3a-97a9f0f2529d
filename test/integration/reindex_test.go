package integration_test

import (
	"bytes"
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/search/api/middleware"

	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/pricing"
	pricingSync "happyfresh.io/search/core/pricing/sync"
	"happyfresh.io/search/worker"
)

func TestIndexQueueing(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	tt := []struct {
		name        string
		indexType   string
		countryCode string
		body        string
	}{
		{
			"index product ID", "index_product", "id", "",
		},
		{
			"index product TH", "index_product", "TH", "",
		},
		{
			"index store product ID", "index_stock_item", "Id", "",
		},
		{
			"index store product TH", "index_stock_item", "th", "",
		},
		{
			"index update store products ID", "update_stock_item", "ID", `{"id":12345}`,
		},
		{
			"index update store products TH", "update_stock_item", "th", `{"id":12345}`,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			uri := func(t string) string {
				switch t {
				case "index_product":
					return "products/12345"
				case "index_stock_item":
					return "store_products/12345"
				default:
					return "store_products"
				}
			}(tc.indexType)

			header := http.Header{}
			header.Set("Content-Type", "application/json")
			header.Set("X-Country-Code", strings.ToUpper(tc.countryCode))

			searchUrl := fmt.Sprintf("/api/v3/documents/%s", uri)

			req, err := newRequest(http.MethodPost, searchUrl, header, nil)
			if err != nil {
				t.Error(err)
			}

			if tc.body != "" {
				buf := &bytes.Buffer{}
				buf.WriteString(tc.body)

				req.Body = ioutil.NopCloser(buf)
			}

			countryCode := strings.ToLower(tc.countryCode)
			q := func(c string) string {
				if len(c) == 0 {
					return fmt.Sprintf("rmq::queue::[%s]::ready", tc.indexType)
				}

				return fmt.Sprintf("rmq::queue::[%s_%s]::ready", tc.indexType, c)
			}(countryCode)

			llenBefore, err := redisClient.LLen(q).Result()
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			llenAfter, err := redisClient.LLen(q).Result()
			if err != nil {
				t.Error(err)
			}

			assert := convey.ShouldBeBetweenOrEqual(resp.StatusCode, http.StatusCreated, http.StatusOK)
			if assert != "" {
				t.Error(assert)
			}

			assert = convey.ShouldEqual(llenAfter-llenBefore, 1)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestReindexStockItem(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	convey.Convey("Given Stock Item Reindex API", t, func() {
		searchURl := "/api/v3/documents/store_products/12345"

		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("X-Country-Code", "ID")

		convey.Convey("Should append reindex job to redis queue", func() {
			req, err := newRequest(http.MethodPost, searchURl, header, nil)
			if err != nil {
				t.Error(err)
			}

			llenBefore, err := redisClient.LLen("rmq::queue::[index_stock_item_id]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			llenAfter, err := redisClient.LLen("rmq::queue::[index_stock_item_id]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusCreated)
			convey.So(llenAfter-llenBefore, convey.ShouldEqual, 1)
		})
	})
}

func TestReindexProduct(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	convey.Convey("Given Product Reindex API", t, func() {
		searchURl := "/api/v3/documents/products/1234567"

		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("X-Country-Code", "ID")

		convey.Convey("Should append reindex job to redis queue", func() {
			req, err := newRequest(http.MethodPost, searchURl, header, nil)
			if err != nil {
				t.Error(err)
			}

			llenBefore, err := redisClient.LLen("rmq::queue::[index_product_id]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			llenAfter, err := redisClient.LLen("rmq::queue::[index_product_id]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusCreated)
			convey.So(llenAfter-llenBefore, convey.ShouldEqual, 1)
		})
	})
}

func TestUpdateStockItem(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	convey.Convey("Given Product Reindex API", t, func() {
		searchURl := "/api/v3/documents/store_products"

		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("X-Country-Code", "ID")

		convey.Convey("Should append reindex job to redis queue", func() {
			req, err := newRequest(http.MethodPost, searchURl, header, nil)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			buf.WriteString(`{"id":12345}`)

			req.Body = ioutil.NopCloser(buf)

			llenBefore, err := redisClient.LLen("rmq::queue::[update_stock_item_id]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			llenAfter, err := redisClient.LLen("rmq::queue::[update_stock_item_id]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusOK)
			convey.So(llenAfter-llenBefore, convey.ShouldEqual, 1)
		})
	})
}

func TestOldAPIDeprecated(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	convey.Convey("Given old index APIs", t, func() {
		header := http.Header{}
		header.Set("Content-Type", "application/json")
		header.Set("X-Country-Code", "ID")

		convey.Convey("Given index product API should return 200", func() {
			searchUrl := "/api/v3/products/12345"
			req, err := newRequest(http.MethodPost, searchUrl, header, nil)
			if err != nil {
				t.Error(err)
			}

			llenBefore, err := redisClient.LLen("rmq::queue::[index_stock_item]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			llenAfter, err := redisClient.LLen("rmq::queue::[index_stock_item]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusCreated)
			convey.So(llenAfter, convey.ShouldEqual, llenBefore)
		})

		convey.Convey("Given index stock items API should return 200", func() {
			searchUrl := "/api/v3/products/reindex/12345"
			req, err := newRequest(http.MethodPost, searchUrl, header, nil)
			if err != nil {
				t.Error(err)
			}

			llenBefore, err := redisClient.LLen("rmq::queue::[index_product]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			llenAfter, err := redisClient.LLen("rmq::queue::[index_product]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusCreated)
			convey.So(llenAfter, convey.ShouldEqual, llenBefore)
		})

		convey.Convey("Given update stock items API should return 200", func() {
			searchUrl := "/api/v3/products/reindex"
			req, err := newRequest(http.MethodPost, searchUrl, header, nil)
			if err != nil {
				t.Error(err)
			}

			llenBefore, err := redisClient.LLen("rmq::queue::[update_stock_item]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			llenAfter, err := redisClient.LLen("rmq::queue::[update_stock_item]::ready").Result()
			if err != nil {
				t.Error(err)
			}

			convey.So(resp.StatusCode, convey.ShouldEqual, http.StatusCreated)
			convey.So(llenAfter, convey.ShouldEqual, llenBefore)
		})
	})
}

func TestSyncOnSearchIndexQueueing(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	tt := []struct {
		name                string
		indexSearchResponse *index.SearchResponse
		products            []*cat.StoreProducts
		priceresp           map[string]*pricing.Product
		countryCode         string
	}{
		{
			"update to queue ID",
			&index.SearchResponse{
				StockItems: []*index.StockItem{
					{
						InStock: "true",
					},
				},
			},
			[]*cat.StoreProducts{
				{
					ProductDetail: &cat.ProductDetail{
						Variants: []*cat.Variant{
							{
								Sku: "12345-ID",
							},
						},
						Properties: &cat.Properties{},
					},
					StoreProducts: []*cat.StoreProduct{
						{
							StoreId: 1,
						},
					},
				},
			},
			map[string]*pricing.Product{
				"12345": {
					Price: &pricing.Price{
						PromoPrice: 15000,
					},
					InStock: false,
				},
			},
			"ID",
		},
		{
			"update to queue TH",
			&index.SearchResponse{
				StockItems: []*index.StockItem{
					{
						InStock: "true",
					},
				},
			},
			[]*cat.StoreProducts{
				{
					ProductDetail: &cat.ProductDetail{
						Variants: []*cat.Variant{
							{
								Sku: "12345-TH",
							},
						},
						Properties: &cat.Properties{},
					},
					StoreProducts: []*cat.StoreProduct{
						{
							StoreId: 1,
						},
					},
				},
			},
			map[string]*pricing.Product{
				"12345": {
					Price: &pricing.Price{
						PromoPrice: 15000,
					},
					InStock: false,
				},
			},
			"th",
		},
	}

	ctx := worker.WithClient(context.Background(), coco)

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			countryCode := strings.ToLower(tc.countryCode)
			q := func(c string) string {
				if len(c) == 0 {
					return "rmq::queue::[update_stock_item]::ready"
				}

				return fmt.Sprintf("rmq::queue::[update_stock_item_%s]::ready", c)
			}(countryCode)

			llenBefore, err := redisClient.LLen(q).Result()
			if err != nil {
				t.Error(err)
			}

			cctx := middleware.NewContext(ctx, "currency", "idr")
			err = pricingSync.Sync(cctx, tc.indexSearchResponse, tc.products, tc.priceresp, "android", tc.countryCode)
			if err != nil {
				t.Error(err)
			}

			llenAfter, err := redisClient.LLen(q).Result()
			if err != nil {
				t.Error(err)
			}

			assert := convey.ShouldEqual(llenAfter-llenBefore, 1)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}
