package integration_test

import (
	"bytes"
	"net/http"
	"net/url"
	"testing"

	"github.com/tidwall/gjson"
	"happyfresh.io/search/api/handler"
	ssrv "happyfresh.io/search/api/handler"
	"happyfresh.io/search/test/integration/assert"
)

func TestTaxonRestriction(t *testing.T) {
	if testing.Short() {
		t.Ski<PERSON>ow()
	}

	for _, tc := range []struct {
		name               string
		url                string
		restrictedTaxonIDs []int64
		header             map[string]string
		param              map[string]string
		assertOption       []assert.Option
	}{
		{
			"Assert in search API",
			"/api/stock_locations/106/products/search",
			[]int64{34, 44},
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "beras",
			},
			[]assert.Option{
				assert.TaxonNoneFilter([]int64{34, 44}),
			},
		},
		{
			"Assert in browse API",
			"/api/stock_locations/106/taxons/212/products",
			[]int64{},
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q[s]":     "popularity desc",
			},
			[]assert.Option{
				assert.TaxonNoneFilter([]int64{55}),
			},
		},
		{
			"Uses catalog config if empty",
			"/api/stock_locations/402/taxons/212/products",
			[]int64{55},
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q[s]":     "popularity desc",
			},
			[]assert.Option{
				assert.TaxonFilterOption([]int64{212, 215, 53, 55, 732}),
			},
		},
		{
			"Uses envar config taxon restriction if catalog error",
			"/api/stock_locations/102/taxons/212/products",
			[]int64{55},
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q[s]":     "popularity desc",
			},
			[]assert.Option{
				assert.TaxonNoneFilter([]int64{55}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			apply := ssrv.WithTaxonNoneFilter(tc.restrictedTaxonIDs)
			apply(searchService.(*handler.SearchService))

			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param.Set(k, v)
			}

			req, err := newRequest(http.MethodGet, tc.url, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}
