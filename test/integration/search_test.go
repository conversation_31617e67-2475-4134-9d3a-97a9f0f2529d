package integration_test

import (
	"bytes"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"testing"
	"time"

	"github.com/alicebob/miniredis"
	"github.com/tidwall/gjson"

	_assert "github.com/stretchr/testify/assert"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/core/pricing"
	"happyfresh.io/search/core/session"
	"happyfresh.io/search/test/integration/assert"
)

func TestSearch(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	searchUrl := "/api/stock_locations/402/products/search"

	for _, tc := range []struct {
		name         string
		header       map[string]string
		param        map[string]string
		assertOption []assert.Option
	}{
		{
			"Relevant with product type, personalization, and raw popularity boosting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "beras",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				// assert.TaxonFilterOrderOption([]int64{34, 44, 60, 88, 64, 90}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 21806, "name": "hotel-en"},
					{"id": 3020, "name": "fantastic-en"},
					{"id": 4279, "name": "pure green-en"},
				}),
				assert.SearchjoyOption(&datastore.SearchjoyRecord{
					Query:           "beras",
					NormalizedQuery: "beras",
					CountryIso:      "id",
					Locale:          "en",
					StockLocationId: 402,
					UserId:          544025,
					Properties: map[string]string{
						"page":                       "1",
						"channel":                    "android",
						"taxon_ids":                  "",
						"valid_synonym":              "false",
						"enhanced_query":             "beras",
						"search_variant":             "",
						"static_synonym":             "false",
						"appsearch_query":            "true",
						"product_type_id":            "5432,5431,5426",
						"transformed_query":          "false",
						"product_type_leaves":        "5432",
						"enhanced_query_terms":       "beras",
						"user_history_product":       "8997025140144-ID,8885001727017-ID",
						"original_normalized_query":  "beras",
						"corrected_normalized_query": "beras",
					},
				}),
				assert.NameAndDecriptionOption("Hotel Organic Black Rice", "Organic Black Rice"),
				assert.BrandOption(&rpc.Brand{Id: 21806, Name: "hotel"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods", "Grains, Rice & Dried Goods"}, []int64{34, 44}),
				assert.VendorOption(&rpc.Vendor{Id: 9999, Name: "LotteVendor", Description: "LotteVendor Description", ImageUrl: "http://url/LotteVendor.jpg"}),
			},
		},
		{
			"Relevant with product type, personalization, raw popularity boosting and localized",
			map[string]string{
				"Locale":              "ID",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "beras",
			},
			[]assert.Option{
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 21806, "name": "hotel-id"},
					{"id": 3020, "name": "fantastic-id"},
					{"id": 4279, "name": "pure green-id"},
				}),
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				assert.NameAndDecriptionOption("Hotel Organic Black Rice-Localized", "Organic Black Rice-Localized"),
				assert.BrandOption(&rpc.Brand{Id: 21806, Name: "hotel-Localized"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods-Localized", "Grains, Rice & Dried Goods-Localized"}, []int64{34, 44}),
				assert.VendorOption(&rpc.Vendor{Id: 9999, Name: "LotteVendor", Description: "LotteVendor Description-Localized", ImageUrl: "http://url/LotteVendor.jpg"}),
			},
		},
		{
			"Relevant with synonym, product type, personalization, and raw popularity boosting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "synonymberas",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				assert.SearchjoyOption(&datastore.SearchjoyRecord{
					Query:           "synonymberas",
					NormalizedQuery: "synonymberas",
					CountryIso:      "id",
					Locale:          "en",
					StockLocationId: 402,
					UserId:          544025,
					Properties: map[string]string{
						"page":                       "1",
						"channel":                    "android",
						"taxon_ids":                  "",
						"valid_synonym":              "true",
						"enhanced_query":             `"beras" "synonymberas"`,
						"search_variant":             "",
						"static_synonym":             "false",
						"appsearch_query":            "false",
						"product_type_id":            "5432,5431,5426",
						"transformed_query":          "false",
						"product_type_leaves":        "5432",
						"enhanced_query_terms":       "synonymberas,beras",
						"user_history_product":       "8997025140144-ID,8885001727017-ID",
						"original_normalized_query":  "synonymberas",
						"corrected_normalized_query": "synonymberas",
					},
				}),
			},
		},
		{
			"Relevant with correction, product type, personalization, and raw popularity boosting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "typobrs",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				assert.SearchjoyOption(&datastore.SearchjoyRecord{
					Query:           "typobrs",
					NormalizedQuery: "typobrs",
					CountryIso:      "id",
					Locale:          "en",
					StockLocationId: 402,
					UserId:          544025,
					Properties: map[string]string{
						"page":                       "1",
						"channel":                    "android",
						"taxon_ids":                  "",
						"valid_synonym":              "false",
						"enhanced_query":             "beras",
						"search_variant":             "",
						"static_synonym":             "false",
						"appsearch_query":            "true",
						"product_type_id":            "5432,5431,5426",
						"transformed_query":          "false",
						"product_type_leaves":        "5432",
						"enhanced_query_terms":       "beras",
						"user_history_product":       "8997025140144-ID,8885001727017-ID",
						"original_normalized_query":  "typobrs",
						"corrected_normalized_query": "beras",
					},
				}),
				assert.QueryCorrectionOption(map[string]string{
					"before": "typobrs",
					"after":  "beras",
				}),
			},
		},
		{
			"Relevant with correction, synonym, product type, personalization, and raw popularity boosting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "typobrs2",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				assert.SearchjoyOption(&datastore.SearchjoyRecord{
					Query:           "typobrs2",
					NormalizedQuery: "typobrs2",
					CountryIso:      "id",
					Locale:          "en",
					StockLocationId: 402,
					UserId:          544025,
					Properties: map[string]string{
						"page":                       "1",
						"channel":                    "android",
						"taxon_ids":                  "",
						"valid_synonym":              "true",
						"enhanced_query":             `"beras" "synonymberas"`,
						"search_variant":             "",
						"static_synonym":             "false",
						"appsearch_query":            "false",
						"product_type_id":            "5432,5431,5426",
						"transformed_query":          "false",
						"product_type_leaves":        "5432",
						"enhanced_query_terms":       "synonymberas,beras",
						"user_history_product":       "8997025140144-ID,8885001727017-ID",
						"original_normalized_query":  "typobrs2",
						"corrected_normalized_query": "synonymberas",
					},
				}),
			},
		},
		{
			"Relevant without any boosting",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "snd",
			},
			map[string]string{
				"per_page": "10",
				"q":        "beras",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{80390, 150449, 17157, 16117, 29003, 70776, 30680, 26763, 26939, 19991}),
				assert.TaxonFilterOption([]int64{}),
				assert.BrandFilterOption([]map[string]interface{}{}),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with product type, personalization, and raw popularity boosting but is synthetic",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				"Synthetic":           "true",
			},
			map[string]string{
				"per_page": "10",
				"q":        "beras",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{30679, 24649, 150449, 203224, 251462, 24650, 24699, 98449, 14214, 14877}),
				assert.SearchjoyNotTrackedOption(),
			},
		},
		{
			"Relevant with fuzziness and taxon filter",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":   "10",
				"q":          "beras",
				"taxon_id[]": "55",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{29003, 20534, 20535}),
				assert.SearchjoyOption(&datastore.SearchjoyRecord{
					Query:           "beras",
					NormalizedQuery: "beras",
					CountryIso:      "id",
					Locale:          "en",
					StockLocationId: 402,
					UserId:          544025,
					Properties: map[string]string{
						"page":                       "1",
						"channel":                    "android",
						"taxon_ids":                  "55",
						"valid_synonym":              "false",
						"enhanced_query":             "beras",
						"search_variant":             "",
						"static_synonym":             "false",
						"appsearch_query":            "true",
						"product_type_id":            "5432,5431,5426",
						"transformed_query":          "false",
						"product_type_leaves":        "5432",
						"enhanced_query_terms":       "beras",
						"user_history_product":       "8997025140144-ID,8885001727017-ID",
						"original_normalized_query":  "beras",
						"corrected_normalized_query": "beras",
					},
				}),
			},
		},
		{
			"Relevant with fuzziness and brand filter",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":   "10",
				"q":          "beras",
				"brand_id[]": "6376",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{24650, 24651, 36729, 24652}),
			},
		},
		{
			"Relevant with pagination",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"page":     "2",
				"per_page": "2",
				"q":        "beras",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{150449, 203224}),
				assert.TaxonFilterOption([]int64{34, 44, 60, 88, 64}),
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 21806, "name": "hotel-en"},
					{"id": 3020, "name": "fantastic-en"},
					{"id": 4279, "name": "pure green-en"},
				}),
				assert.NameAndDecriptionOption("Hoki White Rice", "Qualified White Rice From Selected Rice Plant"),
				assert.BrandOption(&rpc.Brand{Id: 23077, Name: "hoki"}),
				assert.CategoriesOption([]string{"Dry & Canned Goods", "Grains, Rice & Dried Goods"}, []int64{34, 44}),
			},
		},
		{
			"Relevant with sort by name asc",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page": "10",
				"q":        "beras",
				"sort":     "name asc",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{79688, 30680, 108182, 70914, 21262, 21890}),
			},
		},
		{
			"Relevant with fuzziness, taxon filter, sort by price",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "snd",
				"x-order-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":   "10",
				"q":          "beras",
				"taxon_id[]": "55",
				"q[s]":       "price desc",
			},
			[]assert.Option{
				assert.RelevancyOption([]int64{20535, 20534, 29003}),
				assert.PricingOption([]*pricing.Product{
					{
						Code:    "8997025140212",
						StoreID: 4020,
						InStock: true,
						Price: &pricing.Price{
							NormalPrice:      100000,
							PromoPrice:       100000,
							StoreNormalPrice: 100000,
							StorePromoPrice:  100000,
							ClientTypePromoPrice: map[string]float64{
								"android": 89300,
								"ios":     90000,
							},
						},
						Promotions:     []*pricing.Promotion{},
						VestaInStock:   false,
						VestaUpdatedAt: "",
					},
					{
						Code:    "8997025140205",
						StoreID: 4020,
						InStock: true,
						Price: &pricing.Price{
							NormalPrice:      60000,
							PromoPrice:       60000,
							StoreNormalPrice: 60000,
							StorePromoPrice:  60000,
							ClientTypePromoPrice: map[string]float64{
								"android": 57800,
								"ios":     58000,
							},
						},
						Promotions:     []*pricing.Promotion{},
						VestaInStock:   false,
						VestaUpdatedAt: "",
					},
				}, "android"),
			},
		},
		{
			"Test search intention",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":       "10",
				"q":              "oreo",
				"search_variant": "B",
			},
			[]assert.Option{
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 22015, "name": "sari roti-en"},
					{"id": 16844, "name": "biskuat-en"},
				}),
				assert.SearchjoyOption(&datastore.SearchjoyRecord{
					Query:           "oreo",
					NormalizedQuery: "oreo",
					CountryIso:      "id",
					Locale:          "en",
					StockLocationId: 402,
					UserId:          544025,
					Properties: map[string]string{
						"page":                       "1",
						"channel":                    "android",
						"valid_synonym":              "false",
						"enhanced_query":             "oreo",
						"search_variant":             "B",
						"static_synonym":             "false",
						"transformed_query":          "false",
						"enhanced_query_terms":       "oreo",
						"original_normalized_query":  "oreo",
						"corrected_normalized_query": "oreo",
						"sintent_processed_term":     "oreo",
						"sintent_collection_used":    "search_intention_static",
						"sintent_attributes":         "[{\"product_type_ids\":[2820],\"brand_ids\":[22015],\"score\":1},{\"brand_ids\":[16844],\"score\":1}]",
					},
				}),
			},
		},
		{
			"Test search intention with correction",
			map[string]string{
				"Locale":              "en",
				"Country":             "id",
				"x-happy-client-type": "android",
				"X-Spree-Token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
			},
			map[string]string{
				"per_page":       "10",
				"q":              "typooreo",
				"search_variant": "B",
			},
			[]assert.Option{
				assert.BrandFilterOption([]map[string]interface{}{
					{"id": 22015, "name": "sari roti-en"},
					{"id": 16844, "name": "biskuat-en"},
				}),
				assert.SearchjoyOption(&datastore.SearchjoyRecord{
					Query:           "typooreo",
					NormalizedQuery: "typooreo",
					CountryIso:      "id",
					Locale:          "en",
					StockLocationId: 402,
					UserId:          544025,
					Properties: map[string]string{
						"page":                       "1",
						"channel":                    "android",
						"valid_synonym":              "false",
						"enhanced_query":             "oreo",
						"search_variant":             "B",
						"static_synonym":             "false",
						"transformed_query":          "false",
						"enhanced_query_terms":       "oreo",
						"original_normalized_query":  "typooreo",
						"corrected_normalized_query": "oreo",
						"sintent_processed_term":     "oreo",
						"sintent_collection_used":    "search_intention_static",
						"sintent_attributes":         "[{\"product_type_ids\":[2820],\"brand_ids\":[22015],\"score\":1},{\"brand_ids\":[16844],\"score\":1}]",
					},
				}),
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			header := http.Header{}
			header.Set("Content-Type", "application/json")
			for k, v := range tc.header {
				header.Set(k, v)
			}

			param := url.Values{}
			for k, v := range tc.param {
				param.Set(k, v)
			}

			req, err := newRequest(http.MethodGet, searchUrl, header, param)
			if err != nil {
				t.Error(err)
			}

			resp, err := httpClient.Do(req)
			if err != nil {
				t.Error(err)
			}

			buf := &bytes.Buffer{}
			_, err = buf.ReadFrom(resp.Body)
			defer resp.Body.Close()
			if err != nil {
				t.Error(err)
			}

			root := gjson.ParseBytes(buf.Bytes())
			for _, doAssert := range tc.assertOption {
				doAssert(t, root)
			}
		})
	}
}

func TestQueryID(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	mr, err := miniredis.Run()
	if err != nil {
		t.Error(err)
		return
	}

	session.Setup("redis://"+mr.Addr(), 20*time.Second)

	for _, tc := range []struct {
		name             string
		stockLocationIDs []int64
		headers          []map[string]string
		params           []map[string]string
		sleepTime        time.Duration
		assertFunc       func(assertion *_assert.Assertions, queryID1 string, queryID2 string)
	}{
		{
			"returns same query id when params are the same and within sleep time and different page",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page": "10",
					"page":     "2",
					"q":        "beras",
				},
			},
			5 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.Equal(queryID1, queryID2)
			},
		},
		{
			"returns same query id when params are the same and within sleep time and different page and the same user token",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-spree-token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-spree-token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page": "10",
					"page":     "2",
					"q":        "beras",
				},
			},
			5 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.Equal(queryID1, queryID2)
			},
		},
		{
			"returns different query id when params are the same and not within sleep time",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
			},
			20 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.NotEqual(queryID1, queryID2)
			},
		},
		{
			"returns different query id when taxon filter different and within sleep time",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
					"taxon_id": "5",
				},
			},
			5 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.NotEqual(queryID1, queryID2)
			},
		},
		{
			"returns different query id when brand filter different and within sleep time",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page":   "10",
					"page":       "1",
					"q":          "beras",
					"brand_id[]": "5",
				},
			},
			5 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.NotEqual(queryID1, queryID2)
			},
		},
		{
			"returns different query id when sort different and within sleep time",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
					"sort":     "name asc",
				},
			},
			5 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.NotEqual(queryID1, queryID2)
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			mr.FastForward(21 * time.Second)

			queryIDs := []string{}
			for i, stockLocationID := range tc.stockLocationIDs {
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				for k, v := range tc.headers[i] {
					header.Set(k, v)
				}

				param := url.Values{}
				for k, v := range tc.params[i] {
					param.Set(k, v)
				}

				searchUrl := fmt.Sprintf("/api/stock_locations/%d/products/search", stockLocationID)
				req, err := newRequest(http.MethodGet, searchUrl, header, param)
				if err != nil {
					t.Error(err)
				}

				resp, err := httpClient.Do(req)
				if err != nil {
					t.Error(err)
				}

				buf := &bytes.Buffer{}
				_, err = buf.ReadFrom(resp.Body)
				defer resp.Body.Close()
				if err != nil {
					t.Error(err)
				}

				root := gjson.ParseBytes(buf.Bytes())
				queryIDs = append(queryIDs, root.Get("meta").Get("query_id").String())

				mr.FastForward(tc.sleepTime)
			}

			assertion := _assert.New(t)
			assertion.Equal(2, len(queryIDs))

			tc.assertFunc(assertion, queryIDs[0], queryIDs[1])
		})
	}
}

func TestSessionID(t *testing.T) {
	if testing.Short() {
		t.SkipNow()
	}

	mr, err := miniredis.Run()
	if err != nil {
		t.Error(err)
		return
	}

	session.Setup("redis://"+mr.Addr(), 20*time.Second)

	for _, tc := range []struct {
		name             string
		stockLocationIDs []int64
		headers          []map[string]string
		params           []map[string]string
		sleepTime        time.Duration
		assertFunc       func(assertion *_assert.Assertions, sessionID1 string, sessionID2 string)
	}{
		{
			"returns same session id regardless the param within time frame",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
					"taxon_id": "5",
				},
			},
			5 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.Equal(queryID1, queryID2)
			},
		},
		{
			"returns same session id regardless the param within time frame with user id",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-spree-token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-spree-token":       "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page":   "10",
					"page":       "1",
					"q":          "beras",
					"brand_id[]": "5",
				},
			},
			5 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.Equal(queryID1, queryID2)
			},
		},
		{
			"returns different session id with different anon id",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf4asdfc",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
					"taxon_id": "5",
				},
			},
			5 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.NotEqual(queryID1, queryID2)
			},
		},
		{
			"returns different session id when not whithin time frame",
			[]int64{3, 3},
			[]map[string]string{
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
				{
					"Locale":              "en",
					"Country":             "id",
					"x-happy-client-type": "android",
					"x-anonymous-id":      "98bdd77e882203f576726ecffec786df67553d7adf43de0b",
				},
			},
			[]map[string]string{
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
				},
				{
					"per_page": "10",
					"page":     "1",
					"q":        "beras",
					"taxon_id": "5",
				},
			},
			20 * time.Second,
			func(assertion *_assert.Assertions, queryID1 string, queryID2 string) {
				assertion.NotEqual(queryID1, queryID2)
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			mr.FastForward(21 * time.Second)

			sessionIDs := []string{}
			for i, stockLocationID := range tc.stockLocationIDs {
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				for k, v := range tc.headers[i] {
					header.Set(k, v)
				}

				param := url.Values{}
				for k, v := range tc.params[i] {
					param.Set(k, v)
				}

				searchUrl := fmt.Sprintf("/api/stock_locations/%d/products/search", stockLocationID)
				req, err := newRequest(http.MethodGet, searchUrl, header, param)
				if err != nil {
					t.Error(err)
				}

				resp, err := httpClient.Do(req)
				if err != nil {
					t.Error(err)
				}

				buf := &bytes.Buffer{}
				_, err = buf.ReadFrom(resp.Body)
				defer resp.Body.Close()
				if err != nil {
					t.Error(err)
				}

				root := gjson.ParseBytes(buf.Bytes())
				sessionIDs = append(sessionIDs, root.Get("meta").Get("session_id").String())

				mr.FastForward(tc.sleepTime)
			}

			assertion := _assert.New(t)
			assertion.Equal(2, len(sessionIDs))

			tc.assertFunc(assertion, sessionIDs[0], sessionIDs[1])
		})
	}
}

func getTaxonIDFilter(json gjson.Result) ([]int64, bool) {
	taxonFilterJSON := json.Get("filters").Get("taxon_ids")
	taxonFilters := taxonFilterJSON.Array()
	if taxonFilterJSON.Value() == nil || len(taxonFilters) == 0 {
		return []int64{}, true
	}

	taxonIDFilters := make([]int64, len(taxonFilters))
	for i, taxon := range taxonFilters {
		taxonIDFilters[i] = taxon.Int()
	}

	sort.Slice(taxonIDFilters, func(i, j int) bool {
		return taxonIDFilters[i] < taxonIDFilters[j]
	})

	return taxonIDFilters, false
}

func getBrandIDFilter(json gjson.Result) ([]int64, bool) {
	brandFilterJSON := json.Get("filters").Get("brands")
	brandFilters := brandFilterJSON.Array()
	if brandFilterJSON.Value() == nil || len(brandFilters) == 0 {
		return []int64{}, true
	}

	brandIDFilters := make([]int64, len(brandFilters))
	for i, brand := range brandFilters {
		brandIDFilters[i] = brand.Get("id").Int()
	}

	sort.Slice(brandIDFilters, func(i, j int) bool {
		return brandIDFilters[i] < brandIDFilters[j]
	})

	return brandIDFilters, false
}

func getPromotionTypeFilter(json gjson.Result) ([]interface{}, bool) {
	pTypesJSON := json.Get("filters").Get("promotion_types")
	pTypes := pTypesJSON.Array()
	if pTypesJSON.Value() == nil || len(pTypesJSON.Array()) == 0 {
		return []interface{}{}, true
	}

	promotionTypeFilter := make([]interface{}, len(pTypes))
	for i, pType := range pTypes {
		promotionTypeFilter[i] = pType.Value()
	}

	return promotionTypeFilter, false
}
