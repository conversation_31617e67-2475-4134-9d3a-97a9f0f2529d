package catalog

import (
	cat "happyfresh.io/catalog/lib/rpc/api"
)

var StoreProductsData = []*cat.StoreProducts{
	{
		ProductDetail: &cat.ProductDetail{
			ProductId:   11,
			Name:        "Chicken Egg",
			Description: "A dozen of chicken egg",
			Variants: []*cat.Variant{
				{
					VariantId: 21,
					Sku:       "S21",
				},
			},
			TaxonIds:        []int64{1, 2},
			Categories:      []string{"Dairy and Eggs", "Eggs"},
			ProductType:     "chicken-egg",
			ProductTypeId:   991,
			ProductTypeName: "Chicken Egg",
			Slug:            "chicken-egg",
			Properties: &cat.Properties{
				NaturalUnit:      "",
				SupermarketUnit:  "kg",
				AverageWeight:    0,
				SellNatural:      false,
				MaxOrderQuantity: 5,
			},
			Brand: &cat.Brand{
				BrandId: 31,
				Name:    "Eggoist",
			},
			StockItem: &cat.StockItem{
				Popularity: 5,
			},
			SellableItems: "product-sellable",
		},
		StoreProducts: []*cat.StoreProduct{
			{
				StoreProductId:  51,
				StockLocationId: 41,
				VariantId:       21,
				StoreId:         441,
				Popularity:      1,
				MaxPopularity:   10,
				Vendor: &cat.Vendor{
					VendorId: 7,
				},
				SellableItems: "store-product-sellable",
			},
			{
				StoreProductId:  52,
				StockLocationId: 42,
				VariantId:       21,
				StoreId:         442,
				Popularity:      2,
				MaxPopularity:   10,
			},
		},
	},
	{
		ProductDetail: &cat.ProductDetail{
			ProductId:   12,
			Name:        "Duck Egg",
			Description: "A dozen of duck egg",
			Variants: []*cat.Variant{
				{
					VariantId: 22,
					Sku:       "S22",
				},
			},
			TaxonIds:        []int64{1, 2},
			Categories:      []string{"Dairy and Eggs", "Eggs"},
			ProductType:     "chicken-egg",
			ProductTypeId:   991,
			ProductTypeName: "Chicken Egg",
			Slug:            "chicken-egg",
			Properties: &cat.Properties{
				NaturalUnit:      "",
				SupermarketUnit:  "kg",
				AverageWeight:    0,
				SellNatural:      false,
				MaxOrderQuantity: 5,
			},
			Brand: &cat.Brand{
				BrandId: 31,
				Name:    "Eggoist",
			},
			StockItem: &cat.StockItem{
				Popularity: 5,
			},
		},
		StoreProducts: []*cat.StoreProduct{
			{
				StoreProductId:  53,
				StockLocationId: 41,
				VariantId:       22,
				StoreId:         441,
				Popularity:      3,
				MaxPopularity:   10,
			},
			{
				StoreProductId:  54,
				StockLocationId: 42,
				VariantId:       22,
				StoreId:         442,
				Popularity:      4,
				MaxPopularity:   10,
			},
		},
	},
}

// ProductTypesData serves as dummy product types data
var ProductTypesData = []*cat.ProductTypes{
	{
		ProductTypes: []*cat.ProductType{
			{
				Id:           991,
				Translations: map[string]string{"en": "Chicken Egg", "id": "Chicken Egg - id", "ms": "Chicken Egg - ms"},
				Path:         "",
			},
			{
				Id:           992,
				Translations: map[string]string{"en": "Chicken Egg", "id": "Chicken Egg - id", "ms": "Chicken Egg - ms"},
				Path:         "",
			},
		},
	},
}
