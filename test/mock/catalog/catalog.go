package catalog

import (
	"context"
	"fmt"

	"google.golang.org/grpc"
	cat "happyfresh.io/catalog/lib/rpc/api"
)

func GetStoreProductByProductIDMock(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
	for _, c := range StoreProductsData {
		if c.ProductDetail.ProductId == in.ProductId {
			pd := c.ProductDetail
			pd.NameLocal = fmt.Sprintf("%s - %s", c.ProductDetail.Name, in.Locale)
			pd.DescriptionLocal = fmt.Sprintf("%s - %s", c.ProductDetail.Description, in.Locale)
			pd.CategoriesLocal = []string{}
			for _, category := range pd.Categories {
				pd.CategoriesLocal = append(pd.CategoriesLocal, fmt.Sprintf("%s - %s", category, in.Locale))
			}

			pd.ProductTypeNameLocal = fmt.Sprintf("%s - %s", c.ProductDetail.ProductTypeName, in.Locale)
			pd.Brand.NameLocal = fmt.Sprintf("%s - %s", c.ProductDetail.Brand.Name, in.Locale)

			return &cat.StoreProducts{
				ProductDetail: pd,
				StoreProducts: c.StoreProducts,
			}, nil
		}
	}

	return nil, nil
}

func GetStoreProductByIDMock(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
	for _, c := range StoreProductsData {
		for _, sp := range c.StoreProducts {
			if sp.StoreProductId == in.StoreProductId {
				pd := c.ProductDetail
				pd.NameLocal = fmt.Sprintf("%s - %s", c.ProductDetail.Name, in.Locale)
				pd.DescriptionLocal = fmt.Sprintf("%s - %s", c.ProductDetail.Description, in.Locale)
				pd.CategoriesLocal = []string{}
				for _, category := range pd.Categories {
					pd.CategoriesLocal = append(pd.CategoriesLocal, fmt.Sprintf("%s - %s", category, in.Locale))
				}

				pd.ProductTypeNameLocal = fmt.Sprintf("%s - %s", c.ProductDetail.ProductTypeName, in.Locale)
				pd.Brand.NameLocal = fmt.Sprintf("%s - %s", c.ProductDetail.Brand.Name, in.Locale)

				return &cat.StoreProducts{
					ProductDetail: pd,
					StoreProducts: []*cat.StoreProduct{sp},
				}, nil
			}
		}
	}

	return nil, nil
}

// GetProductTypesByProductTypeIDMock mock GetProductTypesByProductTypeID function which return correlated product types of a product type ID
func GetProductTypesByProductTypeIDMock(ctx context.Context, in *cat.ProductTypeRequest, opts ...grpc.CallOption) (*cat.ProductTypes, error) {
	if in.Id == 991 {
		return &cat.ProductTypes{
			ProductTypes: ProductTypesData[0].ProductTypes,
		}, nil
	}

	return nil, nil
}
