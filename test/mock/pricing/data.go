package pricing

import "happyfresh.io/search/core/pricing"

var ProductPriceData = []*pricing.Product{
	{
		Code:    "S21",
		StoreID: 441,
		InStock: true,
		Price: &pricing.Price{
			ClientTypePromoPrice: map[string]float64{
				"android": 10000,
				"ios":     11000,
				"webapp":  9000,
			},
		},
		Promotions: []*pricing.Promotion{
			{
				Code: "promo1",
			},
		},
		VestaInStock:   true,
		VestaUpdatedAt: "",
	},
	{
		Code:    "S21",
		StoreID: 442,
		InStock: true,
		Price: &pricing.Price{
			ClientTypePromoPrice: map[string]float64{
				"android": 10000,
				"ios":     11000,
			},
		},
		Promotions: []*pricing.Promotion{
			{
				Code: "promo1",
			},
		},
		VestaInStock:   true,
		VestaUpdatedAt: "",
	},
}
