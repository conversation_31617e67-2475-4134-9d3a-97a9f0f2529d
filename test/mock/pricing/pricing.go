package pricing

import (
	"context"

	"happyfresh.io/search/core/pricing"
)

func GetProductPriceMock(ctx context.Context, opt *pricing.Option) (map[int64]*pricing.Product, error) {
	prices := []*pricing.Product{}
	for _, item := range opt.Items {
		for _, pp := range ProductPriceData {
			if pp.Code == item.SKU && pp.StoreID == item.StoreID {
				prices = append(prices, pp)
			}
		}
	}

	result := map[int64]*pricing.Product{}
	for _, price := range prices {
		result[int64(price.StoreID)] = price
	}

	return result, nil
}

func EmptyMock(ctx context.Context, option *pricing.Option) (map[string]*pricing.Product, error) {
	return map[string]*pricing.Product{}, nil
}
