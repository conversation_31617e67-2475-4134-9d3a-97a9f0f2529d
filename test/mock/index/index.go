package index

import (
	"context"
	"errors"

	"happyfresh.io/search/core/index"
)

type IndexMock struct {
	data []*index.StockItem
}

func (i *IndexMock) Store(ctx context.Context, data ...interface{}) error {
	for _, d := range data {
		dd, ok := d.(*index.StockItem)
		if !ok {
			continue
		}

		i.data = append(i.data, dd)
	}

	return nil
}

func (i *IndexMock) Search(ctx context.Context, options ...index.SearchOption) (*index.SearchResponse, error) {
	return &index.SearchResponse{StockItems: i.data}, nil
}

func (i *IndexMock) SuggestSearch(ctx context.Context, options ...index.SearchOption) ([]string, error) {
	return nil, nil
}

func (i *IndexMock) Click(ctx context.Context, data interface{}) error {
	return nil
}

type ClientMock struct {
	Indices map[string]index.Index
}

func (c *ClientMock) Index(key string) (index.Index, error) {
	idx, ok := c.Indices[key]
	if !ok {
		return nil, errors.New("Index does not exists")
	}

	return idx, nil
}
