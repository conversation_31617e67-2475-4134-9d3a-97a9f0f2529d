.PHONY: docker-build-local docker-run-prod docker-compose-up docker-build-bitbucket

REPOSITORY_NAME ?= searchservice
IMAGE_NAME ?= search
ENVIRONMENT ?= prod
DEPLOY_DOCKER_IMAGE_URL ?= catalogService/Search

# for use with the ci pipeline
docker-build-ci:
	docker build -t ${DEPLOY_DOCKER_IMAGE_URL} .

# for use on local environment development
docker-build:
ifeq ($(ENVIRONMENT),$(filter $(ENVIRONMENT), base dev test pre-prod prod))
	docker build -t ${REPOSITORY_NAME}/${IMAGE_NAME}:${ENVIRONMENT} --target ${ENVIRONMENT} .
else
	echo "Error: valid values for ENVIRONMENT is : base, dev, test, pre-prod, or prod"
endif

docker-run-prod:
	docker run --rm -it -p ${PORT}:${PORT} ${REPOSITORY_NAME}/${IMAGE_NAME}:prod