version: '3'
services:
  search-golang:
    image: registry-gitlab.happyfresh.net/hf/tpd/ppc/catalog/search/search-golang:1.19
    ports:
      - "8080:8080"  # Change the ports as needed
    depends_on:
      - postgres
      - redis
  postgres:
    image: postgres:14.8-alpine
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"  # Change the ports as needed
  redis:
    image: redis
    ports:
      - "6379:6379"  # Change the ports as needed
