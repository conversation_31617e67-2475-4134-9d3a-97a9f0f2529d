# Search Service

## General Description
This service perform product search operation

## Components

There are two components that needs to be executed to provide full functionalities of this service:

| Name | Description |
| - | - |
| API Gateway | Serve http endpoints consumed by clients (andoid, ios, webapp, and mweb) |
| Worker | Consume data from indexing queue and index data to elasticsearch |

## Dependencies

As a mediator, Search Service depends on several other service inside Catalog family:

* [Hubble](https://bitbucket.org/makana14/spree-proxy-data) (Spree proxy)

* [Orion](https://bitbucket.org/makana14/psn-search) (personalization)

* [Product Catalog](https://bitbucket.org/makana14/product-catalog) (product information)

* [Calculator](https://bitbucket.org/makana14/calculator) (price information)

To easily access these service from your local environment, kindly follow our [port forwarding guidance](https://happyfresh.atlassian.net/wiki/spaces/EN/pages/1860829185/CLI+and+PortForwarding+using+Single-Sign+On).

Other dependencies:

* Elasticsearch v7.12

* PostgreSQL

* Redis

## Development

Follow these steps to run Search Service on your local environment:

### Pre-requisites

* Clone repository
    ```
    $ <NAME_EMAIL>:makana14/search.git
    $ cd search
    ```

* Install project dependencies using CI script
    ```
    ## for MacOS
    $ ./.ci.sh mac-os

    ## for Ubuntu
    $ ./.ci.sh ubuntu
    ```

* Install required golang modules
    ```
    $ go mod download
    ```
    if you find error when downloading happyfresh's repository. try to run this command before download gomod module
    ```
    export GOSUMDB=off
    ```

* Generate code described by directives within root directory
    ```
    $ go generate ./...
    ```

* Set Git Hooks path to our managed hooks directory
    ```
    $ git config core.hooksPath .githooks
    ```

* Copy [config](https://bitbucket.org/makana14/workspace/snippets/7nMK8n) to `config.toml` and use appropriate values according to your desired environment

### Test

* Run linter
    ```
    $ golangci-lint run --skip-dirs=vendor
    ```

* Run unit test
    ```
    $ export TEST_DATABASE_URL="postgres://postgres@localhost:5432/postgres?sslmode=disable"
    $ go run ./test/cli/seed.go
    $ go test -v -coverprofile=testdata/coverage.out ./...
    ```

* Run unit visual studio code(yan's note and todo list)
   * os.Getenv still reading from tmp/.env.test, need to read it from config
   * sql script could be read in test after go build -v
     * queries, err := getQueries("/resources/sql/stock_item_promotion.sql")
   

* Run integration test
    ```
    $ export SSRV_INDEX_SHARDS_TEST_ID="https://:<EMAIL>/api/as/v1/engines/ssrv-staging-id"
    $ export SSRV_INDEX_SHARDS_TEST_MY="https://:<EMAIL>/api/as/v1/engines/ssrv-staging-my"
    $ export SSRV_INDEX_SHARDS_TEST_TH="https://:<EMAIL>/api/as/v1/engines/ssrv-staging-th"
    $ export SSRV_INDEX_ES_SHARDS_TEST_ID="https://elastic:<EMAIL>:9243/.ent-search-engine-documents-ssrv-staging-id"
    $ export SSRV_INDEX_ES_SHARDS_TEST_MY="https://elastic:<EMAIL>:9243/.ent-search-engine-documents-ssrv-staging-my"
    $ export SSRV_INDEX_ES_SHARDS_TEST_TH="https://elastic:<EMAIL>:9243/.ent-search-engine-documents-ssrv-staging-th"
    $ export SSRV_INDEX_SUGGESTER_ES_SHARDS_TEST_ID="https://elastic:<EMAIL>:9243/search-summaries-id-staging/taxon-summaries-id-staging/brand-summaries-id-staging"
    $ export SSRV_INDEX_SUGGESTER_ES_SHARDS_TEST_MY="https://elastic:<EMAIL>:9243/search-summaries-my-staging/taxon-summaries-my-staging/brand-summaries-my-staging"
    $ export SSRV_INDEX_SUGGESTER_ES_SHARDS_TEST_TH="https://elastic:<EMAIL>:9243/search-summaries-th-staging/taxon-summaries-th-staging/brand-summaries-th-staging"
    $ export SSRV_REDIS_TEST_DSN="redis://localhost:6379"
    $ export SSRV_DATABASE_READ_TEST_GATEWAY_DSN="postgres://joyd_stage:<EMAIL>:5432/sprinkles_stage_p?sslmode=disable"
    $ export SSRV_PAPERCLIP_CDN="https://icdn.happyfresh.com"
    $ export SSRV_PAPERCLIP_SECRET_KEY="5e60e14f757f10673bbeb70e0dcec19a"
    $ export TEST_DATABASE_URL="postgres://postgres@localhost:5432/postgres?sslmode=disable"
    $ go run ./test/integration/seed/seed.go
    $ go test -race -v -timeout 20m ./test/integration/...
    ```

* Check unit test coverage
    ```
    $ cat testdata/coverage.out | grep -v -E ".+(json.go|pb.go|sql.go|generated.go).+" > testdata/c.out
    $ go tool cover -func=testdata/c.out
    ```

### Compile from script

Use these command to compile Search Service form script:

* API Gateway
    ```
    $ go run cli/search.go gateway
    ```

* Worker
    ```
    $ go run cli/search.go worker
    ```
