package worker

import "math"

var (
	PopularitySourceSprinkles = "sprinkles"
	PopularitySourceCatalog   = "catalog"
)

func popularity(rawPopularity, maxPopularity float64, boostingPoint float64) float64 {
	// Sirius Boosting = log ( boosting point from aloha * max raw popularity ) + max raw popularity
	// Popularity = Sirius Boosting + raw popularity

	var value float64
	if boostingPoint*maxPopularity >= 0 {
		value = (math.Log10(boostingPoint*maxPopularity+1) + maxPopularity) + rawPopularity
	} else {
		value = rawPopularity
	}

	if value < 0 {
		return 0
	}

	return value
}
