package worker

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPopularity(t *testing.T) {
	testCases := []struct {
		rawPopularity      float64
		maxPopularity      float64
		boostingPoint      float64
		expectedPopularity float64
	}{
		{
			rawPopularity:      0,
			expectedPopularity: 0,
		},
		{
			rawPopularity:      0,
			maxPopularity:      3.0,
			boostingPoint:      3.0,
			expectedPopularity: 4.0,
		},
		{
			rawPopularity:      -1,
			maxPopularity:      -1,
			boostingPoint:      1,
			expectedPopularity: 0,
		},
	}

	for _, testCase := range testCases {
		assert := assert.New(t)

		popularity := popularity(testCase.rawPopularity, testCase.maxPopularity, testCase.boostingPoint)
		assert.Equal(testCase.expectedPopularity, popularity)
	}
}
