package worker

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"happyfresh.io/search/core/datastore"
)

func TestMain(m *testing.M) {
	done := datastore.OpenTest(m, datastore.LocalQuery, os.Getenv("TEST_DATABASE_URL"), nil)
	defer done()

	os.Exit(m.Run())
}

func TestSubmit(t *testing.T) {
	submitKey := "key"
	submitPayload := []byte("payload")

	assert := assert.New(t)

	client := mockClient{}
	client.submit = func(ctx context.Context, key string, payload []byte) error {
		assert.Equal(submitKey, key)
		assert.Equal(submitPayload, payload)

		return nil
	}

	ctx := context.Background()
	ctxWC := WithClient(ctx, client)
	err := Submit(ctxWC, submitKey, submitPayload)

	assert.NoError(err)
}
