package worker_test

import (
	"context"
	"errors"
	"os"
	"testing"

	"github.com/glycerine/goconvey/convey"
	"google.golang.org/grpc"
	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/search/core/catalog"
	"happyfresh.io/search/core/index"

	"happyfresh.io/search/core/pricing"
	catalogMock "happyfresh.io/search/test/mock/catalog"
	indexMock "happyfresh.io/search/test/mock/index"
	pricingMock "happyfresh.io/search/test/mock/pricing"
	"happyfresh.io/search/worker"
)

type mockedObjects struct {
	catalogService catalog.Client
	indexService   *indexMock.ClientMock
}

func NewMockedObjects() *mockedObjects {
	catalogService, _ := catalog.New(
		"",
		os.Getenv("SSRV_REDIS_TEST_DSN"),
		catalog.WithgRPCClient(
			&catalog.ClientMock{
				GetStoreProductsByProductIDFunc:    catalogMock.GetStoreProductByProductIDMock,
				GetProductTypesByProductTypeIDFunc: catalogMock.GetProductTypesByProductTypeIDMock,
			},
		),
	)

	return &mockedObjects{
		catalogService: catalogService,
		indexService: &indexMock.ClientMock{
			Indices: map[string]index.Index{
				"ID": &indexMock.IndexMock{},
				"MY": &indexMock.IndexMock{},
			},
		},
	}
}

func TestIndexProductJob(t *testing.T) {
	payloadFunc := func(pID int, countryCode string) []byte {
		r, _ := worker.NewProductPayload(pID, countryCode)
		return r
	}

	tt := []struct {
		name                           string
		inputProductID                 int
		inputCountryCode               string
		indexedDoc                     []*index.StockItem
		getStoreProductsByProductID    func(context.Context, *cat.StoreProductRequest, ...grpc.CallOption) (*cat.StoreProducts, error)
		getProductTypesByProductTypeID func(context.Context, *cat.ProductTypeRequest, ...grpc.CallOption) (*cat.ProductTypes, error)
		pricingFunc                    func(context.Context, *pricing.Option) (map[int64]*pricing.Product, error)
		errNil                         bool
		popularitySource               string
		newStoreProductThreshold       int
		sprinklesPopularityFunction    func(ctx context.Context, variantID int64, storeID int64) (popularity float64, err error)
	}{
		{
			"Index complete data sprinkles",
			11,
			"ID",
			[]*index.StockItem{
				{
					ID:                   51,
					ProductID:            11,
					VariantID:            21,
					SKU:                  "S21",
					TaxonIDs:             []int64{1, 2},
					TaxonName:            []string{"Dairy and Eggs", "Eggs"},
					TaxonNameLocal:       []string{"Dairy and Eggs - id", "Eggs - id"},
					ProductTypeIDs:       []string{"991", "992"},
					ProductTypeName:      []string{"Chicken Egg", "Chicken Egg"},
					ProductTypeNameLocal: []string{"Chicken Egg - id", "Chicken Egg - id"},
					BrandID:              31,
					BrandName:            "Eggoist",
					BrandNameLocal:       "Eggoist - id",
					Name:                 "Chicken Egg",
					NameLocal:            "Chicken Egg - id",
					Description:          "A dozen of chicken egg",
					DescriptionLocal:     "A dozen of chicken egg - id",
					StoreID:              441,
					StockLocationID:      41,
					PriceAndroid:         10000,
					PriceIos:             11000,
					InStock:              "true",
					RawPopularity:        1,
					Popularity:           0,
					VendorID:             7,
					SellableItems:        "store-product-sellable",
					CreatedAt:            "2021-01-01 00:00:00",
				},
				{
					ID:                   52,
					ProductID:            11,
					VariantID:            21,
					SKU:                  "S21",
					TaxonIDs:             []int64{1, 2},
					TaxonName:            []string{"Dairy and Eggs", "Eggs"},
					TaxonNameLocal:       []string{"Dairy and Eggs - id", "Eggs - id"},
					ProductTypeIDs:       []string{"991", "992"},
					ProductTypeName:      []string{"Chicken Egg", "Chicken Egg"},
					ProductTypeNameLocal: []string{"Chicken Egg - id", "Chicken Egg - id"},
					BrandID:              31,
					BrandName:            "Eggoist",
					BrandNameLocal:       "Eggoist - id",
					Name:                 "Chicken Egg",
					NameLocal:            "Chicken Egg - id",
					Description:          "A dozen of chicken egg",
					DescriptionLocal:     "A dozen of chicken egg - id",
					StoreID:              442,
					StockLocationID:      42,
					PriceAndroid:         10000,
					PriceIos:             11000,
					InStock:              "true",
					RawPopularity:        2,
					Popularity:           0,
					SellableItems:        "product-sellable",
					CreatedAt:            "2021-01-01 00:00:02",
				},
			},
			catalogMock.GetStoreProductByProductIDMock,
			catalogMock.GetProductTypesByProductTypeIDMock,
			pricingMock.GetProductPriceMock,
			true,
			worker.PopularitySourceSprinkles,
			-90,
			nil,
		},
		{
			"Index complete data",
			11,
			"ID",
			[]*index.StockItem{
				{
					ID:                   51,
					ProductID:            11,
					VariantID:            21,
					SKU:                  "S21",
					TaxonIDs:             []int64{1, 2},
					TaxonName:            []string{"Dairy and Eggs", "Eggs"},
					TaxonNameLocal:       []string{"Dairy and Eggs - id", "Eggs - id"},
					ProductTypeIDs:       []string{"991", "992"},
					ProductTypeName:      []string{"Chicken Egg", "Chicken Egg"},
					ProductTypeNameLocal: []string{"Chicken Egg - id", "Chicken Egg - id"},
					BrandID:              31,
					BrandName:            "Eggoist",
					BrandNameLocal:       "Eggoist - id",
					Name:                 "Chicken Egg",
					NameLocal:            "Chicken Egg - id",
					Description:          "A dozen of chicken egg",
					DescriptionLocal:     "A dozen of chicken egg - id",
					StoreID:              441,
					StockLocationID:      41,
					PriceAndroid:         10000,
					PriceIos:             11000,
					InStock:              "true",
					RawPopularity:        1,
					Popularity:           11,
					VendorID:             7,
					SellableItems:        "store-product-sellable",
				},
				{
					ID:                   52,
					ProductID:            11,
					VariantID:            21,
					SKU:                  "S21",
					TaxonIDs:             []int64{1, 2},
					TaxonName:            []string{"Dairy and Eggs", "Eggs"},
					TaxonNameLocal:       []string{"Dairy and Eggs - id", "Eggs - id"},
					ProductTypeIDs:       []string{"991", "992"},
					ProductTypeName:      []string{"Chicken Egg", "Chicken Egg"},
					ProductTypeNameLocal: []string{"Chicken Egg - id", "Chicken Egg - id"},
					BrandID:              31,
					BrandName:            "Eggoist",
					BrandNameLocal:       "Eggoist - id",
					Name:                 "Chicken Egg",
					NameLocal:            "Chicken Egg - id",
					Description:          "A dozen of chicken egg",
					DescriptionLocal:     "A dozen of chicken egg - id",
					StoreID:              442,
					StockLocationID:      42,
					PriceAndroid:         10000,
					PriceIos:             11000,
					InStock:              "true",
					RawPopularity:        2,
					Popularity:           12,
					SellableItems:        "product-sellable",
				},
			},
			catalogMock.GetStoreProductByProductIDMock,
			catalogMock.GetProductTypesByProductTypeIDMock,
			pricingMock.GetProductPriceMock,
			true,
			worker.PopularitySourceCatalog,
			-90,
			nil,
		},
		{
			"Country ID translated to locale",
			11,
			"MY",
			[]*index.StockItem{
				{
					ID:                   51,
					ProductID:            11,
					VariantID:            21,
					SKU:                  "S21",
					TaxonIDs:             []int64{1, 2},
					TaxonName:            []string{"Dairy and Eggs", "Eggs"},
					TaxonNameLocal:       []string{"Dairy and Eggs - ms", "Eggs - ms"},
					ProductTypeIDs:       []string{"991", "992"},
					ProductTypeName:      []string{"Chicken Egg", "Chicken Egg"},
					ProductTypeNameLocal: []string{"Chicken Egg - ms", "Chicken Egg - ms"},
					BrandID:              31,
					BrandName:            "Eggoist",
					BrandNameLocal:       "Eggoist - ms",
					Name:                 "Chicken Egg",
					NameLocal:            "Chicken Egg - ms",
					Description:          "A dozen of chicken egg",
					DescriptionLocal:     "A dozen of chicken egg - ms",
					StoreID:              441,
					StockLocationID:      41,
					PriceAndroid:         10000,
					PriceIos:             11000,
					InStock:              "true",
					RawPopularity:        1,
					Popularity:           11,
					VendorID:             7,
					SellableItems:        "store-product-sellable",
				},
				{
					ID:                   52,
					ProductID:            11,
					VariantID:            21,
					SKU:                  "S21",
					TaxonIDs:             []int64{1, 2},
					TaxonName:            []string{"Dairy and Eggs", "Eggs"},
					TaxonNameLocal:       []string{"Dairy and Eggs - ms", "Eggs - ms"},
					ProductTypeIDs:       []string{"991", "992"},
					ProductTypeName:      []string{"Chicken Egg", "Chicken Egg"},
					ProductTypeNameLocal: []string{"Chicken Egg - ms", "Chicken Egg - ms"},
					BrandID:              31,
					BrandName:            "Eggoist",
					BrandNameLocal:       "Eggoist - ms",
					Name:                 "Chicken Egg",
					NameLocal:            "Chicken Egg - ms",
					Description:          "A dozen of chicken egg",
					DescriptionLocal:     "A dozen of chicken egg - ms",
					StoreID:              442,
					StockLocationID:      42,
					PriceAndroid:         10000,
					PriceIos:             11000,
					InStock:              "true",
					RawPopularity:        2,
					Popularity:           12,
					SellableItems:        "product-sellable",
				},
			},
			catalogMock.GetStoreProductByProductIDMock,
			catalogMock.GetProductTypesByProductTypeIDMock,
			pricingMock.GetProductPriceMock,
			true,
			worker.PopularitySourceCatalog,
			-90,
			nil,
		},
		{
			"Returns error when catalog call fails",
			11,
			"ID",
			nil,
			func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
				return nil, errors.New("Intentional Error")
			},
			catalogMock.GetProductTypesByProductTypeIDMock,
			pricingMock.GetProductPriceMock,
			false,
			worker.PopularitySourceCatalog,
			-90,
			nil,
		},
		{
			"Returns error when aloha call fails",
			11,
			"ID",
			nil,
			catalogMock.GetStoreProductByProductIDMock,
			catalogMock.GetProductTypesByProductTypeIDMock,
			func(ctx context.Context, opt *pricing.Option) (map[int64]*pricing.Product, error) {
				return nil, errors.New("Intentional Error")
			},
			false,
			worker.PopularitySourceCatalog,
			-90,
			nil,
		},
		{
			"No price from aloha",
			11,
			"MY",
			[]*index.StockItem{
				{
					ID:                   51,
					ProductID:            11,
					VariantID:            21,
					SKU:                  "S21",
					TaxonIDs:             []int64{1, 2},
					TaxonName:            []string{"Dairy and Eggs", "Eggs"},
					TaxonNameLocal:       []string{"Dairy and Eggs - ms", "Eggs - ms"},
					ProductTypeIDs:       []string{"991", "992"},
					ProductTypeName:      []string{"Chicken Egg", "Chicken Egg"},
					ProductTypeNameLocal: []string{"Chicken Egg - ms", "Chicken Egg - ms"},
					BrandID:              31,
					BrandName:            "Eggoist",
					BrandNameLocal:       "Eggoist - ms",
					Name:                 "Chicken Egg",
					NameLocal:            "Chicken Egg - ms",
					Description:          "A dozen of chicken egg",
					DescriptionLocal:     "A dozen of chicken egg - ms",
					StoreID:              441,
					StockLocationID:      41,
					PriceAndroid:         0,
					PriceIos:             0,
					InStock:              "false",
					RawPopularity:        1,
					Popularity:           11,
					VendorID:             7,
					SellableItems:        "store-product-sellable",
				},
				{
					ID:                   52,
					ProductID:            11,
					VariantID:            21,
					SKU:                  "S21",
					TaxonIDs:             []int64{1, 2},
					TaxonName:            []string{"Dairy and Eggs", "Eggs"},
					TaxonNameLocal:       []string{"Dairy and Eggs - ms", "Eggs - ms"},
					ProductTypeIDs:       []string{"991", "992"},
					ProductTypeName:      []string{"Chicken Egg", "Chicken Egg"},
					ProductTypeNameLocal: []string{"Chicken Egg - ms", "Chicken Egg - ms"},
					BrandID:              31,
					BrandName:            "Eggoist",
					BrandNameLocal:       "Eggoist - ms",
					Name:                 "Chicken Egg",
					NameLocal:            "Chicken Egg - ms",
					Description:          "A dozen of chicken egg",
					DescriptionLocal:     "A dozen of chicken egg - ms",
					StoreID:              442,
					StockLocationID:      42,
					PriceAndroid:         0,
					PriceIos:             0,
					InStock:              "false",
					RawPopularity:        2,
					Popularity:           12,
					SellableItems:        "product-sellable",
				},
			},
			catalogMock.GetStoreProductByProductIDMock,
			catalogMock.GetProductTypesByProductTypeIDMock,
			func(ctx context.Context, opt *pricing.Option) (map[int64]*pricing.Product, error) {
				return map[int64]*pricing.Product{}, nil
			},
			true,
			worker.PopularitySourceCatalog,
			-90,
			nil,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			pricing.ProductPriceAllChannelGetter = tc.pricingFunc

			catalogService, err := catalog.New(
				"",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithgRPCClient(
					&catalog.ClientMock{
						GetStoreProductsByProductIDFunc:    tc.getStoreProductsByProductID,
						GetProductTypesByProductTypeIDFunc: tc.getProductTypesByProductTypeID,
					},
				),
			)
			if err != nil {
				t.Error(err)
			}

			indexClient := &indexMock.ClientMock{
				Indices: map[string]index.Index{
					"ID": &indexMock.IndexMock{},
					"MY": &indexMock.IndexMock{},
				},
			}

			ctx := context.Background()
			input := payloadFunc(tc.inputProductID, tc.inputCountryCode)
			err = worker.NewIndexProductJob(indexClient, catalogService, tc.popularitySource, tc.newStoreProductThreshold).Do(ctx, input)
			assert := convey.ShouldEqual(err == nil, tc.errNil)
			if assert != "" {
				t.Error(assert)
			}

			if err != nil {
				return
			}

			i, err := indexClient.Index(tc.inputCountryCode)
			if err != nil {
				t.Error(err)
			}

			r, err := i.Search(ctx)
			if err != nil {
				t.Error()
			}

			assert = convey.ShouldEqual(len(r.StockItems), len(tc.indexedDoc))
			if assert != "" {
				t.Error(assert)
			}

			for i, resultSI := range r.StockItems {
				expectSI := tc.indexedDoc[i]
				assert = convey.ShouldEqual(resultSI.ID, expectSI.ID)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.ProductID, expectSI.ProductID)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.VariantID, expectSI.VariantID)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.SKU, expectSI.SKU)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldResemble(resultSI.TaxonIDs, expectSI.TaxonIDs)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldResemble(resultSI.TaxonName, expectSI.TaxonName)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldResemble(resultSI.TaxonNameLocal, expectSI.TaxonNameLocal)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldResemble(resultSI.ProductTypeIDs, expectSI.ProductTypeIDs)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldResemble(resultSI.ProductTypeNameLocal, expectSI.ProductTypeNameLocal)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldResemble(resultSI.ProductTypeName, expectSI.ProductTypeName)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.BrandID, expectSI.BrandID)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.BrandName, expectSI.BrandName)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldResemble(resultSI.BrandNameLocal, expectSI.BrandNameLocal)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.Name, expectSI.Name)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.NameLocal, expectSI.NameLocal)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.Description, expectSI.Description)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.DescriptionLocal, expectSI.DescriptionLocal)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.StoreID, expectSI.StoreID)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.PriceAndroid, expectSI.PriceAndroid)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.PriceIos, expectSI.PriceIos)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.InStock, expectSI.InStock)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.RawPopularity, expectSI.RawPopularity)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.Popularity, expectSI.Popularity)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.VendorID, expectSI.VendorID)
				if assert != "" {
					t.Error(assert)
				}
				assert = convey.ShouldEqual(resultSI.SellableItems, expectSI.SellableItems)
				if assert != "" {
					t.Error(assert)
				}
			}
		})
	}
}
