package worker

import (
	"context"
	"net/http"

	"github.com/pkg/errors"
)

var (
	contextKey = &worker<PERSON><PERSON>{"worker"}
)

type workerKey struct {
	name string
}

func (k *workerKey) String() string {
	return "Context Key: " + k.name
}

func WithClient(ctx context.Context, c Client) context.Context {
	return context.WithValue(ctx, contextKey, c)
}

func FromContext(ctx context.Context) (Client, error) {
	if c, ok := ctx.Value(contextKey).(Client); ok {
		return c, nil
	}

	return nil, errors.New("No client context")
}

func WorkerContext(c Client) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := WithClient(r.Context(), c)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
