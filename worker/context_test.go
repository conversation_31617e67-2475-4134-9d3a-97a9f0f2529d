package worker

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestString(t *testing.T) {
	contextKey := "ck"
	expectedContextKey := "Context Key: " + contextKey

	wk := workerKey{contextKey}
	ck := wk.String()

	assert := assert.New(t)
	assert.Equal(expectedContextKey, ck)
}

type mockClient struct {
	submit func(ctx context.Context, key string, payload []byte) error
}

func (mc mockClient) Submit(ctx context.Context, key string, payload []byte) error {
	if mc.submit != nil {
		return mc.submit(ctx, key, payload)
	}

	return nil
}

func TestWithClient(t *testing.T) {
	client := mockClient{}

	ctx := context.Background()
	ctxWC := WithClient(ctx, client)

	clientWC, ok := ctxWC.Value(contextKey).(Client)

	assert := assert.New(t)
	assert.True(ok)
	assert.Equal(client, clientWC)
}

func TestFromContext(t *testing.T) {
	client := mockClient{}

	ctxWC := context.WithValue(context.Background(), contextKey, client)
	clientWC, err := FromContext(ctxWC)

	assert := assert.New(t)
	assert.NoError(err)
	assert.Equal(client, clientWC)

	ctxNC := context.WithValue(context.Background(), contextKey, nil)
	clientNC, err := FromContext(ctxNC)

	assert.Error(err)
	assert.Nil(clientNC)
}

func TestWorkerContext(t *testing.T) {
	client := mockClient{}

	nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		clientWC, err := FromContext(r.Context())

		assert := assert.New(t)
		assert.NoError(err)
		assert.Equal(client, clientWC)
	})

	handlerToTest := WorkerContext(client)(nextHandler)
	testRequest := httptest.NewRequest("POST", "http://test.happyfresh.com", nil)

	handlerToTest.ServeHTTP(httptest.NewRecorder(), testRequest)
}
