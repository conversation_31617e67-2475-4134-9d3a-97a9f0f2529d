package response

import (
	"context"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	cat "happyfresh.io/catalog/lib/rpc/api"
	hubbleRPC "happyfresh.io/hubble/lib/rpc"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/pricing"
	"happyfresh.io/search/core/promotion"
)

func TestMap(t *testing.T) {
	for _, tc := range []struct {
		testName           string
		products           []*cat.StoreProducts
		rootOptionChain    []RootOption
		productOptionChain []ProductOption
		expectedOutput     *rpc.SearchResponse
	}{
		{
			testName: "No matched products",
			products: []*cat.StoreProducts{},
			expectedOutput: &rpc.SearchResponse{
				Products: make([]*rpc.Product, 0),
			},
		},
		{
			testName: "Map products to response",
			products: []*cat.StoreProducts{
				{
					ProductDetail: &cat.ProductDetail{
						Properties: &cat.Properties{},
						Variants: []*cat.Variant{
							{
								Images: &cat.Images{
									Images: []*cat.Image{
										{
											Id:                  145717,
											Position:            1,
											AltText:             "mori-tama-eggs",
											MiniUrl:             "https://icdn.happyfresh.com/t/s_mini,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
											SmallUrl:            "https://icdn.happyfresh.com/t/s_small,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
											ProductUrl:          "https://icdn.happyfresh.com/t/s_product,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
											ProductHqUrl:        "https://icdn.happyfresh.com/t/s_product_hq,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
											LargeUrl:            "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
											OriginalUrl:         "https://icdn.happyfresh.com/t/s_wide,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
											AttachmentUpdatedAt: "2015-11-29 01:04:55",
										},
									},
								},
							},
						},
					},
				},
			},
			expectedOutput: &rpc.SearchResponse{
				Products: []*rpc.Product{
					{
						Variants: []*rpc.Variant{
							{
								Images: []*rpc.Image{
									{
										Id:                  145717,
										Position:            1,
										AttachmentUpdatedAt: "2015-11-29 01:04:55",
										MiniUrl:             "https://icdn.happyfresh.com/t/s_mini,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
										SmallUrl:            "https://icdn.happyfresh.com/t/s_small,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
										ProductUrl:          "https://icdn.happyfresh.com/t/s_product,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
										ProductHqUrl:        "https://icdn.happyfresh.com/t/s_product_hq,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
										LargeUrl:            "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
										OriginalUrl:         "https://icdn.happyfresh.com/t/s_wide,c_fit/spree/images/attachments/8de9cbcb160dd07fd0c26118402ef267d781541e-original.jpg",
									},
								},
							},
						},
					},
				},
			},
		},
	} {
		t.Run(tc.testName, func(t *testing.T) {
			output, err := Map(nil, tc.products, tc.rootOptionChain, tc.productOptionChain)
			if err != nil {
				t.Errorf("\nexpectation: %v\nreality: %v", nil, err)
			}

			if len(tc.products) == 0 {
				if len(output.Products) > 0 {
					t.Errorf("\nexpectation: %v\nreality: %v", 0, len(output.Products))
				} else {
					return
				}
			}

			pvImages, expectedPVImages := make(map[int64][]*rpc.Image), make(map[int64][]*rpc.Image)
			for _, product := range output.Products {
				pvImages[product.Id] = product.Variants[0].Images
			}
			for _, expectedProduct := range tc.expectedOutput.Products {
				expectedPVImages[expectedProduct.Id] = expectedProduct.Variants[0].Images
			}
			if !reflect.DeepEqual(pvImages, expectedPVImages) {
				t.Errorf("\nexpectation: %v\nreality: %v", expectedPVImages, pvImages)
			}
		})
	}
}

func TestRootOptionChain(t *testing.T) {
	rootOptions := []RootOption{}
	appliedRootOptions := RootOptionChain(rootOptions...)

	if !reflect.DeepEqual(rootOptions, appliedRootOptions) {
		t.Errorf("\nexpectation: %v\nreality: %v", rootOptions, appliedRootOptions)
	}
}

func TestProductOptionChain(t *testing.T) {
	productOptions := []ProductOption{}
	appliedProductOptions := ProductOptionChain(productOptions...)

	if !reflect.DeepEqual(productOptions, appliedProductOptions) {
		t.Errorf("\nexpectation: %v\nreality: %v", productOptions, appliedProductOptions)
	}
}

func TestWithMetaAndPageLimiter(t *testing.T) {
	for _, tc := range []struct {
		name    string
		meta    *index.Meta
		limiter int64
		version string
		root    *rpc.SearchResponse
	}{
		{
			"Copies data from *index.Meta",
			&index.Meta{
				SearchID:   "",
				Page:       1,
				PageSize:   20,
				TotalPages: 5,
				Count:      20,
				TotalCount: 90,
			},
			100,
			"3.34",
			&rpc.SearchResponse{
				Count:       20,
				TotalCount:  90,
				CurrentPage: 1,
				TotalPage:   5,
				PerPage:     20,
				SearchId:    0,
				Meta: &rpc.Meta{
					EngineVersion: "3.34",
				},
			},
		},
		{
			"Limits total pages and total count based on page limiter",
			&index.Meta{
				SearchID:   "",
				Page:       1,
				PageSize:   20,
				TotalPages: 5,
				Count:      20,
				TotalCount: 90,
			},
			3,
			"3.34",
			&rpc.SearchResponse{
				Count:       20,
				TotalCount:  60,
				CurrentPage: 1,
				TotalPage:   3,
				PerPage:     20,
				SearchId:    0,
				Meta: &rpc.Meta{
					EngineVersion: "3.34",
				},
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			result := &rpc.SearchResponse{}
			f := WithMetaAndPageLimiter(tc.meta, tc.limiter, tc.version)
			f(result)

			if tc.root.Count != result.Count {
				t.Errorf("\nexpectation: %v\nreality: %v", tc.root.Count, result.Count)
			}

			if tc.root.TotalCount != result.TotalCount {
				t.Errorf("\nexpectation: %v\nreality: %v", tc.root.TotalCount, result.TotalCount)
			}

			if tc.root.CurrentPage != result.CurrentPage {
				t.Errorf("\nexpectation: %v\nreality: %v", tc.root.CurrentPage, result.CurrentPage)
			}

			if tc.root.TotalPage != result.TotalPage {
				t.Errorf("\nexpectation: %v\nreality: %v", tc.root.TotalPage, result.TotalPage)
			}

			if tc.root.PerPage != result.PerPage {
				t.Errorf("\nexpectation: %v\nreality: %v", tc.root.PerPage, result.PerPage)
			}

			if tc.root.SearchId != result.SearchId {
				t.Errorf("\nexpectation: %v\nreality: %v", tc.root.SearchId, result.SearchId)
			}

			if tc.root.Meta.EngineVersion != result.Meta.EngineVersion {
				t.Errorf("\nexpectation: %v\nreality: %v", tc.root.Meta.EngineVersion, result.Meta.EngineVersion)
			}
		})
	}
}

func TestWithNormalizedQuery(t *testing.T) {
	t.Run("With normalized query", func(t *testing.T) {
		normalizedQuery := "telur"

		result := &rpc.SearchResponse{}
		f := WithNormalizedQuery(normalizedQuery)
		f(result)

		if result.NormalizedQuery != normalizedQuery {
			t.Errorf("\nexpectation: %v\nreality: %v", normalizedQuery, result.NormalizedQuery)
		}
	})
}

func TestWithSearchID(t *testing.T) {
	t.Run("With search ID", func(t *testing.T) {
		searchID := int64(*********)

		result := &rpc.SearchResponse{}
		f := WithSearchID(searchID)
		f(result)

		if result.SearchId != searchID {
			t.Errorf("\nexpectation: %v\nreality: %v", searchID, result.SearchId)
		}
	})
}

func TestWithTaxonIDsFilter(t *testing.T) {
	t.Run("Returns empty array as default value", func(t *testing.T) {
		result := &rpc.SearchResponse{}
		f := WithTaxonIDsFilter(nil)
		f(result)

		if result.Filters.TaxonIds == nil {
			t.Errorf("\nexpectation: %v\nreality: <nil>", []int64{})
		}
	})
}

func TestWithBrandsFilter(t *testing.T) {
	t.Run("Returns empty array as default value", func(t *testing.T) {
		result := &rpc.SearchResponse{}
		f := WithBrandsFilter(nil)
		f(result)

		if result.Filters.Brands == nil {
			t.Errorf("\nexpectation: %v\nreality: <nil>", []*rpc.Brand{})
		}
	})
}

func TestWithPromotionTypesFilter(t *testing.T) {
	t.Run("Returns empty array as default value", func(t *testing.T) {
		result := &rpc.SearchResponse{}
		f := WithPromotionTypesFilter(nil)
		f(result)

		if result.Filters.PromotionTypes == nil {
			t.Errorf("\nexpectation: %v\nreality: <nil>", []*rpc.PromotionType{})
		}
	})
}

func TestWithQuery(t *testing.T) {
	testCases := []struct {
		name        string
		queryBefore string
		queryAfter  string
	}{
		{
			name:        "Empty \"before\" and \"after\" information",
			queryBefore: "",
			queryAfter:  "",
		},
		{
			name:        "\"After\" equal to \"before\" information",
			queryBefore: "before",
			queryAfter:  "before",
		},
		{
			name:        "Assign \"before\" and \"after\" information",
			queryBefore: "before",
			queryAfter:  "after",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			result := &rpc.SearchResponse{}
			f := WithQuery(testCase.queryBefore, testCase.queryAfter)
			f(result)

			if testCase.queryBefore != testCase.queryAfter && len(testCase.queryBefore) > 0 && len(testCase.queryAfter) > 0 {
				if result.Query.Before != testCase.queryBefore {
					t.Errorf("\nexpectation: %v\nreality: %v", testCase.queryBefore, result.Query.Before)
				}
				if result.Query.After != testCase.queryAfter {
					t.Errorf("\nexpectation: %v\nreality: %v", testCase.queryAfter, result.Query.After)
				}
			}
		})
	}
}

func TestWithNamingAndDescription(t *testing.T) {
	testCases := []struct {
		name                string
		locale              string
		from                *cat.StoreProducts
		expectedName        string
		expectedDescription string
		to                  *rpc.Product
	}{
		{
			name:   "Has complete attribute",
			locale: "id",
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					NameLocal:        "Jus Sari Edamame",
					Name:             "Edamame Juice",
					DescriptionLocal: "Minuman Sari Edamame Segar",
					Description:      "Fresh Edamame Juice",
				},
			},
			expectedName:        "Jus Sari Edamame",
			expectedDescription: "Minuman Sari Edamame Segar",
			to: &rpc.Product{
				Variants: []*rpc.Variant{{}},
			},
		},
		{
			name:   "Has complete attribute, with en locale",
			locale: "en",
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					NameLocal:        "Jus Sari Edamame",
					Name:             "Edamame Juice",
					DescriptionLocal: "Minuman Sari Edamame Segar",
					Description:      "Fresh Edamame Juice",
				},
			},
			expectedName:        "Edamame Juice",
			expectedDescription: "Fresh Edamame Juice",
			to:                  &rpc.Product{},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithNamingAndDescription(testCase.locale)
			f(testCase.from, testCase.to, 0)

			if testCase.to.Name != testCase.expectedName {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedName, testCase.to.Name)
			}
			if testCase.to.Description != testCase.expectedDescription {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedDescription, testCase.to.Description)
			}
			if len(testCase.to.Variants) > 0 {
				if testCase.to.Variants[0].Name != testCase.expectedName {
					t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedName, testCase.to.Variants[0].Name)
				}
				if testCase.to.Variants[0].Description != testCase.expectedDescription {
					t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedDescription, testCase.to.Variants[0].Description)
				}
			}
		})
	}
}

func TestWithBrandNaming(t *testing.T) {
	testCases := []struct {
		name      string
		locale    string
		from      *cat.StoreProducts
		to        *rpc.Product
		brandID   int64
		brandName string
	}{
		{
			name:   "Source has complete attribute",
			locale: "id",
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Brand: &cat.Brand{
						BrandId:   6310,
						NameLocal: "Daily Fresh",
						Name:      "daily fresh",
					},
				},
			},
			to:        &rpc.Product{},
			brandID:   6310,
			brandName: "Daily Fresh",
		},
		{
			name:   "With en locale",
			locale: "en",
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Brand: &cat.Brand{
						BrandId:   6310,
						NameLocal: "Daily Fresh",
						Name:      "daily fresh",
					},
				},
			},
			to:        &rpc.Product{},
			brandID:   6310,
			brandName: "daily fresh",
		},
		{
			name:   "Source has no brand",
			locale: "id",
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{},
			},
			to:        &rpc.Product{},
			brandID:   0,
			brandName: "",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithBrandNaming(testCase.locale)
			f(testCase.from, testCase.to, 0)

			if testCase.to.Brand.Name != testCase.brandName {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.brandName, testCase.to.Brand.Name)
			}
			if testCase.to.Brand.Id != testCase.brandID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.brandID, testCase.to.Brand.Id)
			}
		})
	}
}

func TestWithCategories(t *testing.T) {
	testCases := []struct {
		name       string
		locale     string
		from       *cat.StoreProducts
		to         *rpc.Product
		categories []string
	}{
		{
			name:   "Source has complete attribute",
			locale: "id",
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					CategoriesLocal: []string{"Jus & Minuman Kesehatan", "Minuman"},
					Categories:      []string{"Juices & Health Drinks", "Beverages"},
				},
			},
			to:         &rpc.Product{},
			categories: []string{"Jus & Minuman Kesehatan", "Minuman"},
		},
		{
			name:   "With en locale",
			locale: "en",
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					CategoriesLocal: []string{"Jus & Minuman Kesehatan", "Minuman"},
					Categories:      []string{"Juices & Health Drinks", "Beverages"},
				},
			},
			to:         &rpc.Product{},
			categories: []string{"Juices & Health Drinks", "Beverages"},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithCategories(testCase.locale)
			f(testCase.from, testCase.to, 0)

			if !reflect.DeepEqual(testCase.to.Categories, testCase.categories) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.categories, testCase.to.Categories)
			}
		})
	}
}

func TestWithVendor(t *testing.T) {
	testCases := []struct {
		name              string
		locale            string
		from              *cat.StoreProducts
		to                *rpc.Product
		vendorID          int64
		vendorName        string
		vendorDescription string
		vendorImageUrl    string
	}{
		{
			name:   "Source has complete attribute",
			locale: "id",
			from: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{
					{
						Vendor: &cat.Vendor{
							VendorId:         18,
							Name:             "TipTop Jaya",
							Description:      "TipTop Jaya all can buy any products you need",
							DescriptionLocal: "TipTop Jaya serba ada",
							ImageUrl:         "/vendor/display_images/61d0dc89df1b8da6da1e3938bf31442c4a2b7606-original.png",
						},
					},
				},
			},
			to:                &rpc.Product{},
			vendorID:          18,
			vendorName:        "TipTop Jaya",
			vendorDescription: "TipTop Jaya serba ada",
			vendorImageUrl:    "/vendor/display_images/61d0dc89df1b8da6da1e3938bf31442c4a2b7606-original.png",
		},
		{
			name:   "With en locale",
			locale: "en",
			from: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{
					{
						Vendor: &cat.Vendor{
							VendorId:         18,
							Name:             "TipTop Jaya",
							Description:      "TipTop Jaya all can buy any products you need",
							DescriptionLocal: "TipTop Jaya serba ada",
							ImageUrl:         "/vendor/display_images/61d0dc89df1b8da6da1e3938bf31442c4a2b7606-original.png",
						},
					},
				},
			},
			to:                &rpc.Product{},
			vendorID:          18,
			vendorName:        "TipTop Jaya",
			vendorDescription: "TipTop Jaya all can buy any products you need",
			vendorImageUrl:    "/vendor/display_images/61d0dc89df1b8da6da1e3938bf31442c4a2b7606-original.png",
		},
		{
			name:   "With no vendor",
			locale: "en",
			from: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{{}},
			},
			to:                &rpc.Product{},
			vendorID:          18,
			vendorName:        "TipTop Jaya",
			vendorDescription: "TipTop Jaya all can buy any products you need",
			vendorImageUrl:    "/vendor/display_images/61d0dc89df1b8da6da1e3938bf31442c4a2b7606-original.png",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithVendor(testCase.locale)
			f(testCase.from, testCase.to, 0)

			if testCase.from.StoreProducts[0].Vendor == nil {
				if testCase.to.Vendor != nil {
					t.Errorf("\nexpectation: %v\nreality: %v", nil, testCase.to.Vendor)
				}

				return
			}

			if testCase.to.Vendor.Id != testCase.vendorID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.vendorID, testCase.to.Vendor.Id)
			}
			if testCase.to.Vendor.Name != testCase.vendorName {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.vendorName, testCase.to.Vendor.Name)
			}
			if testCase.to.Vendor.Description != testCase.vendorDescription {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.vendorDescription, testCase.to.Vendor.Description)
			}
			if testCase.to.Vendor.ImageUrl != testCase.vendorImageUrl {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.vendorImageUrl, testCase.to.Vendor.ImageUrl)
			}
		})
	}
}

func TestWithPromotionLabels(t *testing.T) {
	testCases := []struct {
		name                                           string
		promotions                                     map[string]*promotion.Promotion
		from                                           *cat.StoreProducts
		to                                             *rpc.Product
		expectedDisplayPromotionActionsCombinationText string
		hasDisplayBannerText                           bool
		expectedDisplayBannerText                      []string
		hasActionProducts                              bool
		expectedActionProducts                         []*rpc.PromotionProduct
		expectedPromotionTypes                         []string
	}{
		{
			name: "With promotion label",
			promotions: map[string]*promotion.Promotion{
				"3077313021258-ID": {
					DisplayPromotionActionCombinationText: "Promo",
				},
			},
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
				},
			},
			to: &rpc.Product{},
			expectedDisplayPromotionActionsCombinationText: "Promo",
			expectedPromotionTypes:                         []string{},
		},
		{
			name: "With promotion label buy x get y",
			promotions: map[string]*promotion.Promotion{
				"3077313021258-ID": {
					DisplayPromotionActionCombinationText: "Buy X Get Y",
					PromotionActionType:                   "Spree::Promotion::Actions::GetYAction",
					GetYQuantity:                          1,
					DisplayBannerText:                     []string{"Buy X Get Y"},
				},
			},
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					ProductId: 11,
				},
			},
			to: &rpc.Product{
				Variants: []*rpc.Variant{
					{
						Id: 1,
					},
				},
			},
			expectedDisplayPromotionActionsCombinationText: "Buy X Get Y",
			hasDisplayBannerText:                           true,
			expectedDisplayBannerText:                      []string{"Buy X Get Y"},
			hasActionProducts:                              true,
			expectedActionProducts: []*rpc.PromotionProduct{
				{
					Id:                11,
					Quantity:          1,
					DisplayBannerText: []string{"Buy X Get Y"},
					Variants: []*rpc.Variant{
						{
							Id: 1,
						},
					},
				},
			},
			expectedPromotionTypes: []string{"get_y_action"},
		},
		{
			name: "With promotion discount",
			promotions: map[string]*promotion.Promotion{
				"3077313021258-ID": {
					DisplayPromotionActionCombinationText: "",
					PromotionActionType:                   "Spree::Promotion::Actions::GetDiscountAction",
					BuyXQuantity:                          2,
					GetPPercent:                           0.2,
					DisplayBannerText:                     []string{"2nd Item 20%% off"},
				},
			},
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					ProductId: 11,
				},
			},
			to: &rpc.Product{
				Variants: []*rpc.Variant{
					{
						Id: 1,
					},
				},
			},
			expectedDisplayPromotionActionsCombinationText: "",
			hasDisplayBannerText:                           true,
			expectedDisplayBannerText:                      []string{"2nd Item 20%% off"},
			hasActionProducts:                              true,
			expectedActionProducts: []*rpc.PromotionProduct{
				{
					Id:                11,
					Quantity:          0,
					DisplayBannerText: []string{"2nd Item 20%% off"},
					Discount:          0.2,
					Variants: []*rpc.Variant{
						{
							Id: 1,
						},
					},
				},
			},
			expectedPromotionTypes: []string{"get_discount_action"},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			assert := assert.New(t)

			f := WithPromotionLabels(testCase.promotions)
			f(testCase.from, testCase.to, 0)

			assert.Equal(testCase.expectedDisplayPromotionActionsCombinationText, testCase.to.DisplayPromotionActionsCombinationText)

			assert.Equal(testCase.expectedPromotionTypes, testCase.to.PromotionTypes)

			if testCase.hasDisplayBannerText {
				assert.Equal(testCase.expectedDisplayBannerText, testCase.to.DisplayBannerText)
			}

			if testCase.hasActionProducts {
				assert.Equal(testCase.expectedActionProducts, testCase.to.PromotionActions[0].Products)
			}
		})
	}
}

func TestWithMultiplePromotionLabels(t *testing.T) {
	testCases := []struct {
		name                                           string
		promotionsPerSLI                               map[int64]map[string]*promotion.Promotion
		from                                           *cat.StoreProducts
		to                                             *rpc.Product
		expectedDisplayPromotionActionsCombinationText string
	}{
		{
			name: "Promotion label with multiple SLI",
			promotionsPerSLI: map[int64]map[string]*promotion.Promotion{
				3: {
					"3077313021258-ID": {
						DisplayPromotionActionCombinationText: "Buy X Get Y",
					},
				},
			},
			from: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{
					{
						StockLocationId: 3,
					},
				},
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
				},
			},
			to: &rpc.Product{},
			expectedDisplayPromotionActionsCombinationText: "Buy X Get Y",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithMultiplePromotionLabels(testCase.promotionsPerSLI)
			f(testCase.from, testCase.to, 0)

			if testCase.to.DisplayPromotionActionsCombinationText != testCase.expectedDisplayPromotionActionsCombinationText {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedDisplayPromotionActionsCombinationText, testCase.to.DisplayPromotionActionsCombinationText)
			}
		})
	}
}

func TestWithAlohaPricing(t *testing.T) {
	testCases := []struct {
		name                     string
		prices                   map[string]*pricing.Product
		currencyCode             string
		countryCode              string
		isMPQEligible            bool
		disableLimitedStockQty   bool
		expectedPromotionActions []string
		from                     *cat.StoreProducts
		to                       *rpc.Product
	}{
		{
			name: "With aloha pricing",
			prices: map[string]*pricing.Product{
				"3077313021258": {
					Price: &pricing.Price{
						NormalPrice:       25000,
						PromoPrice:        25000,
						StoreNormalPrice:  25000,
						StorePromoPrice:   25000,
						PromoCost:         25000,
						Quantity:          3,
						ThresholdQuantity: 3,
						PriceSource:       "CONTENT",
					},
				},
			},
			currencyCode:             "",
			countryCode:              "ID",
			isMPQEligible:            false,
			disableLimitedStockQty:   false,
			expectedPromotionActions: []string{},
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						SupermarketUnit: "bundle",
					},
				},
			},
			to: &rpc.Product{},
		},
		{
			name: "With aloha pricing promotion types pricing",
			prices: map[string]*pricing.Product{
				"3077313021258": {
					Price: &pricing.Price{
						NormalPrice:       25000,
						PromoPrice:        20000,
						StoreNormalPrice:  25000,
						StorePromoPrice:   25000,
						PromoCost:         25000,
						Quantity:          3,
						ThresholdQuantity: 3,
						PriceSource:       "CONTENT",
					},
				},
			},
			currencyCode:             "",
			countryCode:              "ID",
			isMPQEligible:            false,
			disableLimitedStockQty:   false,
			expectedPromotionActions: []string{"price"},
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						SupermarketUnit: "bundle",
					},
				},
			},
			to: &rpc.Product{},
		},
		{
			name: "With aloha pricing non bundle",
			prices: map[string]*pricing.Product{
				"3077313021258": {
					Price: &pricing.Price{
						NormalPrice:       25000,
						PromoPrice:        25000,
						StoreNormalPrice:  25000,
						StorePromoPrice:   25000,
						PromoCost:         25000,
						Quantity:          3,
						ThresholdQuantity: 3,
						PriceSource:       "CONTENT",
					},
				},
			},
			currencyCode:             "",
			countryCode:              "ID",
			isMPQEligible:            false,
			disableLimitedStockQty:   false,
			expectedPromotionActions: []string{},
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						SupermarketUnit: "bundle",
					},
				},
			},
			to: &rpc.Product{},
		},
		{
			name: "Will add bundle to promotion types",
			prices: map[string]*pricing.Product{
				"3077313021258": {
					Price: &pricing.Price{
						NormalPrice:       25000,
						PromoPrice:        25000,
						StoreNormalPrice:  25000,
						StorePromoPrice:   25000,
						PromoCost:         25000,
						Quantity:          3,
						ThresholdQuantity: 3,
						PriceSource:       "BUNDLE_HA",
					},
				},
			},
			currencyCode:             "",
			countryCode:              "ID",
			isMPQEligible:            false,
			disableLimitedStockQty:   false,
			expectedPromotionActions: []string{"get_y_action", "bundle"},
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						SupermarketUnit: "bundle",
					},
				},
			},
			to: &rpc.Product{
				PromotionTypes: []string{"get_y_action"},
			},
		},
		{
			name: "With aloha pricing will prioritize bundle rather than price promotion",
			prices: map[string]*pricing.Product{
				"3077313021258": {
					Price: &pricing.Price{
						NormalPrice:       25000,
						PromoPrice:        20000,
						StoreNormalPrice:  25000,
						StorePromoPrice:   25000,
						PromoCost:         25000,
						Quantity:          3,
						ThresholdQuantity: 3,
						PriceSource:       "BUNDLE_HA",
					},
				},
			},
			currencyCode:             "",
			countryCode:              "ID",
			isMPQEligible:            false,
			disableLimitedStockQty:   false,
			expectedPromotionActions: []string{"bundle"},
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						SupermarketUnit: "bundle",
					},
				},
			},
			to: &rpc.Product{},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithAlohaPricing(testCase.prices, testCase.currencyCode, testCase.countryCode, "en", testCase.isMPQEligible, testCase.disableLimitedStockQty)
			f(testCase.from, testCase.to, 0)

			assert.Equal(t, testCase.expectedPromotionActions, testCase.to.PromotionTypes)

			if testCase.to.PricingEngine != "aloha" {
				t.Errorf("\nexpectation: %v\nreality: %v", "aloha", testCase.to.DisplayPromotionActionsCombinationText)
			}
		})
	}
}

func TestWithMultipleAlohaPricing(t *testing.T) {
	testCases := []struct {
		name                   string
		pricesPerSLI           map[int64]map[string]*pricing.Product
		currencyCode           string
		countryCode            string
		isMPQEligible          bool
		from                   *cat.StoreProducts
		expectedShowPrice      bool
		expectedIsLimitedStock bool
	}{
		{
			name: "Aloha pricing with multiple SLI",
			pricesPerSLI: map[int64]map[string]*pricing.Product{
				3: {
					"3077313021258": {
						Price: &pricing.Price{
							NormalPrice:             25000,
							PromoPrice:              25000,
							StoreNormalPrice:        25000,
							StorePromoPrice:         25000,
							PromoCost:               25000,
							Quantity:                4,
							ThresholdQuantity:       4,
							StoreUseLimitedQuantity: true,
						},
					},
				},
			},
			currencyCode:  "",
			countryCode:   "ID",
			isMPQEligible: false,
			from: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{
					{
						StockLocationId: 3,
					},
				},
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						SupermarketUnit: "bundle",
					},
				},
			},
			expectedShowPrice:      true,
			expectedIsLimitedStock: false, // ltd stock disabled for multiple aloha pricing
		},
		{
			name:          "With hidden price",
			pricesPerSLI:  map[int64]map[string]*pricing.Product{},
			currencyCode:  "",
			countryCode:   "ID",
			isMPQEligible: false,
			from: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{
					{
						StockLocationId: 3,
					},
				},
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						SupermarketUnit: "bundle",
					},
				},
			},
			expectedShowPrice:      false,
			expectedIsLimitedStock: false,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithMultipleAlohaPricing(testCase.pricesPerSLI, testCase.currencyCode, testCase.countryCode, "en", testCase.isMPQEligible)
			to := &rpc.Product{
				ShowPrice: true,
			}

			f(testCase.from, to, 0)

			assert := assert.New(t)

			assert.Equal(testCase.expectedShowPrice, to.ShowPrice)
			assert.Equal(testCase.expectedIsLimitedStock, to.IsLimitedStock)

			if testCase.expectedShowPrice {
				assert.Equal("aloha", to.PricingEngine)
			}
		})
	}
}

func TestWithStorePriceIncluded(t *testing.T) {
	testCases := []struct {
		name                                    string
		includeStorePrice                       bool
		from                                    *cat.StoreProducts
		to                                      *rpc.Product
		expectedStoreNormalPrice                string
		expectedDisplayStoreNormalPrice         string
		expectedStorePromoPrice                 string
		expectedDisplayStorePromoPrice          string
		expectedCost                            string
		expectedDisplayCost                     string
		expectedSupermarketUnitCostPrice        string
		expectedDisplaySupermarketUnitCostPrice string
	}{
		{
			name:              "Without store price",
			includeStorePrice: false,
			from:              &cat.StoreProducts{},
			to: &rpc.Product{
				StoreNormalPrice:                "36000",
				DisplayStoreNormalPrice:         "Rp36,000",
				StorePromoPrice:                 "36000",
				DisplayStorePromoPrice:          "Rp36,000",
				Cost:                            "36000",
				DisplayCost:                     "Rp36,000",
				SupermarketUnitCostPrice:        "7600",
				DisplaySupermarketUnitCostPrice: "Rp7,600 / 100 g",
			},
			expectedStoreNormalPrice:                "",
			expectedDisplayStoreNormalPrice:         "",
			expectedStorePromoPrice:                 "",
			expectedDisplayStorePromoPrice:          "",
			expectedCost:                            "",
			expectedDisplayCost:                     "",
			expectedSupermarketUnitCostPrice:        "",
			expectedDisplaySupermarketUnitCostPrice: "",
		},
		{
			name:              "With store price",
			includeStorePrice: true,
			from:              &cat.StoreProducts{},
			to: &rpc.Product{
				StoreNormalPrice:                "36000",
				DisplayStoreNormalPrice:         "Rp36,000",
				StorePromoPrice:                 "36000",
				DisplayStorePromoPrice:          "Rp36,000",
				Cost:                            "36000",
				DisplayCost:                     "Rp36,000",
				SupermarketUnitCostPrice:        "7600",
				DisplaySupermarketUnitCostPrice: "Rp7,600 / 100 g",
			},
			expectedStoreNormalPrice:                "36000",
			expectedDisplayStoreNormalPrice:         "Rp36,000",
			expectedStorePromoPrice:                 "36000",
			expectedDisplayStorePromoPrice:          "Rp36,000",
			expectedCost:                            "36000",
			expectedDisplayCost:                     "Rp36,000",
			expectedSupermarketUnitCostPrice:        "7600",
			expectedDisplaySupermarketUnitCostPrice: "Rp7,600 / 100 g",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithStorePriceIncluded(testCase.includeStorePrice)
			f(testCase.from, testCase.to, 0)

			if testCase.to.StoreNormalPrice != testCase.expectedStoreNormalPrice {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedStoreNormalPrice, testCase.to.StoreNormalPrice)
			}
			if testCase.to.DisplayStoreNormalPrice != testCase.expectedDisplayStoreNormalPrice {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedDisplayStoreNormalPrice, testCase.to.DisplayStoreNormalPrice)
			}
			if testCase.to.StorePromoPrice != testCase.expectedStorePromoPrice {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedStorePromoPrice, testCase.to.StorePromoPrice)
			}
			if testCase.to.DisplayStorePromoPrice != testCase.expectedDisplayStorePromoPrice {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedDisplayStorePromoPrice, testCase.to.DisplayStorePromoPrice)
			}
			if testCase.to.Cost != testCase.expectedCost {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedCost, testCase.to.Cost)
			}
			if testCase.to.DisplayCost != testCase.expectedDisplayCost {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedDisplayCost, testCase.to.DisplayCost)
			}
			if testCase.to.SupermarketUnitCostPrice != testCase.expectedSupermarketUnitCostPrice {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedSupermarketUnitCostPrice, testCase.to.SupermarketUnitCostPrice)
			}
			if testCase.to.DisplaySupermarketUnitCostPrice != testCase.expectedDisplaySupermarketUnitCostPrice {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedDisplaySupermarketUnitCostPrice, testCase.to.DisplaySupermarketUnitCostPrice)
			}
		})
	}
}

func TestWithSearchScore(t *testing.T) {
	testCases := []struct {
		name                 string
		productsScore        map[int64]float64
		from                 *cat.StoreProducts
		to                   *rpc.Product
		expectedProductScore float64
	}{
		{
			name: "With raw popularity",
			productsScore: map[int64]float64{
				19175: 25.6,
			},
			from: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{
					{
						StoreProductId: 19175,
					},
				},
			},
			to:                   &rpc.Product{},
			expectedProductScore: 25.6,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithSearchScore(testCase.productsScore)
			f(testCase.from, testCase.to, 0)

			if testCase.to.Score != testCase.expectedProductScore {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedProductScore, testCase.to.Score)
			}
		})
	}
}

func TestWithRawPopularity(t *testing.T) {
	testCases := []struct {
		name                  string
		productsRawPopularity map[int64]float64
		from                  *cat.StoreProducts
		to                    *rpc.Product
		expectedRawPopularity float64
	}{
		{
			name: "With raw popularity",
			productsRawPopularity: map[int64]float64{
				19175: 25.6,
			},
			from: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{
					{
						StoreProductId: 19175,
					},
				},
			},
			to:                    &rpc.Product{},
			expectedRawPopularity: 25.6,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithRawPopularity(testCase.productsRawPopularity)
			f(testCase.from, testCase.to, 0)

			if testCase.to.RawPopularity != float64(testCase.expectedRawPopularity) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedRawPopularity, testCase.to.RawPopularity)
			}
		})
	}
}

func TestWithWithTranslatedIDs(t *testing.T) {
	testCases := []struct {
		name                   string
		translatedIDs          []*hubbleRPC.TranslatedID
		idxTranslatedIDs       int
		from                   *cat.StoreProducts
		to                     *rpc.Product
		expectedSpreeProductID int64
		expectedSpreeVariantID int64
		expectedSpreeBrandID   int64
	}{
		{
			name: "With raw popularity",
			translatedIDs: []*hubbleRPC.TranslatedID{
				{
					SpreeProductId: 101,
					SpreeVariantId: 1651,
					SpreeBrandId:   1791,
				},
			},
			idxTranslatedIDs: 0,
			from:             &cat.StoreProducts{},
			to: &rpc.Product{
				Variants: []*rpc.Variant{{}},
				Brand:    &rpc.Brand{},
			},
			expectedSpreeProductID: 101,
			expectedSpreeVariantID: 1651,
			expectedSpreeBrandID:   1791,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithTranslatedIDs(testCase.translatedIDs)
			f(testCase.from, testCase.to, testCase.idxTranslatedIDs)

			if testCase.to.Id != testCase.expectedSpreeProductID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedSpreeProductID, testCase.to.Id)
			}
			if testCase.to.VariantId != testCase.expectedSpreeVariantID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedSpreeVariantID, testCase.to.VariantId)
			}
			if testCase.to.Variants[0].Id != testCase.expectedSpreeVariantID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedSpreeVariantID, testCase.to.Variants[0].Id)
			}
			if testCase.to.Brand.Id != testCase.expectedSpreeBrandID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedSpreeBrandID, testCase.to.Brand.Id)
			}
		})
	}
}

func TestWithGlobalIDs(t *testing.T) {
	testCases := []struct {
		name                    string
		isSynthetic             bool
		from                    *cat.StoreProducts
		to                      *rpc.Product
		expectedProductGlobalID int64
		expectedVariantGlobalID int64
		expectedBrandGlobalID   int64
	}{
		{
			name:                    "Synthetic false",
			isSynthetic:             false,
			from:                    &cat.StoreProducts{},
			to:                      &rpc.Product{},
			expectedProductGlobalID: 0,
			expectedVariantGlobalID: 0,
			expectedBrandGlobalID:   0,
		},
		{
			name:        "Synthetic true",
			isSynthetic: true,
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					ProductId: 50,
					Variants: []*cat.Variant{
						{
							VariantId: 65,
						},
					},
					Brand: &cat.Brand{
						BrandId: 80,
					},
				},
			},
			to:                      &rpc.Product{},
			expectedProductGlobalID: 50,
			expectedVariantGlobalID: 65,
			expectedBrandGlobalID:   80,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithGlobalIDs(testCase.isSynthetic)
			f(testCase.from, testCase.to, 0)

			if testCase.to.ProductGlobalId != testCase.expectedProductGlobalID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedProductGlobalID, testCase.to.ProductGlobalId)
			}
			if testCase.to.VariantGlobalId != testCase.expectedVariantGlobalID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedVariantGlobalID, testCase.to.VariantGlobalId)
			}
			if testCase.to.BrandGlobalId != testCase.expectedBrandGlobalID {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedBrandGlobalID, testCase.to.BrandGlobalId)
			}
		})
	}
}

func TestWithStockLocationID(t *testing.T) {
	t.Run("", func(t *testing.T) {
		storeProduct := &cat.StoreProducts{
			StoreProducts: []*cat.StoreProduct{
				{
					StockLocationId: 3,
				},
			},
		}
		product := &rpc.Product{
			StockLocationId: 3,
		}

		resFunc := WithStockLocationID()
		resFunc(storeProduct, product, 0)

		if storeProduct.StoreProducts[0].StockLocationId != product.StockLocationId {
			t.Errorf("\nexpectation: %v\nreality: %v", storeProduct.StoreProducts[0].StockLocationId, product.StockLocationId)
		}
	})
}

func TestProductMOQ(t *testing.T) {
	testCases := []struct {
		name                   string
		userType               string
		maxMOQ                 int64
		maxMOQHC               int64
		disableLimitedStockQty bool
		from                   *cat.StoreProducts
		to                     *rpc.Product
		expectedMaxMOQ         int64
		countryCode            string
		prices                 map[string]*pricing.Product
	}{
		{
			name:                   "User type HC",
			userType:               "hc",
			maxMOQ:                 10,
			maxMOQHC:               25,
			disableLimitedStockQty: false,
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						MaxOrderQuantity: 50,
					},
				},
			},
			to:             &rpc.Product{},
			expectedMaxMOQ: 25,
			countryCode:    "id",
			prices: map[string]*pricing.Product{
				"3077313021258": {
					Price: &pricing.Price{
						NormalPrice:       25000,
						PromoPrice:        25000,
						StoreNormalPrice:  25000,
						StorePromoPrice:   25000,
						PromoCost:         25000,
						Quantity:          25,
						ThresholdQuantity: 3,
					},
				},
			},
		},
		{
			name:                   "User type Non HC",
			userType:               "non-hc",
			maxMOQ:                 10,
			maxMOQHC:               25,
			disableLimitedStockQty: false,
			from: &cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					Variants: []*cat.Variant{
						{
							Sku: "3077313021258-ID",
						},
					},
					Properties: &cat.Properties{
						MaxOrderQuantity: 50,
					},
				},
			},
			to:             &rpc.Product{},
			expectedMaxMOQ: 10,
			countryCode:    "id",
			prices: map[string]*pricing.Product{
				"3077313021258": {
					Price: &pricing.Price{
						NormalPrice:       25000,
						PromoPrice:        25000,
						StoreNormalPrice:  25000,
						StorePromoPrice:   25000,
						PromoCost:         25000,
						Quantity:          10,
						ThresholdQuantity: 3,
					},
				},
			},
		},
		// TODO: Re-enable MoQ in HFS once stock from pricing is consistent again
		// {
		// 	name:                   "Quantity below MOQ",
		// 	userType:               "non-hc",
		// 	maxMOQ:                 10,
		// 	maxMOQHC:               25,
		// 	disableLimitedStockQty: false,
		// 	from: &cat.StoreProducts{
		// 		ProductDetail: &cat.ProductDetail{
		// 			Variants: []*cat.Variant{
		// 				{
		// 					Sku: "3077313021258-ID",
		// 				},
		// 			},
		// 			Properties: &cat.Properties{
		// 				MaxOrderQuantity: 50,
		// 			},
		// 		},
		// 	},
		// 	to:             &rpc.Product{},
		// 	expectedMaxMOQ: 5,
		// 	countryCode:    "id",
		// 	prices: map[string]*pricing.Product{
		// 		"3077313021258": {
		// 			Price: &pricing.Price{
		// 				NormalPrice:       25000,
		// 				PromoPrice:        25000,
		// 				StoreNormalPrice:  25000,
		// 				StorePromoPrice:   25000,
		// 				PromoCost:         25000,
		// 				Quantity:          5,
		// 				ThresholdQuantity: 3,
		// 			},
		// 		},
		// 	},
		// },
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithProductMOQ(testCase.prices, testCase.userType, testCase.maxMOQ, testCase.maxMOQHC, testCase.countryCode, testCase.disableLimitedStockQty)
			f(testCase.from, testCase.to, 0)

			if testCase.to.MaxOrderQuantity != testCase.expectedMaxMOQ {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedMaxMOQ, testCase.to.MaxOrderQuantity)
			}
		})
	}
}

func TestMapTaxonFilter(t *testing.T) {
	testCases := []struct {
		testName         string
		SearchResponse   *index.SearchResponse
		expectedTaxonIDs []int64
	}{
		{
			testName:         "Empty Taxon List",
			SearchResponse:   &index.SearchResponse{},
			expectedTaxonIDs: []int64{},
		},
		{
			testName: "Non-empty Taxon List",
			SearchResponse: &index.SearchResponse{
				Filters: map[string][]interface{}{
					"taxons": {10, 15, 20, 25, 30},
				},
			},
			expectedTaxonIDs: []int64{10, 15, 20, 25, 30},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.testName, func(t *testing.T) {
			ctx := context.Background()

			taxonIDs, err := MapTaxonFilter(ctx, testCase.SearchResponse)
			if err != nil {
				t.Errorf("\nexpectation: <nil>\nreality: %v", err)
			}

			if len(taxonIDs) != len(testCase.expectedTaxonIDs) {
				t.Errorf("\nexpectation: %v\nreality: %v", len(testCase.expectedTaxonIDs), len(taxonIDs))
			}

			if !reflect.DeepEqual(taxonIDs, testCase.expectedTaxonIDs) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedTaxonIDs, taxonIDs)
			}
		})
	}
}

func TestMapToBrandIDs(t *testing.T) {
	testCases := []struct {
		testName         string
		SearchResponse   *index.SearchResponse
		expectedBrandIDs []int64
	}{
		{
			"Empty brand list",
			&index.SearchResponse{},
			nil,
		},
		{
			"With brand list",
			&index.SearchResponse{
				Filters: map[string][]interface{}{
					"brands": {
						&index.Brand{
							BrandID: 501,
						},
						&index.Brand{
							BrandID: 505,
						},
					},
				},
			},
			[]int64{501, 505},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.testName, func(t *testing.T) {
			ctx := context.Background()

			brands, err := MapToBrandIDs(ctx, testCase.SearchResponse)
			if err != nil {
				t.Errorf("\nexpectation: <nil>\nreality: %v", err)
			}

			if len(brands) != len(testCase.expectedBrandIDs) {
				t.Errorf("\nexpectation: %v\nreality: %v", len(testCase.expectedBrandIDs), len(brands))
			}

			if !reflect.DeepEqual(brands, testCase.expectedBrandIDs) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedBrandIDs, brands)
			}
		})
	}
}

func TestMapBrandFilter(t *testing.T) {
	testCases := []struct {
		testName       string
		SearchResponse []*cat.Brand
		expectedBrands []*rpc.Brand
	}{
		{
			"Empty brand list",
			[]*cat.Brand{},
			[]*rpc.Brand{},
		},
		{
			"With brand list",
			[]*cat.Brand{
				{
					BrandId:   501,
					NameLocal: "Kokita",
				},
				{
					BrandId:   505,
					NameLocal: "Sari Roti",
				},
			},
			[]*rpc.Brand{
				{
					Id:   501,
					Name: "Kokita",
				},
				{
					Id:   505,
					Name: "Sari Roti",
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.testName, func(t *testing.T) {
			ctx := context.Background()

			brands, err := MapBrandFilter(ctx, testCase.SearchResponse)
			if err != nil {
				t.Errorf("\nexpectation: <nil>\nreality: %v", err)
			}

			if len(brands) != len(testCase.expectedBrands) {
				t.Errorf("\nexpectation: %v\nreality: %v", len(testCase.expectedBrands), len(brands))
			}

			if !reflect.DeepEqual(brands, testCase.expectedBrands) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedBrands, brands)
			}
		})
	}
}

func TestMapStockLocationID(t *testing.T) {
	testCases := []struct {
		testName           string
		SearchResponse     *index.SearchResponse
		expectedStockItems []index.StockItem
	}{
		{
			testName:           "Empty stock items",
			SearchResponse:     &index.SearchResponse{},
			expectedStockItems: []index.StockItem{},
		},
		{
			testName: "With stock items",
			SearchResponse: &index.SearchResponse{
				Filters: map[string][]interface{}{
					"stock_location_ids": {
						index.StockItem{
							StockLocationID: 3,
						},
					},
				},
			},
			expectedStockItems: []index.StockItem{
				{
					StockLocationID: 3,
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.testName, func(t *testing.T) {
			ctx := context.Background()

			stockItems, err := MapStockLocationID(ctx, testCase.SearchResponse)
			if err != nil {
				t.Errorf("\nexpectation: <nil>\nreality: %v", err)
			}

			if len(stockItems) != len(testCase.expectedStockItems) {
				t.Errorf("\nexpectation: %v\nreality: %v", len(testCase.expectedStockItems), len(stockItems))
			}

			if !reflect.DeepEqual(stockItems, testCase.expectedStockItems) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedStockItems, stockItems)
			}
		})
	}
}
