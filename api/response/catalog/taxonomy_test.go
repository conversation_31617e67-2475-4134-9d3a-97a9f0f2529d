package catalog

import (
	"testing"

	"github.com/stretchr/testify/assert"
	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/search/api/rpc"
)

func TestMapTaxonomyList(t *testing.T) {
	for _, tc := range []struct {
		testName   string
		taxonomies *cat.Taxonomy
		output     *rpc.TaxonomyListResponse
		opts       []OptionTaxonomyList
	}{
		{
			testName:   "Empty taxon",
			taxonomies: &cat.Taxonomy{},
			output:     &rpc.TaxonomyListResponse{},
		},
		{
			testName: "Taxon Exist With sort",
			taxonomies: &cat.Taxonomy{
				Taxons: []*cat.Taxon{
					{
						Taxons: []*cat.Taxon{
							{
								TaxonId:         111,
								Name:            "Oils & Vinegars",
								ParentId:        48,
								Description:     "oils & vinegars description",
								Permalink:       "pantry/oils-and-vinegars",
								TaxonomyId:      1,
								MetaDescription: "oils & vinegars meta description",
								MetaKeywords:    "oils & vinegars meta keywords",
								MetaTitle:       "oils & vinegars meta title",
								Position:        311,
								DisplayImage: &cat.Image{
									Id:         84806,
									MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
									ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
								},
								SortingUnitPrice: false,
								Slug:             "oils-vinegars-111",
								IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
								ProductsCount:    1,
							},
						},
						TaxonId:         48,
						ParentId:        1,
						Name:            "Pantry",
						Description:     "pantry description",
						Permalink:       "pantry",
						TaxonomyId:      1,
						MetaDescription: "pantry meta description",
						MetaKeywords:    "pantry meta keywords",
						MetaTitle:       "pantry meta title",
						Position:        300,
						DisplayImage: &cat.Image{
							Id:         84806,
							MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: false,
						Slug:             "pantry-48",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
					},
					{
						Taxons:          []*cat.Taxon{},
						TaxonId:         96,
						ParentId:        1,
						Name:            "Household",
						Description:     "household description",
						Permalink:       "household",
						TaxonomyId:      1,
						MetaDescription: "household meta description",
						MetaKeywords:    "household meta keywords",
						MetaTitle:       "household meta title",
						Position:        330,
						DisplayImage: &cat.Image{
							Id:         1616998,
							MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
							ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
						},
						SortingUnitPrice: false,
						Slug:             "household-96",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
						ProductsCount:    1,
					},
				},
			},
			output: &rpc.TaxonomyListResponse{
				Taxons: []*rpc.TaxonList{
					{
						Taxons:          []*rpc.TaxonList{},
						Id:              96,
						ParentId:        1,
						Name:            "Household",
						Description:     "household description",
						Permalink:       "household",
						TaxonomyId:      1,
						MetaDescription: "household meta description",
						MetaKeywords:    "household meta keywords",
						MetaTitle:       "household meta title",
						Position:        330,
						DisplayImage: &rpc.TaxonListImage{
							Id:     1616998,
							Mini:   "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
							Normal: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
						},
						SortingUnitPrice: false,
						Slug:             "household-96",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/e3946057e43e81c5329b27783b966f5cd03d350e-original.jpg",
						ProductsCount:    1,
					},
					{
						Taxons: []*rpc.TaxonList{
							{
								Id:              111,
								Name:            "Oils & Vinegars",
								ParentId:        48,
								Description:     "oils & vinegars description",
								Permalink:       "pantry/oils-and-vinegars",
								TaxonomyId:      1,
								MetaDescription: "oils & vinegars meta description",
								MetaKeywords:    "oils & vinegars meta keywords",
								MetaTitle:       "oils & vinegars meta title",
								Position:        311,
								DisplayImage: &rpc.TaxonListImage{
									Id:     84806,
									Mini:   "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
									Normal: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
								},
								SortingUnitPrice: false,
								Slug:             "oils-vinegars-111",
								IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
								ProductsCount:    1,
							},
						},
						Id:              48,
						ParentId:        1,
						Name:            "Pantry",
						Description:     "pantry description",
						Permalink:       "pantry",
						TaxonomyId:      1,
						MetaDescription: "pantry meta description",
						MetaKeywords:    "pantry meta keywords",
						MetaTitle:       "pantry meta title",
						Position:        300,
						DisplayImage: &rpc.TaxonListImage{
							Id:     84806,
							Mini:   "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							Normal: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: false,
						Slug:             "pantry-48",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
					},
				},
			},
			opts: []OptionTaxonomyList{
				WithSortByTaxonRank(map[int64]int64{96: 1, 48: 2}),
			},
		},
		{
			testName: "With category boosting",
			taxonomies: &cat.Taxonomy{
				Taxons: []*cat.Taxon{
					{
						TaxonId:         2,
						Name:            "Fresh Produce",
						ParentId:        0,
						Description:     "fresh produce description",
						Permalink:       "fresh-produce",
						TaxonomyId:      1,
						MetaDescription: "fresh produce meta description",
						MetaKeywords:    "fresh produce meta keywords",
						MetaTitle:       "fresh produce meta title",
						Position:        0,
						DisplayImage: &cat.Image{
							Id:         84806,
							MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "fresh-produce-2",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
					{
						TaxonId:         7,
						Name:            "Meat & Seafood",
						ParentId:        0,
						Description:     "meat and seafood description",
						Permalink:       "meat-and-seafood",
						TaxonomyId:      1,
						MetaDescription: "meat and seafood meta description",
						MetaKeywords:    "meat and seafood meta keywords",
						MetaTitle:       "meat and seafood meta title",
						Position:        0,
						DisplayImage: &cat.Image{
							Id:         84806,
							MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "meat-seafood-7",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
					{
						TaxonId:         53,
						Name:            "Beverages",
						ParentId:        0,
						Description:     "beverages description",
						Permalink:       "beverages",
						TaxonomyId:      1,
						MetaDescription: "beverages meta description",
						MetaKeywords:    "beverages meta keywords",
						MetaTitle:       "beverages meta title",
						Position:        0,
						DisplayImage: &cat.Image{
							Id:         84806,
							MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "beverages-53",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
					{
						TaxonId:         178,
						Name:            "Grapes",
						ParentId:        0,
						Description:     "grapes description",
						Permalink:       "grapes",
						TaxonomyId:      1,
						MetaDescription: "grapes meta description",
						MetaKeywords:    "grapes meta keywords",
						MetaTitle:       "grapes meta title",
						Position:        0,
						DisplayImage: &cat.Image{
							Id:         84806,
							MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "grapes-178",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
					{
						TaxonId:         291,
						Name:            "Health & Wellness",
						ParentId:        0,
						Description:     "health and wellness description",
						Permalink:       "health-and-wellness",
						TaxonomyId:      1,
						MetaDescription: "health and wellness meta description",
						MetaKeywords:    "health and wellness meta keywords",
						MetaTitle:       "health and wellness meta title",
						Position:        0,
						DisplayImage: &cat.Image{
							Id:         84806,
							MiniUrl:    "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							ProductUrl: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "health-wellness-291",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
				},
			},
			output: &rpc.TaxonomyListResponse{
				Taxons: []*rpc.TaxonList{
					{
						Id:              53,
						Name:            "Beverages",
						ParentId:        0,
						Description:     "beverages description",
						Permalink:       "beverages",
						TaxonomyId:      1,
						MetaDescription: "beverages meta description",
						MetaKeywords:    "beverages meta keywords",
						MetaTitle:       "beverages meta title",
						Position:        0,
						DisplayImage: &rpc.TaxonListImage{
							Id:     84806,
							Mini:   "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							Normal: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "beverages-53",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
					{
						Id:              291,
						Name:            "Health & Wellness",
						ParentId:        0,
						Description:     "health and wellness description",
						Permalink:       "health-and-wellness",
						TaxonomyId:      1,
						MetaDescription: "health and wellness meta description",
						MetaKeywords:    "health and wellness meta keywords",
						MetaTitle:       "health and wellness meta title",
						Position:        0,
						DisplayImage: &rpc.TaxonListImage{
							Id:     84806,
							Mini:   "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							Normal: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "health-wellness-291",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
					{
						Id:              2,
						Name:            "Fresh Produce",
						ParentId:        0,
						Description:     "fresh produce description",
						Permalink:       "fresh-produce",
						TaxonomyId:      1,
						MetaDescription: "fresh produce meta description",
						MetaKeywords:    "fresh produce meta keywords",
						MetaTitle:       "fresh produce meta title",
						Position:        0,
						DisplayImage: &rpc.TaxonListImage{
							Id:     84806,
							Mini:   "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							Normal: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "fresh-produce-2",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
					{
						Id:              7,
						Name:            "Meat & Seafood",
						ParentId:        0,
						Description:     "meat and seafood description",
						Permalink:       "meat-and-seafood",
						TaxonomyId:      1,
						MetaDescription: "meat and seafood meta description",
						MetaKeywords:    "meat and seafood meta keywords",
						MetaTitle:       "meat and seafood meta title",
						Position:        0,
						DisplayImage: &rpc.TaxonListImage{
							Id:     84806,
							Mini:   "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							Normal: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "meat-seafood-7",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
					{
						Id:              178,
						Name:            "Grapes",
						ParentId:        0,
						Description:     "grapes description",
						Permalink:       "grapes",
						TaxonomyId:      1,
						MetaDescription: "grapes meta description",
						MetaKeywords:    "grapes meta keywords",
						MetaTitle:       "grapes meta title",
						Position:        0,
						DisplayImage: &rpc.TaxonListImage{
							Id:     84806,
							Mini:   "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
							Normal: "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						},
						SortingUnitPrice: true,
						Slug:             "grapes-178",
						IconUrl:          "https://icdn.happyfresh.com/t/s_large,c_fit/spree/images/attachments/622c7ae8ba09c7df88cc6a67475614d1871121f2-original.jpg",
						ProductsCount:    1,
					},
				},
			},
			opts: []OptionTaxonomyList{
				WithSortByTaxonPins(
					&cat.TaxonPins{TaxonPins: []*cat.TaxonPin{
						{
							TaxonId:     53,
							CountryCode: "ID",
							Position:    1,
						},
						{
							TaxonId:     291,
							CountryCode: "ID",
							Position:    2,
						},
						{
							TaxonId:     2,
							CountryCode: "ID",
							Position:    3,
						},
					}},
				),
			},
		},
	} {
		t.Run(tc.testName, func(t *testing.T) {
			output := MapTaxonomyList(tc.taxonomies, OptionChain(tc.opts...), BuilderOptionChain())

			assert.Equal(t, len(output.Taxons), len(tc.output.Taxons))

			// check sort
			for index, taxon := range output.Taxons {
				assert.Equal(t, tc.output.Taxons[index].Id, taxon.Id)
			}
		})
	}
}

func TestWithSortByTaxonRank(t *testing.T) {
	assert := assert.New(t)
	taxonomy := &cat.Taxonomy{}
	taxonomyListResponse := &rpc.TaxonomyListResponse{
		Taxons: []*rpc.TaxonList{
			{
				Id:       int64(5),
				Position: 3,
			},
			{
				Id:       int64(4),
				Position: 2,
			},
		},
	}

	// Test both taxons matched
	returnedFn := WithSortByTaxonRank(map[int64]int64{5: 1, 4: 2})
	returnedFn(taxonomy, taxonomyListResponse)
	assert.Equal(int64(5), taxonomyListResponse.Taxons[0].Id)
	assert.Equal(int64(4), taxonomyListResponse.Taxons[1].Id)

	// Test only one taxon matched
	returnedFn = WithSortByTaxonRank(map[int64]int64{50: 1, 4: 2})
	returnedFn(taxonomy, taxonomyListResponse)
	assert.Equal(int64(4), taxonomyListResponse.Taxons[0].Id)
	assert.Equal(int64(5), taxonomyListResponse.Taxons[1].Id)

	// Test no taxons matched
	returnedFn = WithSortByTaxonRank(map[int64]int64{50: 1, 40: 2})
	returnedFn(taxonomy, taxonomyListResponse)
	assert.Equal(int64(4), taxonomyListResponse.Taxons[0].Id)
	assert.Equal(int64(5), taxonomyListResponse.Taxons[1].Id)

}
