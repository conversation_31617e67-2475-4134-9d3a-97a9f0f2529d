package catalog

import (
	"context"
	"testing"

	cat "happyfresh.io/catalog/lib/rpc/api"
	hubbleRPC "happyfresh.io/hubble/lib/rpc"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/core/pricing"
	"happyfresh.io/search/core/promotion"

	"github.com/stretchr/testify/assert"
)

func TestMap(t *testing.T) {
	assert := assert.New(t)

	ctx := context.Background()
	productDetail := &cat.ProductDetail{
		Vendor: &cat.Vendor{
			VendorId: 123,
		},
	}
	version := "3.34"
	opts := []Option{}

	productDetailResponse, _ := Map(ctx, version, productDetail, opts...)

	assert.Equal(productDetail.GetProductId(), productDetailResponse.Id)
	assert.Equal(int64(123), productDetailResponse.Vendor.Id)
	assert.Equal(version, productDetailResponse.EngineVersion)
}

func TestWithLocale(t *testing.T) {
	assert := assert.New(t)

	productDetail := &cat.ProductDetail{
		Name:        "product name",
		Description: "product description",
		Vendor: &cat.Vendor{
			Description:      "product vendor description",
			DescriptionLocal: "product vendor description local",
		},
	}

	productDetailResponse := &rpc.ProductDetailResponse{
		Brand:    &rpc.Brand{},
		Variants: []*rpc.Variant{{}},
		Vendor: &rpc.Vendor{
			Description: "",
		},
	}

	WithLocale("en")(productDetail, productDetailResponse)

	assert.Equal(productDetail.Name, productDetailResponse.GetName())
	assert.Equal("product vendor description", productDetailResponse.Vendor.Description)

	WithLocale("id")(productDetail, productDetailResponse)
	assert.Equal("product vendor description local", productDetailResponse.Vendor.Description)

	productDetail.Vendor = nil
	WithLocale("id")(productDetail, productDetailResponse)
	assert.NotEqual("product vendor description local", productDetailResponse.Vendor.Description)

}

func TestWithAlohaPricingSkuNotFound(t *testing.T) {

	productPrices := map[string]*pricing.Product{
		"ASkuInProducts": {},
	}
	productDetail := &cat.ProductDetail{
		Variants: []*cat.Variant{
			{
				Sku: "DifferentSKUProduct",
			},
		},
	}
	productDetailResponse := &rpc.ProductDetailResponse{}

	returnedFn := WithAlohaPricing(productPrices, "id", "IDR", "en", false)
	returnedFn(productDetail, productDetailResponse)

	assert.NotEqual(t, "aloha", productDetailResponse.PricingEngine)

}

func TestWithAlohaPricing(t *testing.T) {
	assert := assert.New(t)
	skuName := "DummySKU"
	countryCode := "id"
	currencyCode := "IDR"
	isMPQEligible := false

	type TableTest struct {
		testName               string
		productPrices          map[string]*pricing.Product
		productDetail          *cat.ProductDetail
		productDetailResponse  *rpc.ProductDetailResponse
		expectedPromotionTypes []string
	}

	productDetail := &cat.ProductDetail{
		Name:        "product name",
		Description: "product description",
		Variants: []*cat.Variant{
			{
				Sku: skuName,
			},
		},
		Properties: &cat.Properties{
			SupermarketUnit: "100 ml",
			AverageWeight:   2,
		},
	}

	tableTest := []*TableTest{
		{
			testName: "All conditions are true",
			productPrices: map[string]*pricing.Product{
				skuName: {
					Price: &pricing.Price{
						NormalPrice:      float64(100),
						PromoPrice:       float64(90),
						StoreNormalPrice: float64(85),
						StorePromoPrice:  float64(80),
					},
					InStock: true,
					Promotions: []*pricing.Promotion{
						{
							Rules: map[string]string{
								"moq": "5",
							},
						},
						{
							Rules: nil,
						},
						{
							Rules: map[string]string{
								"moq": "xx",
							},
						},
					},
				},
			},
			productDetail: productDetail,
			productDetailResponse: &rpc.ProductDetailResponse{
				Brand:    &rpc.Brand{},
				Variants: []*rpc.Variant{{}},
				Vendor: &rpc.Vendor{
					Description: "",
				},
			},
			expectedPromotionTypes: []string{"price"},
		},
		{
			testName: "Promo price equals normal price",
			productPrices: map[string]*pricing.Product{
				skuName: {
					Price: &pricing.Price{
						NormalPrice: float64(100),
						PromoPrice:  float64(100),
					},
					Promotions: []*pricing.Promotion{},
				},
			},
			productDetail: productDetail,
			productDetailResponse: &rpc.ProductDetailResponse{
				Brand:    &rpc.Brand{},
				Variants: []*rpc.Variant{{}},
				Vendor: &rpc.Vendor{
					Description: "",
				},
			},
			expectedPromotionTypes: []string{},
		},
		{
			testName: "bundle",
			productPrices: map[string]*pricing.Product{
				skuName: {
					Price: &pricing.Price{
						NormalPrice:      float64(100),
						PromoPrice:       float64(90),
						StoreNormalPrice: float64(85),
						StorePromoPrice:  float64(80),
						PriceSource:      "BUNDLE_HA",
					},
					InStock: true,
					Promotions: []*pricing.Promotion{
						{
							Rules: map[string]string{
								"moq": "5",
							},
						},
						{
							Rules: nil,
						},
						{
							Rules: map[string]string{
								"moq": "xx",
							},
						},
					},
				},
			},
			productDetail: productDetail,
			productDetailResponse: &rpc.ProductDetailResponse{
				Brand:    &rpc.Brand{},
				Variants: []*rpc.Variant{{}},
				Vendor: &rpc.Vendor{
					Description: "",
				},
			},
			expectedPromotionTypes: []string{"bundle"},
		},
		{
			testName: "bundle combined with get y action",
			productPrices: map[string]*pricing.Product{
				skuName: {
					Price: &pricing.Price{
						NormalPrice:      float64(100),
						PromoPrice:       float64(90),
						StoreNormalPrice: float64(85),
						StorePromoPrice:  float64(80),
						PriceSource:      "BUNDLE_HA",
					},
					InStock: true,
					Promotions: []*pricing.Promotion{
						{
							Rules: map[string]string{
								"moq": "5",
							},
						},
						{
							Rules: nil,
						},
						{
							Rules: map[string]string{
								"moq": "xx",
							},
						},
					},
				},
			},
			productDetail: productDetail,
			productDetailResponse: &rpc.ProductDetailResponse{
				PromotionTypes: []string{"get_y_action"},
				Brand:          &rpc.Brand{},
				Variants:       []*rpc.Variant{{}},
				Vendor: &rpc.Vendor{
					Description: "",
				},
			},
			expectedPromotionTypes: []string{"get_y_action", "bundle"},
		},
	}

	for _, tc := range tableTest {
		t.Run(tc.testName, func(t *testing.T) {
			productResponse := tc.productDetailResponse

			returnedFn := WithAlohaPricing(tc.productPrices, countryCode, currencyCode, "en", isMPQEligible)
			returnedFn(tc.productDetail, productResponse)

			assert.Equal("aloha", productResponse.PricingEngine)
			assert.Equal("100", productResponse.NormalPrice)
			assert.Equal(tc.expectedPromotionTypes, productResponse.PromotionTypes)

			selectedPrice := tc.productPrices[skuName]
			if selectedPrice.Price.PromoPrice < selectedPrice.Price.NormalPrice {

				assert.Equal("Rp100", productResponse.DisplayNormalPrice)
				assert.Equal("90", productResponse.Price)
				assert.Equal("Rp90", productResponse.DisplayPrice)
				assert.Equal("85", productResponse.StoreNormalPrice)
				assert.Equal("Rp85", productResponse.DisplayStoreNormalPrice)
				assert.Equal("80", productResponse.StorePromoPrice)
				assert.Equal("Rp80", productResponse.DisplayStorePromoPrice)
				assert.Equal("10%", productResponse.DisplayPromoPricePercentage)
				assert.Equal("100", productResponse.UnitPrice)
				assert.Equal("Rp100 / 100 ml", productResponse.DisplayUnitPrice)
				assert.Equal("100", productResponse.SupermarketUnitCostPrice)
				assert.Equal("Rp100 / 100 ml", productResponse.DisplaySupermarketUnitCostPrice)

				assert.True(productResponse.Variants[0].InStock)
				assert.Equal(int64(5), productResponse.MaxPromoQuantity)
				assert.Equal(int64(5), productResponse.MaxOrderQuantity)

				assert.Equal(float64(2), productResponse.NaturalAverageWeight)
				assert.Equal("~ 2 100 ml", productResponse.DisplayAverageWeight)
			} else {
				assert.Equal("", productResponse.DisplayNormalPrice)
				assert.Equal("", productResponse.DisplayPromoPricePercentage)
			}

		})
	}

}

func TestWithCurrencyCodeForPricing(t *testing.T) {
	assert := assert.New(t)
	productDetail := &cat.ProductDetail{
		StockItem: &cat.StockItem{
			Price: &cat.Price{
				Cost:                 float64(100000),
				OriginalPromoCost:    float64(90000),
				SupermarketUnitPrice: float64(85000),
				ClientPrice:          float64(100000),
				Normal:               float64(110000),
				UnitPrice:            float64(90000),
			},
		},
		Properties: &cat.Properties{
			AverageWeight: float64(10),
		},
	}
	productResponse := &rpc.ProductDetailResponse{}

	returnedFn := WithCurrencyCodeForPricing("IDR", "en")
	returnedFn(productDetail, productResponse)

	assert.Equal("100000", productResponse.StoreNormalPrice)
	assert.Equal("Rp100,000", productResponse.DisplayStoreNormalPrice)
	assert.Equal("90000", productResponse.StorePromoPrice)
	assert.Equal("Rp90,000", productResponse.DisplayStorePromoPrice)
	assert.Equal("85000", productResponse.SupermarketUnitCostPrice)
	assert.Equal("Rp85,000 / each", productResponse.DisplaySupermarketUnitCostPrice)
	assert.Equal("100000", productResponse.Price)
	assert.Equal("Rp100,000", productResponse.DisplayPrice)
	assert.Equal("110000", productResponse.NormalPrice)
	assert.Equal("Rp110,000", productResponse.DisplayNormalPrice)
	assert.Equal("90000", productResponse.UnitPrice)
	assert.Equal("Rp90,000 / each", productResponse.DisplayUnitPrice)
	assert.Equal("9%", productResponse.DisplayPromoPricePercentage)

	assert.Equal(float64(10), productResponse.NaturalAverageWeight)
	assert.Equal("~ 10 ", productResponse.DisplayAverageWeight)

}

func TestWithMOQAndMPQ(t *testing.T) {
	for _, tc := range []struct {
		testName        string
		eligible        bool
		productDetail   *cat.ProductDetail
		productResponse *rpc.ProductDetailResponse
	}{
		{
			testName: "MPQ Eligible",
			eligible: true,
			productDetail: &cat.ProductDetail{
				StockItem: &cat.StockItem{
					MaxPromoQuantity: 10,
					MaxOrderQuantity: 0,
				},
				Properties: &cat.Properties{
					MaxOrderQuantity: 20,
				},
			},
			productResponse: &rpc.ProductDetailResponse{},
		},
		{
			testName: "MPQ Eligible but 0 MOQ",
			eligible: true,
			productDetail: &cat.ProductDetail{
				StockItem: &cat.StockItem{
					MaxPromoQuantity: 10,
					MaxOrderQuantity: 0,
				},
				Properties: &cat.Properties{
					MaxOrderQuantity: 0,
				},
			},
			productResponse: &rpc.ProductDetailResponse{},
		},
		{
			testName: "MPQ not Eligible",
			eligible: false,
			productDetail: &cat.ProductDetail{
				StockItem: &cat.StockItem{
					MaxPromoQuantity: 10,
					MaxOrderQuantity: 0,
				},
				Properties: &cat.Properties{
					MaxOrderQuantity: 20,
				},
			},
			productResponse: &rpc.ProductDetailResponse{},
		},
	} {
		t.Run(tc.testName, func(t *testing.T) {
			returnedFn := WithMOQAndMPQ(tc.eligible)
			returnedFn(tc.productDetail, tc.productResponse)

			if tc.eligible {

				if tc.productDetail.Properties.MaxOrderQuantity == 0 {
					assert.Equal(t, int64(50), tc.productResponse.MaxOrderQuantity)
				} else {
					assert.Equal(t, int64(20), tc.productResponse.MaxOrderQuantity)
				}
			} else {
				assert.Equal(t, int64(10), tc.productResponse.MaxOrderQuantity)
			}
		})
	}
}

func TestWithPromotionLabel(t *testing.T) {
	assert := assert.New(t)
	promotions := map[string]*promotion.Promotion{
		"BBB": {
			PromotionID:                           int64(11),
			DisplayPromotionActionCombinationText: "display promotion action combination text",
			DisplayBannerText:                     []string{"display banner text"},
			GetYQuantity:                          int64(89),
			DisplayPromotionActionLongText:        "DisplayPromotionActionLongText",
			DisplayPromotionActionShortText:       "DisplayPromotionActionShortText",
		},
	}

	productDetail := &cat.ProductDetail{
		ProductId: int64(99),
	}
	productResponse := &rpc.ProductDetailResponse{
		Variants: []*rpc.Variant{{}},
	}

	returnedFn := WithPromotionLabel(promotions)
	returnedFn(productDetail, productResponse)

	// No variants
	assert.Empty(productResponse.DisplayPromotionActionsCombinationText)

	// No SKU matched
	productDetail.Variants = append(productDetail.Variants, &cat.Variant{
		Sku: "AAA",
	})
	// returnedFn = WithPromotionLabel(promotions)
	returnedFn(productDetail, productResponse)
	assert.Empty(productResponse.DisplayPromotionActionsCombinationText)

	// SKU matched
	productDetail.Variants[0] = &cat.Variant{
		Sku: "BBB",
	}
	promotions["BBB"].PromotionActionType = ""
	returnedFn(productDetail, productResponse)
	assert.Equal("display promotion action combination text", productResponse.DisplayPromotionActionsCombinationText)

	// if PromotionActionType == "Spree::Promotion::Actions::GetYAction"
	promotions["BBB"].PromotionActionType = "Spree::Promotion::Actions::GetYAction"
	returnedFn(productDetail, productResponse)

	assert.Equal("display banner text", productResponse.DisplayBannerText[0])

	assert.NotEmpty(productResponse.PromotionActions)

	promotionAction := productResponse.PromotionActions[0]
	promotion := productResponse.Promotions[0]

	actionProductsExpected := []*rpc.PromotionProduct{
		{
			Id:                productDetail.GetProductId(),
			Quantity:          promotions["BBB"].GetYQuantity,
			DisplayBannerText: promotions["BBB"].DisplayBannerText,
			Variants:          productResponse.Variants,
		},
	}

	assert.Equal("Spree::Promotion::Actions::GetYAction", promotionAction.Type)
	assert.Equal("DisplayPromotionActionLongText", promotionAction.DisplayLongText)
	assert.Equal("DisplayPromotionActionShortText", promotionAction.DisplayShortText)
	assert.Equal(actionProductsExpected, promotionAction.Products)
	assert.Equal("Spree::Promotion::Rules::BuyXSingleRule", promotion.Rules[0].Type)
	assert.Equal(int64(11), promotion.Id)

	// if PromotionActionType != "Spree::Promotion::Actions::GetYAction"
	productResponse.PromotionActions[0] = &rpc.PromotionAction{} // Reset
	promotions["BBB"].PromotionActionType = "blabla"
	returnedFn(productDetail, productResponse)

	assert.Empty(productResponse.DisplayBannerText)

	assert.Empty(productResponse.PromotionActions[0].Products[0].Quantity)
}

func TestWithPromotionLabelForPromotionTypes(t *testing.T) {
	assert := assert.New(t)

	type TableTest struct {
		testName               string
		promotions             map[string]*promotion.Promotion
		productDetail          *cat.ProductDetail
		productDetailResponse  *rpc.ProductDetailResponse
		expectedActionProducts []*rpc.PromotionProduct
		expectedPromotionTypes []string
	}

	tableTest := []*TableTest{
		{
			testName: "buy x get y",
			promotions: map[string]*promotion.Promotion{
				"BBB": {
					PromotionID:                           int64(11),
					DisplayPromotionActionCombinationText: "display promotion action combination text",
					DisplayBannerText:                     []string{"display banner text"},
					GetYQuantity:                          int64(89),
					DisplayPromotionActionLongText:        "DisplayPromotionActionLongText",
					DisplayPromotionActionShortText:       "DisplayPromotionActionShortText",
					PromotionActionType:                   "Spree::Promotion::Actions::GetYAction",
				},
			},
			productDetail: &cat.ProductDetail{
				ProductId: int64(99),
				Variants:  []*cat.Variant{{Sku: "BBB"}},
			},
			productDetailResponse: &rpc.ProductDetailResponse{
				Variants: []*rpc.Variant{{}},
			},
			expectedActionProducts: []*rpc.PromotionProduct{
				{
					Id:                int64(99),
					Quantity:          int64(89),
					DisplayBannerText: []string{"display banner text"},
					Variants:          []*rpc.Variant{{Sku: "BBB"}},
				},
			},
			expectedPromotionTypes: []string{"get_y_action"},
		},
		{
			testName: "buy x get discount",
			promotions: map[string]*promotion.Promotion{
				"BBB": {
					PromotionID:                           int64(11),
					DisplayPromotionActionCombinationText: "display promotion action combination text",
					DisplayBannerText:                     []string{"display banner text"},
					BuyXQuantity:                          int64(1),
					GetPPercent:                           0.2,
					DisplayPromotionActionLongText:        "DisplayPromotionActionLongText",
					DisplayPromotionActionShortText:       "DisplayPromotionActionShortText",
					PromotionActionType:                   "Spree::Promotion::Actions::GetDiscountAction",
				},
			},
			productDetail: &cat.ProductDetail{
				ProductId: int64(99),
				Variants:  []*cat.Variant{{Sku: "BBB"}},
			},
			productDetailResponse: &rpc.ProductDetailResponse{
				Variants: []*rpc.Variant{{}},
			},
			expectedActionProducts: []*rpc.PromotionProduct{
				{
					Id:                int64(99),
					Quantity:          int64(1),
					Discount:          0.2,
					DisplayBannerText: []string{"display banner text"},
					Variants:          []*rpc.Variant{{Sku: "BBB"}},
				},
			},
			expectedPromotionTypes: []string{"get_discount_action"},
		},
	}

	for _, tc := range tableTest {
		t.Run(tc.testName, func(t *testing.T) {
			returnedFn := WithPromotionLabel(tc.promotions)
			returnedFn(tc.productDetail, tc.productDetailResponse)

			assert.Equal(tc.expectedPromotionTypes, tc.productDetailResponse.PromotionTypes)
		})
	}
}

func TestWithTranslatedIDs(t *testing.T) {
	assert := assert.New(t)
	translatedID := &hubbleRPC.TranslatedID{
		SpreeProductId:    int64(222),
		SpreeVariantId:    int64(333),
		SpreeBrandId:      int64(444),
		DeliveredQuantity: int64(555),
	}
	isSynthetic := true
	productDetail := &cat.ProductDetail{
		ProductId: int64(12),
		Variants: []*cat.Variant{
			{},
		},
		Brand: &cat.Brand{
			BrandId: int64(123),
		},
	}
	productResponse := &rpc.ProductDetailResponse{
		Variants: []*rpc.Variant{
			{},
		},
		Brand: &rpc.Brand{},
	}

	WithTranslatedIDs(nil, isSynthetic)(productDetail, productResponse)

	assert.Empty(productResponse.ProductGlobalId)

	WithTranslatedIDs(translatedID, isSynthetic)(productDetail, productResponse)

	assert.Equal(int64(12), productResponse.ProductGlobalId)
	assert.Equal(int64(123), productResponse.BrandGlobalId)
	assert.Equal(int64(222), productResponse.Id)
	assert.Equal(int64(333), productResponse.Variants[0].Id)
	assert.Equal(int64(444), productResponse.Brand.Id)
	assert.Equal(int64(555), productResponse.DeliveredQuantity)

}

func TestWithStorePriceIncluded(t *testing.T) {
	assert := assert.New(t)
	productDetail := &cat.ProductDetail{}
	productResponse := &rpc.ProductDetailResponse{
		StoreNormalPrice: "100",
	}

	WithStorePriceIncluded(true)(productDetail, productResponse)
	assert.Equal("100", productResponse.StoreNormalPrice)

	WithStorePriceIncluded(false)(productDetail, productResponse)
	assert.Empty(productResponse.StoreNormalPrice)

}

func TestWithProductMOQ(t *testing.T) {
	assert := assert.New(t)

	maxMOQ := int64(10)
	maxMOQHC := int64(15)
	productDetail := &cat.ProductDetail{
		Variants: []*cat.Variant{
			{Sku: "ABC"},
		},
	}
	productResponse := &rpc.ProductDetailResponse{}

	pricesHighStock := map[string]*pricing.Product{
		"ABC": {
			Price: &pricing.Price{
				NormalPrice:      float64(100),
				PromoPrice:       float64(90),
				StoreNormalPrice: float64(85),
				StorePromoPrice:  float64(80),
				Quantity:         100,
			},
			InStock: true,
		},
	}

	// TODO: Re-enable MoQ in HFS once stock from pricing is consistent again
	// pricesLowStock := map[string]*pricing.Product{
	// 	"ABC": {
	// 		Price: &pricing.Price{
	// 			NormalPrice:      float64(100),
	// 			PromoPrice:       float64(90),
	// 			StoreNormalPrice: float64(85),
	// 			StorePromoPrice:  float64(80),
	// 			Quantity:         7,
	// 		},
	// 		InStock: true,
	// 	},
	// }

	WithProductMOQ(pricesHighStock, "type1", maxMOQ, maxMOQHC, "ID")(productDetail, productResponse)
	assert.Equal(int64(10), productResponse.MaxOrderQuantity)

	WithProductMOQ(pricesHighStock, "hc", maxMOQ, maxMOQHC, "ID")(productDetail, productResponse)
	assert.Equal(int64(15), productResponse.MaxOrderQuantity)

	// TODO: Re-enable MoQ in HFS once stock from pricing is consistent again
	// WithProductMOQ(pricesLowStock, "type1", maxMOQ, maxMOQHC, "ID")(productDetail, productResponse)
	// assert.Equal(int64(7), productResponse.MaxOrderQuantity)

	// WithProductMOQ(pricesLowStock, "hc", maxMOQ, maxMOQHC, "ID")(productDetail, productResponse)
	// assert.Equal(int64(15), productResponse.MaxOrderQuantity)

}

func TestWithVariants(t *testing.T) {
	assert := assert.New(t)
	productDetail := &cat.ProductDetail{
		Variants: []*cat.Variant{
			{
				VariantId: int64(20),
				Images: &cat.Images{
					Images: []*cat.Image{
						{
							Id: int64(111),
						},
					},
				},
			},
		},
	}
	productResponse := &rpc.ProductDetailResponse{}

	withVariants()(productDetail, productResponse)
	assert.Equal(int64(20), productResponse.VariantId)
	assert.Equal(int64(111), productResponse.Variants[0].Images[0].Id)

}

func TestSupermarketUnit(t *testing.T) {
	assert := assert.New(t)

	responses := map[string][]string{
		"each":   {"bundle", "Random number"},
		"100 ml": {"l", "ml"},
		"mg":     {"mg"},
		"100 g":  {"kg", "g"},
	}

	for key, values := range responses {
		for _, value := range values {
			assert.Equal(supermarketUnit(value), key)
		}
	}

	assert.NotEqualValues(supermarketUnit("randomized"), []string{"100 ml", "mg", "100 g"})
	assert.Equal(supermarketUnit("this sentence contains l"), "100 ml")
	assert.Equal(supermarketUnit("this sentence contains ml"), "100 ml")

}
