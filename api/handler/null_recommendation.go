package handler

import (
	"context"
	"strconv"
	"sync"

	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	cat "happyfresh.io/catalog/lib/rpc/api"
	hub "happyfresh.io/hubble/lib/rpc"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/api/response"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/pricing"
	"happyfresh.io/search/core/pricing/aloha"
	"happyfresh.io/search/core/promotion"
	"happyfresh.io/search/core/stemmer"
	"happyfresh.io/search/core/synonym"
	"happyfresh.io/search/lib/grpc"
	"happyfresh.io/search/lib/strs"
)

func (s *SearchService) NullRecommendation(ctx context.Context, in *rpc.SearchRequest) (*rpc.SearchResponse, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Failed to parse metadata"))
	}

	normalizedQuery := in.NormalizedQuery()
	stockLocationIDs := in.StockLocationIDs()
	// channel := in.Channel(md)
	locale := in.Locale(md)
	clientVersion := in.ClientVersion(md)
	userType := in.UserType(md)
	isSynthetic := in.IsSynthetic(md)
	isSND := in.IsSND(md)
	searchVariant := in.SearchVariant(md)
	userID := in.UserID(md)
	_, nonSellableItems := in.Sellables(md)
	nearbyStores := in.NearbyStoreList(stockLocationIDs...)
	nullRecommendationVariant := in.NullRecommendationVariant(md)
	if nullRecommendationVariant == "a" {
		return s.bestDealsForNullrecommendation(ctx, in)
	}

	// If no nearbyStores is provided
	// then call Best Deal
	// no matter what the nullRecommendationVariant is
	if len(nearbyStores) <= 0 {
		return s.bestDealsForNullrecommendation(ctx, in)
	}

	// To be used only for snd features that are related to searching, fetching price, and mpq
	orderChannel := in.OrderChannel(md)

	i, err := s.elasticsearchClient.Index(in.CountryCode)
	if err != nil {
		return nil, grpc.NewStatusError(codes.InvalidArgument, err)
	}

	country, err := s.hubbleClient.GetCountryByIso(ctx, in.CountryCode)
	if err != nil {
		return nil, grpc.NewStatusError(codes.InvalidArgument, err)
	}

	maxMOQ := country.GetPreferencesNormalMoq()
	if maxMOQ < 0 {
		maxMOQ = s.defaultMaxMOQ
	}

	maxMOQHC := country.GetPreferencesHcMoq()
	if maxMOQHC < 0 {
		maxMOQHC = s.defaultMaxMOQHC
	}

	filters := &rpc.FilterLock{}

	swg := &sync.WaitGroup{}
	swg.Add(3)

	var esSynonymTerms []string
	go func() {
		defer swg.Done()

		if !s.enableESSynonym || isSND || normalizedQuery == "" {
			return
		}

		esSynonym, err := s.esSynonymAdapters.Get(in.CountryCode)
		if err != nil {
			log.ForContext(ctx, "null recommendation", "ES-synonym").Warn(err)
			return
		}
		response, err := esSynonym.Get(ctx, normalizedQuery)
		if err != nil {
			log.ForContext(ctx, "null recommendation", "ES-synonym").Warn(err)
			return
		}

		esSynonymTerms = response.Terms
	}()

	var translatedQuery string
	go func() {
		defer swg.Done()

		tq, err := s.translator.Translate(ctx, in.CountryCode, "en", normalizedQuery)
		if err != nil {
			log.Warn(err)
		}

		translatedQuery = tq
	}()

	var stemmedQueries []string
	go func() {
		defer swg.Done()

		stemmed := stemmer.StemQuery(normalizedQuery)
		if err == nil {
			log.ForContext(ctx, "null recommendation", "StemQuery").Warn(err)
		}

		stemmedQueries = stemmed
	}()

	var terms []string
	if !s.disableOrion && !isSND && normalizedQuery != "" {
		sCtx := synonym.NewContext(ctx, "country_iso", in.CountryCode)

		uID := ""
		if userID != -1 {
			uID = strconv.FormatInt(userID, 10)
		}
		sCtx = synonym.NewContext(sCtx, "user_id", uID)

		sCtx = synonym.NewContext(sCtx, "synonym_variant", searchVariant)

		syno, err := synonym.Get(sCtx, normalizedQuery)
		if err != nil {
			log.ForContext(ctx, "null recommendation", "get synonym").Error(err)
			terms = []string{normalizedQuery}
		} else {
			terms = syno.Terms
			if len(terms) == 0 {
				terms = []string{normalizedQuery}
			}
		}
	} else {
		terms = []string{normalizedQuery}
	}

	swg.Wait()

	terms = append(terms, esSynonymTerms...)
	if translatedQuery != "" && translatedQuery != normalizedQuery {
		terms = append(terms, translatedQuery)
	}

	searchOptions := []index.SearchOption{
		index.WithTermQueries(terms...),
		index.WithTermQueries(stemmedQueries...),
		index.WithFuzzyQueries(normalizedQuery),
		index.WithLocale(locale),
		index.WithTermFilterInt64("stock_location_id", nearbyStores...),
		index.WithCountry(country.IsoName),
		index.WithPagination(50, 1),
	}

	indexSearchResponse, err := i.Search(ctx, searchOptions...)
	if err != nil {
		e, ok := err.(*index.Error)
		if ok {
			return nil, grpc.NewStatusError(grpc.StatusFromHTTP(e.Code), e.Unwrap())
		}

		return nil, grpc.ErrorFrom(ctx, err)
	}

	searchResultLen := len(indexSearchResponse.StockItems)
	if searchResultLen <= 0 {
		return s.bestDealsForNullrecommendation(ctx, in)
	}

	// Null Recommendation
	maxResultLen := 10
	productIdScores := make(map[int64]float64, 0)
	for _, si := range indexSearchResponse.StockItems {
		if len(productIdScores) >= maxResultLen {
			break
		}
		if _, ok := productIdScores[si.ProductID]; !ok {
			productIdScores[si.ProductID] = si.Score
		}
	}

	nullRecommendationInput := make(map[int64]float64)
	for k, v := range productIdScores {
		replacement, err := s.replacementService.GetReplacement(ctx, stockLocationIDs[0], k)
		if err != nil {
			log.For("null recommendation", "GetReplacement").Error(err)
		}

		for r := range replacement {
			nullRecommendationInput[r] = v
			break
		}
	}

	// If PSN API doesn't give product replacement
	// then call best deal
	if len(nullRecommendationInput) <= 0 {
		return s.bestDealsForNullrecommendation(ctx, in)
	}

	n, err := s.nullRecommendationService.GetRecommendationProducts(ctx, nullRecommendationInput)
	if err != nil {
		return nil, err
	}

	productIDsResult := make([]int64, len(n))
	variantIDsResult := make([]int64, len(n))
	cnt := 0
	for k, v := range n {
		productIDsResult[cnt] = k
		variantIDsResult[cnt] = v
		cnt += 1
	}

	eg, egCtx := errgroup.WithContext(ctx)

	var productProperties []*cat.StoreProducts
	eg.Go(func() error {
		r, err := s.catalogClient.GetStoreProductsPLP(egCtx, variantIDsResult, stockLocationIDs[0], locale)
		if err != nil {
			return grpc.ErrorFrom(egCtx, err)
		}

		productProperties = r
		return nil
	})

	var translatedIDs []*hub.TranslatedID
	eg.Go(func() error {
		r, err := s.idConverter.PLPIDs(egCtx, productIDsResult)
		if err != nil {
			return grpc.ErrorFrom(egCtx, err)
		}

		translatedIDs = r
		return nil
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	productsPrices, err := pricing.Get(
		ctx,
		pricing.WithProducts(productProperties),
		pricing.WithChannel(orderChannel),
		pricing.WithUserType(userType),
		pricing.WithCountryCode(in.CountryCode),
	)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	taxonNoneFilterMap := map[int64]struct{}{}
	catalogRestrictedTaxons, err := s.catalogClient.GetRestrictedTaxons(ctx, 0, stockLocationIDs[0], orderChannel, isSND)
	if err != nil {
		log.ForContext(ctx, "null recommendation", "GetRestrictedTaxons").Warn(err)
		taxonNoneFilterMap = s.taxonNoneFilterMap
	} else {
		for _, id := range catalogRestrictedTaxons.RestrictedTaxonIds {
			taxonNoneFilterMap[id] = struct{}{}
		}
	}

	availableProducts, meta := func() ([]*cat.StoreProducts, *index.Meta) {
		ap := []*cat.StoreProducts{}
		nonSellableMap := map[string]struct{}{}
		for _, n := range nonSellableItems {
			nonSellableMap[n] = struct{}{}
		}

		for _, p := range productProperties {
			func() {
				if p.ProductDetail == nil {
					return
				}

				taxonIDs := p.ProductDetail.TaxonIds
				for _, taxonID := range taxonIDs {
					_, ok := taxonNoneFilterMap[taxonID]
					if ok {
						return
					}
				}

				productCode := strs.SKU(p.ProductDetail.Variants[0].Sku).ToProductCode(in.CountryCode)
				price, priceOk := productsPrices[productCode]
				_, nonSellableOk := nonSellableMap[p.ProductDetail.SellableItems]

				if ((priceOk && price.InStock) || in.ShowOos) && !nonSellableOk {
					ap = append(ap, p)
				}
			}()
		}

		count := int64(len(ap))
		return ap, &index.Meta{
			Page:       1,
			PageSize:   count,
			TotalPages: 1,
			Count:      count,
			TotalCount: count,
		}
	}()

	if meta.TotalCount == 0 {
		return s.bestDealsForNullrecommendation(ctx, in)
	}

	wg := &sync.WaitGroup{}
	wg.Add(2)

	productsPromotions := map[string]*promotion.Promotion{}
	go func() {
		defer wg.Done()

		pTemp, err := promotion.Get(ctx, availableProducts, locale)
		if err != nil {
			log.ForContext(ctx, "null return", "get promotion").Error(err)
		} else {
			productsPromotions = pTemp
		}
	}()

	var isMPQEligible bool
	go func() {
		defer wg.Done()

		isMPQEligible = aloha.IsMpqEligible(ctx, orderChannel, clientVersion)
	}()

	wg.Wait()

	// disabled until error fixed
	// go func() {
	// 	if s.disableSync || len(stockLocationIDs) <= 0 || isSynthetic || isSND {
	// 		return
	// 	}

	// 	err := pricingSync.Sync(ctx, indexSearchResponse, availableProducts, productsPrices, channel, in.CountryCode)
	// 	if err != nil {
	// 		log.ForContext(ctx, "null recommendation", "sync pricing").Error(err)
	// 	}
	// }()

	out, err := response.Map(
		ctx,
		availableProducts,
		response.RootOptionChain(
			response.WithMetaAndPageLimiter(meta, s.pageLimiter, s.version),
			response.WithNormalizedQuery(normalizedQuery),
			response.WithTaxonIDsFilter(filters.GetTaxons()),
			response.WithBrandsFilter(filters.GetBrands()),
			response.WithPromotionTypesFilter(filters.GetPromotionTypes()),
			response.WithRecommendationType("new"),
		),
		response.ProductOptionChain(
			response.WithNamingAndDescription(locale),
			response.WithBrandNaming(locale),
			response.WithCategories(locale),
			response.WithVendor(locale),
			response.WithAlohaPricing(productsPrices, country.GetCurrency(), in.CountryCode, locale, isMPQEligible, in.DisableLimitedStockQuantity),
			response.WithPromotionLabels(productsPromotions),
			response.WithStorePriceIncluded(isSND),
			response.WithTranslatedIDs(translatedIDs),
			response.WithGlobalIDs(isSynthetic),
			response.WithProductMOQ(productsPrices, userType, maxMOQ, maxMOQHC, in.CountryCode, in.DisableLimitedStockQuantity),
		),
	)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	return out, nil
}

func (s *SearchService) bestDealsForNullrecommendation(ctx context.Context, in *rpc.SearchRequest) (*rpc.SearchResponse, error) {
	inBestDeal := &rpc.BestDealsRequest{
		StoreIds:         in.StoreIds,
		StockLocationIds: in.StockLocationIds,
		CountryCode:      in.CountryCode,
		ShowOos:          in.ShowOos,
	}

	out, err := s.BestDeals(ctx, inBestDeal)
	if err != nil {
		return nil, err
	}
	out.Meta.RecommendationType = "best_deal"
	return out, nil
}
