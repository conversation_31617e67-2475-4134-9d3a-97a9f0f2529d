package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	cat "happyfresh.io/catalog/lib/rpc/api"
	hub "happyfresh.io/hubble/lib/rpc"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	orion "happyfresh.io/orion/lib/rpc"
	orionRPCLib "happyfresh.io/orion/lib/rpc/client"
	"happyfresh.io/search/api/response"
	"happyfresh.io/search/api/rpc"
	"happyfresh.io/search/core/catalog"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/core/hubble"
	"happyfresh.io/search/core/idconv"
	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/nullrecommendation"
	"happyfresh.io/search/core/replacement"
	"happyfresh.io/search/core/session"
	"happyfresh.io/search/core/sprinkles"
	"happyfresh.io/search/core/stemmer"
	"happyfresh.io/search/core/translation"

	"happyfresh.io/search/core/pricing"
	"happyfresh.io/search/core/pricing/aloha"
	pricingSync "happyfresh.io/search/core/pricing/sync"
	promotion "happyfresh.io/search/core/promotion"
	recommendation "happyfresh.io/search/core/recommendation"
	"happyfresh.io/search/core/synonym"
	"happyfresh.io/search/lib/grpc"
	"happyfresh.io/search/lib/strs"
)

//go:generate protoc -I${PWD}/../rpc -I/usr/local/include/protobuf-1.3.1 --gofast_out=plugins=grpc:${PWD}/../rpc/. search.proto

type SearchService struct {
	elasticsearchClient        index.Client
	pqClient                   index.Client
	suggesterShards            index.SuggesterShards
	esSynonymAdapters          synonym.Adapters
	catalogClient              catalog.Client
	orionClient                *orionRPCLib.Orion
	hubbleClient               *hubble.Client
	idConverter                idconv.IDConv
	translator                 translation.Translator
	disableSync                bool
	disableSearchSuggestion    bool
	disableOrion               bool
	pageLimiter                int64
	taxonFilterMinimumScore    float64
	bestDealsProductLimit      int64
	taxonNoneFilter            []int64
	taxonNoneFilterMap         map[int64]struct{}
	promotionTypeFilterChannel []string
	rawPopularityBoostScore    float64
	maxGlobalSLI               int64
	maxGlobalProduct           int64
	defaultMaxMOQ              int64
	defaultMaxMOQHC            int64
	enableESSynonym            bool
	enableGlobalPriceMap       map[string]bool
	version                    string
	trendingBoostWeight        float64
	trendingBoostActivePeriod  int64
	replacementService         replacement.ReplacementService
	nullRecommendationService  nullrecommendation.NullRecommendationService
}

func (s *SearchService) Search(ctx context.Context, in *rpc.SearchRequest) (*rpc.SearchResponse, error) {
	promotedSKUs, maxPromotedProductCount := s.getPromotedSKUsAndMaxProductCount(ctx, in.NormalizedQuery())
	in.PromotedSkus = promotedSKUs
	in.MaxPromotedProductCount = maxPromotedProductCount
	return s.search(ctx, in, s.elasticsearchClient)
}

func (s *SearchService) Browse(ctx context.Context, in *rpc.SearchRequest) (*rpc.SearchResponse, error) {
	return s.search(ctx, in, s.elasticsearchClient)
}

func (s *SearchService) SearchReplacement(ctx context.Context, in *rpc.SearchRequest) (*rpc.SearchResponse, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Failed to parse metadata"))
	}

	normalizedQuery := in.NormalizedQuery()
	stockItemIDs := in.StockItemIDs()
	variantIDs := in.VariantIDs()
	SKUs := in.SKUs()
	taxonIDs := in.TaxonIDs()
	productTypeIDs := in.ProductTypeIDs()
	brandIDs := in.BrandIDs()
	storeIDs := in.StoreIDs()
	stockLocationIDs := in.StockLocationIDs()
	inStock := in.InStock(md)
	sorting := in.SortBy(md)
	size, page := in.Pagination()
	channel := in.Channel(md)
	locale := in.Locale(md)
	clientVersion := in.ClientVersion(md)
	userType := in.UserType(md)
	isSND := in.IsSND(md)
	isSynthetic := in.IsSynthetic(md)
	userID := in.UserID(md)
	searchVariant := in.SearchVariant(md)
	_, nonSellableItems := in.Sellables(md)

	i, err := s.elasticsearchClient.Index(in.CountryCode)
	if err != nil {
		return nil, grpc.NewStatusError(codes.InvalidArgument, err)
	}

	country, err := s.hubbleClient.GetCountryByIso(ctx, in.CountryCode)
	if err != nil {
		return nil, grpc.NewStatusError(codes.InvalidArgument, err)
	}

	maxMOQ := country.GetPreferencesNormalMoq()
	if maxMOQ < 0 {
		maxMOQ = s.defaultMaxMOQ
	}

	maxMOQHC := country.GetPreferencesHcMoq()
	if maxMOQHC < 0 {
		maxMOQHC = s.defaultMaxMOQHC
	}

	swg := &sync.WaitGroup{}
	swg.Add(4)

	var esSynonymTerms []string
	go func() {
		defer swg.Done()

		if !s.enableESSynonym || isSND || normalizedQuery == "" {
			return
		}

		esSynonym, err := s.esSynonymAdapters.Get(in.CountryCode)
		if err != nil {
			log.ForContext(ctx, "search", "ES-synonym").Warn(err)
			return
		}
		response, err := esSynonym.Get(ctx, normalizedQuery)
		if err != nil {
			log.ForContext(ctx, "search", "ES-synonym").Warn(err)
			return
		}

		esSynonymTerms = response.Terms
	}()

	var taxonNoneFilter []int64
	go func() {
		defer swg.Done()
		catalogRestrictedTaxons, err := s.catalogClient.GetRestrictedTaxons(ctx, 0, stockLocationIDs[0], channel, isSND)
		if err != nil {
			log.ForContext(ctx, "handler", "search").Warn(err)
			taxonNoneFilter = s.taxonNoneFilter
		} else {
			taxonNoneFilter = catalogRestrictedTaxons.RestrictedTaxonIds
		}
	}()

	go func() {
		defer swg.Done()

		taxonomyList, err := s.catalogClient.GetTaxonomy(ctx, 1, stockLocationIDs[0], locale, in.CountryCode)

		if err != nil {
			log.ForContext(ctx, "handler", "search").Warn(err)
			return
		}

		allowedTaxonIDs := s.catalogClient.GetTaxonIDs(taxonomyList.GetTaxons())
		if len(taxonIDs) == 0 {
			taxonIDs = allowedTaxonIDs
		} else {
			filteredTaxonIDs := []int64{}
			for _, taxonID := range taxonIDs {
				if containsInt64(allowedTaxonIDs, taxonID) {
					filteredTaxonIDs = append(filteredTaxonIDs, taxonID)
				}
			}
			taxonIDs = filteredTaxonIDs
		}
	}()

	var stemmedQueries []string
	go func() {
		defer swg.Done()

		stemmed := stemmer.StemQuery(normalizedQuery)
		stemmedQueries = stemmed
	}()

	var recommendedPtys map[string]float64
	var productBoosts map[string]float64
	var terms []string
	if !s.disableOrion && !isSND && normalizedQuery != "" {
		sCtx := synonym.NewContext(ctx, "country_iso", in.CountryCode)

		uID := ""
		if userID != -1 {
			uID = strconv.FormatInt(userID, 10)
		}
		sCtx = synonym.NewContext(sCtx, "user_id", uID)

		sCtx = synonym.NewContext(sCtx, "synonym_variant", searchVariant)

		syno, err := synonym.Get(sCtx, normalizedQuery)
		if err != nil {
			log.ForContext(ctx, "handler", "SearchReplacement").Error(err)
			terms = []string{normalizedQuery}
		} else {
			terms = syno.Terms
			recommendedPtys = syno.ProductTypes
			productBoosts = syno.ProductBoosts

			if len(terms) == 0 {
				terms = []string{normalizedQuery}
			}
		}
	} else {
		var err error
		terms = []string{normalizedQuery}
		recommendedPtys, err = recommendation.Get(ctx, normalizedQuery)
		if err != nil {
			log.ForContext(ctx, "handler", "SearchReplacement").Error(err)
		}
	}

	swg.Wait()
	terms = append(terms, esSynonymTerms...)

	searchOptions := []index.SearchOption{
		index.WithTermQueries(terms...),
		index.WithTermQueries(stemmedQueries...),
		index.WithFuzzyQueries(normalizedQuery),
		index.WithTermFilterInt64("store_id", storeIDs...),
		index.WithTermFilterInt64("taxon_ids", taxonIDs...),
		index.WithTermFilterString("product_type_ids", productTypeIDs...),
		index.WithTermFilterString("in_stock", inStock...),
		index.WithTermFilterInt64("variant_id", variantIDs...),
		index.WithTermFilterString("id", stockItemIDs...),
		index.WithTermFilterString("sku", SKUs...),
		index.WithNoneTermFilterString("sellable_items", nonSellableItems...),
		index.WithNoneTermFilterInt64("taxon_ids", taxonNoneFilter...),
		index.WithNoneTermFilterInt64("price_android", 0),
		index.WithLocale(locale),
		index.WithTermFilterInt64("brand_id", brandIDs...),
		index.WithSorting(sorting),
		index.WithPagination(int(size), int(page)),
		index.WithAnalyticTags("channel", channel),
		index.WithConversionBoost("product_type_ids", recommendedPtys),
		index.WithCountry(country.IsoName),
	}
	if in.CountryCode == "TH" {
		searchOptions = append(
			searchOptions,
			index.WithTermFilterInt64("stock_location_id", stockLocationIDs...),
		)
	}
	if userID != -1 {
		searchOptions = append(searchOptions, index.WithAnalyticTags("user_ids", userID), index.WithConversionBoost("sku", productBoosts))
	}
	if userType != "" {
		searchOptions = append(searchOptions, index.WithAnalyticTags("user_type", userType))
	}
	if normalizedQuery == "" {
		searchOptions = append(searchOptions, index.WithFunctionalBoosts("popularity"))
	}
	if normalizedQuery != "" && !isSND && (sorting == "_score desc" || sorting == "_score asc") {
		searchOptions = append(searchOptions, index.WithTrendingBoost(s.trendingBoostWeight, s.trendingBoostActivePeriod))

		if s.rawPopularityBoostScore != 0 {
			searchOptions = append(searchOptions, index.WithFunctionalBoostsScore(map[string]float64{"raw_popularity": s.rawPopularityBoostScore}))
		}
	}

	indexSearchResponse, err := i.Search(ctx, searchOptions...)
	if err != nil {
		e, ok := err.(*index.Error)
		if ok {
			return nil, grpc.NewStatusError(grpc.StatusFromHTTP(e.Code), e.Unwrap())
		}

		return nil, grpc.ErrorFrom(ctx, err)
	}

	stockItemIDsResult := make([]int64, len(indexSearchResponse.StockItems))
	productIDsResult := make([]int64, len(indexSearchResponse.StockItems))
	variantIDsResult := make([]int64, len(indexSearchResponse.StockItems))
	for i, s := range indexSearchResponse.StockItems {
		stockItemIDsResult[i] = s.ID
		productIDsResult[i] = s.ProductID
		variantIDsResult[i] = s.VariantID
	}

	eg, egCtx := errgroup.WithContext(ctx)

	var productProperties []*cat.StoreProducts
	eg.Go(func() error {
		r, err := s.catalogClient.GetStoreProductsPLP(egCtx, variantIDsResult, stockLocationIDs[0], locale)
		if err != nil {
			return grpc.ErrorFrom(egCtx, err)
		}

		productProperties = r
		return nil
	})

	var translatedIDs []*hub.TranslatedID
	eg.Go(func() error {
		r, err := s.idConverter.PLPIDs(egCtx, productIDsResult)
		if err != nil {
			return grpc.ErrorFrom(egCtx, err)
		}

		translatedIDs = r
		return nil
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	productsPrices, err := pricing.Get(
		ctx,
		pricing.WithProducts(productProperties),
		pricing.WithChannel(channel),
		pricing.WithUserType(userType),
		pricing.WithCountryCode(in.CountryCode),
	)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	wg := &sync.WaitGroup{}
	wg.Add(2)

	productsPromotions := map[string]*promotion.Promotion{}
	go func() {
		defer wg.Done()

		pTemp, err := promotion.Get(ctx, productProperties, locale)
		if err != nil {
			log.ForContext(ctx, "handler", "SearchReplacement").Error(err)
		} else {
			productsPromotions = pTemp
		}
	}()

	var isMPQEligible bool
	go func() {
		defer wg.Done()

		isMPQEligible = aloha.IsMpqEligible(ctx, channel, clientVersion)
	}()

	wg.Wait()

	go func() {
		if s.disableSync {
			return
		}

		err := pricingSync.Sync(ctx, indexSearchResponse, productProperties, productsPrices, channel, in.CountryCode)
		if err != nil {
			log.ForContext(ctx, "handler", "SearchReplacement").Error(err)
		}
	}()

	out, err := response.Map(
		ctx,
		productProperties,
		response.RootOptionChain(
			response.WithMetaAndPageLimiter(indexSearchResponse.Meta, s.pageLimiter, s.version),
			response.WithNormalizedQuery(normalizedQuery),
		),
		response.ProductOptionChain(
			response.WithNamingAndDescription(locale),
			response.WithBrandNaming(locale),
			response.WithCategories(locale),
			response.WithVendor(locale),
			response.WithAlohaPricing(productsPrices, country.Currency, in.CountryCode, locale, isMPQEligible, in.DisableLimitedStockQuantity),
			response.WithPromotionLabels(productsPromotions),
			response.WithStorePriceIncluded(isSND),
			response.WithTranslatedIDs(translatedIDs),
			response.WithGlobalIDs(isSynthetic),
			response.WithProductMOQ(productsPrices, userType, maxMOQ, maxMOQHC, in.CountryCode, in.DisableLimitedStockQuantity),
		),
	)
	if err != nil {
		return nil, grpc.NewStatusError(codes.Internal, err)
	}

	out.ExternalSearchId = ""
	out.SearchId = 0

	return out, nil
}

func (s *SearchService) BestDeals(ctx context.Context, in *rpc.BestDealsRequest) (*rpc.SearchResponse, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Failed to parse metadata"))
	}

	storeIDs := in.StoreIDs()
	channel := in.Channel(md)
	userType := in.UserType(md)

	if len(storeIDs) == 0 {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Empty store IDs"))
	}

	skus := []string{}
	taxonNoneFilter := []int64{}

	switch in.PromotionType {
	case "":
		wg := &sync.WaitGroup{}
		wg.Add(3)

		spreePromo := []*datastore.StockItemPromotion{}
		discountPromo := []string{}
		bundlePromo := []string{}

		go func(ctx context.Context, storeID int) {
			defer wg.Done()

			stockItems, err := datastore.StockItemPromotionByStoreAndTypes(ctx, storeID, "buy_x_get_y", "free_shipping")
			if err != nil {
				log.ForContext(ctx, "handler", "BestDeal").Errorf("Error: %+v", err)
				return
			}

			spreePromo = stockItems.StockItemPromotions
		}(ctx, int(storeIDs[0]))

		go func(ctx context.Context, storeID int, channel, userType string) {
			defer wg.Done()

			productCodes, err := aloha.GetTodaysPromotedProduct(ctx, storeID, channel, userType)
			if err != nil {
				log.ForContext(ctx, "handler", "BestDeal").Errorf("Error: %+v", err)
				return
			}

			discountPromo = productCodes
		}(ctx, int(storeIDs[0]), channel, userType)

		go func(ctx context.Context, storeID int, channel, userType string) {
			defer wg.Done()

			productCodes, err := aloha.GetTodaysPromotedBundleProduct(ctx, storeID, channel, in.CountryCode, userType)
			if err != nil {
				log.ForContext(ctx, "handler", "BestDeal").Errorf("Error: %+v", err)
				return
			}

			bundlePromo = productCodes
		}(ctx, int(storeIDs[0]), channel, userType)

		wg.Wait()

		for _, sp := range spreePromo {
			skus = append(skus, sp.Sku)
		}

		for _, dp := range discountPromo {
			skus = append(skus, strs.ProductCode(dp).ToSKU(in.CountryCode))
		}

		for _, bp := range bundlePromo {
			skus = append(skus, strs.ProductCode(bp).ToSKU(in.CountryCode))
		}
	case "discount":
		productCodes, err := aloha.GetTodaysPromotedProduct(ctx, int(storeIDs[0]), channel, userType)
		if err != nil {
			return nil, grpc.ErrorFrom(ctx, err)
		}

		for _, pc := range productCodes {
			skus = append(skus, strs.ProductCode(pc).ToSKU(in.CountryCode))
		}
	case "bundle":
		productCodes, err := aloha.GetTodaysPromotedBundleProduct(ctx, int(storeIDs[0]), channel, in.CountryCode, userType)
		if err != nil {
			return nil, grpc.ErrorFrom(ctx, err)
		}

		for _, pc := range productCodes {
			skus = append(skus, strs.ProductCode(pc).ToSKU(in.CountryCode))
		}
	case "exclusive":
		wg := &sync.WaitGroup{}
		// add this value if calling more service later
		wait_service_count := 3
		wg.Add(wait_service_count)
		spreePromo := []*datastore.StockItemPromotion{}
		alohaPromo := []string{}
		internalPricePromo := []string{}
		go func(ctx context.Context, storeID int) {
			defer wg.Done()
			stockItems, err := datastore.StockItemPromotionByStoreAndIsExclusive(ctx, int(storeIDs[0]), true)
			if err != nil {
				log.ForContext(ctx, "handler", "BestDeal").Errorf("Error: %+v", err)
				return
			}
			spreePromo = stockItems.StockItemPromotions
		}(ctx, int(storeIDs[0]))

		go func(ctx context.Context, storeID int, countryCode string, channel string) {
			defer wg.Done()
			alohaPromoCodes, err := promotion.GetExclusivePromotion(ctx, storeID, countryCode, channel)
			if err != nil {
				log.ForContext(ctx, "handler", "BestDeal").Errorf("Error: %+v", err)
				return
			}
			alohaPromo = alohaPromoCodes
		}(ctx, int(storeIDs[0]), in.CountryCode, channel)

		go func(ctx context.Context, storeID int) {
			defer wg.Done()
			internalPrices, err := pricing.GetExclusivePromotion(ctx, storeID)
			if err != nil {
				log.ForContext(ctx, "handler", "BestDeal").Errorf("Error: %+v", err)
				return
			}
			internalPricePromo = internalPrices
		}(ctx, int(storeIDs[0]))

		wg.Wait()

		// remove alcohol for ramadhan
		// taxonNoneFilter = []int64{103, 1270}

		for _, p := range spreePromo {
			skus = append(skus, p.Sku)
		}
		for _, p := range alohaPromo {
			skus = append(skus, fmt.Sprintf("%s-%s", p, strings.ToUpper(in.CountryCode)))
		}
		for _, p := range internalPricePromo {
			skus = append(skus, fmt.Sprintf("%s-%s", p, strings.ToUpper(in.CountryCode)))
		}
	default:
		stockItems, err := datastore.StockItemPromotionByStoreAndTypes(ctx, int(storeIDs[0]), in.PromotionType)
		if err != nil {
			return nil, grpc.ErrorFrom(ctx, err)
		}

		for _, p := range stockItems.StockItemPromotions {
			skus = append(skus, p.Sku)
		}
	}

	if len(skus) == 0 {
		skus = []string{"-1"}
	}

	return s.search(
		ctx,
		&rpc.SearchRequest{
			Skus:                        skus,
			TaxonIds:                    in.TaxonIds,
			BrandIds:                    in.BrandIds,
			StoreIds:                    storeIDs,
			StockLocationIds:            in.StockLocationIds,
			ShowOos:                     in.ShowOos,
			Sorting:                     in.Sorting,
			PerPage:                     in.PerPage,
			Page:                        in.Page,
			CountryCode:                 in.CountryCode,
			ShowPromotionTypeFilter:     in.ShowPromotionTypeFilter,
			DisableLimitedStockQuantity: true,
			TaxonNoneFilter:             taxonNoneFilter,
		},
		s.elasticsearchClient,
	)
}

func (s *SearchService) ProductsByVariants(ctx context.Context, in *rpc.SearchRequest) (*rpc.SearchResponse, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Failed to parse metadata"))
	}

	variantIDs := in.VariantIDs()
	stockLocationIDs := in.StockLocationIDs()
	locale := in.Locale(md)
	userType := in.UserType(md)
	size, page := in.Pagination()
	_, nonSellableItems := in.Sellables(md)
	clientVersion := in.ClientVersion(md)
	isSND := in.IsSND(md)
	isSynthetic := in.IsSynthetic(md)
	userID := in.UserID(md)
	deviceID := in.DeviceID(md)
	orderNumber := in.OrderNumber(md)

	// To be used only for snd features that are related to searching, fetching price, and mpq
	orderChannel := in.OrderChannel(md)

	abMarkup := sprinkles.GetAbMarkup(ctx, int(userID), deviceID, orderNumber)

	country, err := s.hubbleClient.GetCountryByIso(ctx, in.CountryCode)
	if err != nil {
		return nil, grpc.NewStatusError(codes.InvalidArgument, err)
	}

	maxMOQ := country.GetPreferencesNormalMoq()
	if maxMOQ < 0 {
		maxMOQ = s.defaultMaxMOQ
	}

	maxMOQHC := country.GetPreferencesHcMoq()
	if maxMOQHC < 0 {
		maxMOQHC = s.defaultMaxMOQHC
	}

	storeProducts, err := s.catalogClient.GetStoreProductsPLP(ctx, variantIDs, stockLocationIDs[0], locale)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	productsPrices, err := pricing.Get(
		ctx,
		pricing.WithProducts(storeProducts),
		pricing.WithChannel(orderChannel),
		pricing.WithUserType(userType),
		pricing.WithCountryCode(in.CountryCode),
		pricing.WithAbMarkup(abMarkup),
	)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	taxonNoneFilterMap := map[int64]struct{}{}
	catalogRestrictedTaxons, err := s.catalogClient.GetRestrictedTaxons(ctx, 0, stockLocationIDs[0], orderChannel, isSND)
	if err != nil {
		log.ForContext(ctx, "handler", "search").Warn(err)
		taxonNoneFilterMap = s.taxonNoneFilterMap
	} else {
		taxonNoneFilter := catalogRestrictedTaxons.RestrictedTaxonIds
		for _, id := range taxonNoneFilter {
			taxonNoneFilterMap[id] = struct{}{}
		}
	}

	paginatedStoreProducts, meta := func() ([]*cat.StoreProducts, *index.Meta) {
		pp := make([]*cat.StoreProducts, 0, size)
		count := int64(0)
		upperCount := page * size
		lowerCount := upperCount - size
		nonSellableMap := map[string]struct{}{}
		for _, n := range nonSellableItems {
			nonSellableMap[n] = struct{}{}
		}

		for _, p := range storeProducts {
			func() {
				if p.ProductDetail == nil {
					return
				}

				taxonIDs := p.ProductDetail.TaxonIds
				for _, taxonID := range taxonIDs {
					_, ok := taxonNoneFilterMap[taxonID]
					if ok {
						return
					}
				}

				productCode := strs.SKU(p.ProductDetail.Variants[0].Sku).ToProductCode(in.CountryCode)
				price, priceOk := productsPrices[productCode]
				_, nonSellableOk := nonSellableMap[p.ProductDetail.SellableItems]

				if ((priceOk && price.InStock) || in.ShowOos) && !nonSellableOk {
					if count >= lowerCount && count < upperCount {
						pp = append(pp, p)
					}
					count++
				}
			}()
		}

		return pp, &index.Meta{
			Page:       page,
			PageSize:   size,
			TotalPages: ((count - 1) / size) + 1,
			Count:      int64(len(pp)),
			TotalCount: count,
		}
	}()

	eg, egCtx := errgroup.WithContext(ctx)

	var translatedIDs []*hub.TranslatedID
	eg.Go(func() error {
		productIDs := make([]int64, len(paginatedStoreProducts))
		for i, p := range paginatedStoreProducts {
			productIDs[i] = p.ProductDetail.ProductId
		}

		t, err := s.idConverter.PLPIDs(egCtx, productIDs)
		if err != nil {
			return grpc.ErrorFrom(ctx, err)
		}

		translatedIDs = t
		return nil
	})

	productsPromotions := map[string]*promotion.Promotion{}
	eg.Go(func() error {
		p, err := promotion.Get(egCtx, paginatedStoreProducts, locale)
		if err != nil {
			log.ForContext(ctx, "handler", "search").Error(err)
		} else {
			productsPromotions = p
		}

		return nil
	})

	var isMPQEligible bool
	eg.Go(func() error {
		isMPQEligible = aloha.IsMpqEligible(egCtx, orderChannel, clientVersion)

		return nil
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	out, err := response.Map(
		ctx,
		paginatedStoreProducts,
		response.RootOptionChain(
			response.WithMetaAndPageLimiter(meta, s.pageLimiter, s.version),
			response.WithAbMarkup(abMarkup),
		),
		response.ProductOptionChain(
			response.WithNamingAndDescription(locale),
			response.WithBrandNaming(locale),
			response.WithCategories(locale),
			response.WithVendor(locale),
			response.WithAlohaPricing(productsPrices, country.Currency, in.CountryCode, locale, isMPQEligible, in.DisableLimitedStockQuantity),
			response.WithPromotionLabels(productsPromotions),
			response.WithStorePriceIncluded(isSND),
			response.WithTranslatedIDs(translatedIDs),
			response.WithGlobalIDs(isSynthetic),
			response.WithProductMOQ(productsPrices, userType, maxMOQ, maxMOQHC, in.CountryCode, in.DisableLimitedStockQuantity),
		),
	)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	return out, nil
}

func (s *SearchService) ProductsByPromotion(ctx context.Context, in *rpc.ProductsByPromotionRequest) (*rpc.SearchResponse, error) {
	stockLocationIDs := in.StockLocationIDs()
	if len(stockLocationIDs) == 0 {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Empty stock location IDs"))
	}

	promotion, err := datastore.PromotionByID(ctx, int(in.PromotionId))
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	variants, err := datastore.VariantsByPromotionDetailAndStockLocationID(ctx, promotion, int(stockLocationIDs[0]))
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	vIDs := make([]int64, len(variants.Variants))
	for i, v := range variants.Variants {
		vIDs[i] = v.Id
	}

	manualSKUBoostsByPromo, err := s.catalogClient.GetManualSKUBoostsByPromoCode(ctx, promotion.Code, stockLocationIDs[0])
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	var pinnedVariantIDs []int64
	if manualSKUBoostsByPromo.ManualSkuBoosts != nil {
		sort.Slice(manualSKUBoostsByPromo.ManualSkuBoosts, func(i, j int) bool {
			return manualSKUBoostsByPromo.ManualSkuBoosts[i].Position < manualSKUBoostsByPromo.ManualSkuBoosts[j].Position
		})

		for _, boost := range manualSKUBoostsByPromo.ManualSkuBoosts {
			// 0 stock location id means boost applies for every store
			if (boost.StockLocationId == stockLocationIDs[0] || boost.StockLocationId == 0) && boost.Position > 0 {
				pinnedVariantIDs = append(pinnedVariantIDs, boost.VariantId)
			}
		}
	}

	sClient := s.pqClient

	if strings.Contains(in.Sorting, "discount") {
		sClient = s.elasticsearchClient
	}

	return s.search(
		ctx,
		&rpc.SearchRequest{
			VariantIds:       vIDs,
			TaxonIds:         in.TaxonIds,
			BrandIds:         in.BrandIds,
			StoreIds:         in.StoreIds,
			StockLocationIds: in.StockLocationIds,
			ShowOos:          in.ShowOos,
			Sorting:          in.Sorting,
			PerPage:          in.PerPage,
			Page:             in.Page,
			CountryCode:      in.CountryCode,
			PinnedVariantIds: pinnedVariantIDs,
		},
		sClient,
	)
}

func (s *SearchService) SuggestSearch(ctx context.Context, in *rpc.SearchSuggestionRequest) (*rpc.SearchSuggestionResponse, error) {
	if s.disableSearchSuggestion {
		return &rpc.SearchSuggestionResponse{
			Keywords:        []string{},
			KeywordsInTaxon: []*rpc.KeywordTaxon{},
			Corrections:     []string{},
			PastItems:       []string{},
			EngineVersion:   s.version,
		}, nil
	}

	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Failed to parse metadata"))
	}

	storeIDs := in.StoreIDs()
	stockLocationIDs := in.StockLocationIDs()
	if len(storeIDs) == 0 || len(stockLocationIDs) == 0 {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Failed to parse metadata"))
	}

	var supplierID int64 = -1
	stockLocation, err := datastore.StockLocationByID(ctx, int(stockLocationIDs[0]))
	if err == nil {
		supplierID = stockLocation.SupplierId
	}

	storeSupplierCategoryIDs, _ := datastore.SupplierStoreCategoryIDsBySupplierID(ctx, stockLocation.SupplierId)

	locale := in.Locale(md)

	suggester, err := s.suggesterShards.Get(in.CountryCode)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	suggestOptions := []index.SuggestOption{
		index.WithSuggesterQuery(in.NormalizedQuery()),
		index.WithSuggesterStockLocationID(stockLocationIDs[0]),
		index.WithSuggesterSupplierID(supplierID),
		index.WithSuggesterStoreCategory(storeSupplierCategoryIDs...),
		index.WithSuggesterLocale(locale),
		index.WithSuggesterSize(5),
	}
	if in.CountryCode == "TH" {
		suggestOptions = append(suggestOptions, index.WithSuggesterFuzziness(1))
	}

	b, err := suggester.Suggest(ctx, suggestOptions...)
	if err != nil {
		e, ok := err.(*index.Error)
		if ok {
			return nil, grpc.NewStatusError(grpc.StatusFromHTTP(e.Code), e.Unwrap())
		}

		return nil, grpc.ErrorFrom(ctx, err)
	}

	keywordTaxon := make([]*rpc.KeywordTaxon, len(b.KeywordInTaxons))
	for i, k := range b.KeywordInTaxons {
		keywordTaxon[i] = &rpc.KeywordTaxon{
			Keyword: k.Keyword,
			Taxon: &rpc.Taxon{
				Id:   int64(k.TaxonID),
				Name: k.TaxonName,
			},
		}
	}

	taxonSuggestion := make([]*rpc.Taxon, len(b.Taxons))
	for i, t := range b.Taxons {
		taxonSuggestion[i] = &rpc.Taxon{
			Id:   t.ID,
			Name: t.Name,
		}
	}

	brandSuggestion := make([]*rpc.Brand, len(b.Brands))
	for i, br := range b.Brands {
		brandSuggestion[i] = &rpc.Brand{
			Id:   br.ID,
			Name: br.Name,
		}
	}

	if in.Channel(md) == "webapp" {
		if len(keywordTaxon) > 2 {
			keywordTaxon = keywordTaxon[:2]
		}

		if len(brandSuggestion) > 1 {
			brandSuggestion = brandSuggestion[:1]
		}
	}

	return &rpc.SearchSuggestionResponse{
		Keywords:        b.Keywords,
		KeywordsInTaxon: keywordTaxon,
		Corrections:     []string{},
		PastItems:       []string{},
		Taxons:          taxonSuggestion,
		Brands:          brandSuggestion,
		EngineVersion:   s.version,
	}, nil
}

func (s *SearchService) getPromotedSKUsAndMaxProductCount(ctx context.Context, keyword string) ([]string, int64) {
	result, err := datastore.SearchAdsListItemsByKeyword(ctx, keyword)
	if err != nil || len(result) == 0 {
		return nil, 0
	}

	skus := []string{}
	for _, v := range result {
		skus = append(skus, v.Sku)
	}

	return skus, result[0].MaxProductCount
}

func containsInt64(ints []int64, val int64) bool {
	for _, i := range ints {
		if i == val {
			return true
		}
	}
	return false
}

func (s *SearchService) search(ctx context.Context, in *rpc.SearchRequest, indexClient index.Client) (*rpc.SearchResponse, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, grpc.NewStatusError(codes.InvalidArgument, errors.New("Failed to parse metadata"))
	}

	normalizedQuery := in.NormalizedQuery()
	stockItemIDs := in.StockItemIDs()
	variantIDs := in.VariantIDs()
	SKUs := in.SKUs()
	taxonIDs := in.TaxonIDs()
	productTypeIDs := in.ProductTypeIDs()
	brandIDs := in.BrandIDs()
	storeIDs := in.StoreIDs()
	stockLocationIDs := in.StockLocationIDs()
	inStock := in.InStock(md)
	sorting := in.SortBy(md)
	size, page := in.Pagination()
	channel := in.Channel(md)
	locale := in.Locale(md)
	clientVersion := in.ClientVersion(md)
	userType := in.UserType(md)
	isSynthetic := in.IsSynthetic(md)
	isSND := in.IsSND(md)
	searchVariant := in.SearchVariant(md)
	userID := in.UserID(md)
	_, nonSellableItems := in.Sellables(md)
	vendorID := in.GetVendorId()
	pinnedVariantIDs := in.PinnedVariantIDs()
	anonymousID := in.GetAnonymousID(md)
	promotedSKUs := in.GetPromotedSkus()
	maxPromotedProductCount := in.GetMaxPromotedProductCount()
	deviceID := in.DeviceID(md)
	orderNumber := in.OrderNumber(md)

	abMarkup := sprinkles.GetAbMarkup(ctx, int(userID), deviceID, orderNumber)

	// To be used only for snd features that are related to searching, fetching price, and mpq
	orderChannel := in.OrderChannel(md)

	i, err := indexClient.Index(in.CountryCode)
	if err != nil {
		return nil, grpc.NewStatusError(codes.InvalidArgument, err)
	}

	country, err := s.hubbleClient.GetCountryByIso(ctx, in.CountryCode)
	if err != nil {
		return nil, grpc.NewStatusError(codes.InvalidArgument, err)
	}

	maxMOQ := country.GetPreferencesNormalMoq()
	if maxMOQ < 0 {
		maxMOQ = s.defaultMaxMOQ
	}

	maxMOQHC := country.GetPreferencesHcMoq()
	if maxMOQHC < 0 {
		maxMOQHC = s.defaultMaxMOQHC
	}

	filterWG := &sync.WaitGroup{}
	filterWG.Add(2)

	filters := &rpc.FilterLock{}
	go func() {
		defer filterWG.Done()

		if !in.ShowPromotionTypeFilter || len(storeIDs) <= 0 {
			return
		}

		pfWG := &sync.WaitGroup{}
		pfWG.Add(3)

		m := &sync.RWMutex{}

		promoFilter := []*rpc.PromotionType{}
		go func() {
			defer pfWG.Done()

			productCodes, err := aloha.GetTodaysPromotedProduct(ctx, int(storeIDs[0]), channel, userType)
			if err != nil {
				log.ForContext(ctx, "search", "PromotionTypesFilter/GET").Error(err)
				return
			}

			promotionTypes := []*rpc.PromotionType{}
			if len(productCodes) > 0 {
				promotionTypes = []*rpc.PromotionType{
					{
						Type:  "discount",
						Total: int64(len(productCodes)),
					},
				}
			}

			m.Lock()
			promoFilter = append(promoFilter, promotionTypes...)
			m.Unlock()
		}()

		go func() {
			defer pfWG.Done()

			allowed := false
			for _, ac := range s.promotionTypeFilterChannel {
				if channel == ac {
					allowed = true
					break
				}
			}

			if !allowed {
				return
			}

			productCodes, err := aloha.GetTodaysPromotedBundleProduct(ctx, int(storeIDs[0]), channel, in.CountryCode, userType)
			if err != nil {
				log.ForContext(ctx, "search", "PromotionTypesFilter/GET").Error(err)
				return
			}

			promotionTypes := []*rpc.PromotionType{}
			if len(productCodes) > 0 {
				promotionTypes = []*rpc.PromotionType{
					{
						Type:  "bundle",
						Total: int64(len(productCodes)),
					},
				}
			}

			m.Lock()
			promoFilter = append(promoFilter, promotionTypes...)
			m.Unlock()
		}()

		go func() {
			defer pfWG.Done()

			promotionTypeResult, err := datastore.StockItemPromotionTypeCount(ctx, int(storeIDs[0]))
			if err != nil {
				log.ForContext(ctx, "search", "PromotionTypesFilter/GET").Error(err)
				return
			}

			promotionTypes := []*rpc.PromotionType{}
			for _, sip := range promotionTypeResult.GetStockItemPromotions() {
				if sip.Type == "discount" || sip.Type == "bundle" {
					continue
				}

				promotionTypes = append(promotionTypes, &rpc.PromotionType{
					Type:  sip.GetType(),
					Total: sip.GetTotal(),
				})
			}

			m.Lock()
			promoFilter = append(promoFilter, promotionTypes...)
			m.Unlock()
		}()

		pfWG.Wait()

		filters.SetPromotionTypes(promoFilter)
	}()

	swg := &sync.WaitGroup{}
	swg.Add(7)

	var sessionID string
	var queryID string
	go func() {
		defer swg.Done()

		id := str.String(anonymousID).EmptyOrDefault(in.UserToken(md)).String()

		sID, err := session.GenerateSearchSessionID(ctx, id)
		if err != nil {
			log.ForContext(ctx, "search", "sessionID").Warn(err)
			return
		}

		var taxonID int64
		var brandID int64
		if len(taxonIDs) > 0 {
			taxonID = taxonIDs[0]
		}
		if len(brandIDs) > 0 {
			brandID = brandIDs[0]
		}

		qID, err := session.GenerateQueryID(ctx, id, normalizedQuery, sorting, stockLocationIDs[0], taxonID, brandID)
		if err != nil {
			log.ForContext(ctx, "search", "queryID").Warn(err)
			return
		}

		sessionID = sID
		queryID = qID
	}()

	var esSynonymTerms []string
	go func() {
		defer swg.Done()

		if !s.enableESSynonym || isSND || normalizedQuery == "" {
			return
		}

		esSynonym, err := s.esSynonymAdapters.Get(in.CountryCode)
		if err != nil {
			log.ForContext(ctx, "search", "ES-synonym").Warn(err)
			return
		}
		response, err := esSynonym.Get(ctx, normalizedQuery)
		if err != nil {
			log.ForContext(ctx, "search", "ES-synonym").Warn(err)
			return
		}

		esSynonymTerms = response.Terms
	}()

	var translatedQuery string
	go func() {
		defer swg.Done()

		tq, err := s.translator.Translate(ctx, in.CountryCode, "en", normalizedQuery)
		if err != nil {
			log.Warn(err)
		}

		translatedQuery = tq
	}()

	taxonNoneFilter := in.TaxonNoneFilter

	go func() {
		defer swg.Done()
		catalogRestrictedTaxons, err := s.catalogClient.GetRestrictedTaxons(ctx, 0, stockLocationIDs[0], channel, isSND)
		if err != nil {
			log.ForContext(ctx, "handler", "search").Warn(err)
			taxonNoneFilter = append(taxonNoneFilter, s.taxonNoneFilter...)
		} else {
			taxonNoneFilter = append(taxonNoneFilter, catalogRestrictedTaxons.RestrictedTaxonIds...)
		}
	}()

	go func() {
		defer swg.Done()

		taxonomyList, err := s.catalogClient.GetTaxonomy(ctx, 1, stockLocationIDs[0], locale, in.CountryCode)

		if err != nil {
			log.ForContext(ctx, "handler", "search").Warn(err)
			return
		}

		allowedTaxonIDs := s.catalogClient.GetTaxonIDs(taxonomyList.GetTaxons())
		if len(taxonIDs) == 0 {
			taxonIDs = allowedTaxonIDs
		} else {
			filteredTaxonIDs := []int64{}
			for _, taxonID := range taxonIDs {
				if containsInt64(allowedTaxonIDs, taxonID) {
					filteredTaxonIDs = append(filteredTaxonIDs, taxonID)
				}
			}
			taxonIDs = filteredTaxonIDs
		}
	}()

	var stemmedQueries []string
	go func() {
		defer swg.Done()

		stemmedQueries = stemmer.StemQuery(normalizedQuery)
	}()

	var searchIntentionAttributes orion.SearchIntentionResponse
	go func() {
		defer swg.Done()

		if normalizedQuery == "" {
			return
		}

		r, err := s.orionClient.ConvertTermToSearchIntention(ctx, normalizedQuery, in.CountryCode, "G")
		if err != nil {
			log.ForContext(ctx, "handler", "search").Warn(err)
			return
		}

		searchIntentionAttributes = *r
	}()

	var recommendedPtys map[string]float64
	var syn string
	var validSynonym bool
	var staticSynonym bool
	var queryTransformed bool
	var productBoosts map[string]float64
	var terms []string
	var pTypeLeafIDs []string
	if !s.disableOrion && !isSND && normalizedQuery != "" {
		sCtx := synonym.NewContext(ctx, "country_iso", in.CountryCode)

		uID := ""
		if userID != -1 {
			uID = strconv.FormatInt(userID, 10)
		}
		sCtx = synonym.NewContext(sCtx, "user_id", uID)

		sCtx = synonym.NewContext(sCtx, "synonym_variant", searchVariant)

		syno, err := synonym.Get(sCtx, normalizedQuery)
		if err != nil {
			log.ForContext(ctx, "handler", "search").Error(err)
			syn = normalizedQuery
			terms = []string{normalizedQuery}
		} else {
			syn = syno.Result
			terms = syno.Terms
			recommendedPtys = syno.ProductTypes
			pTypeLeafIDs = syno.ProductTypeLeafIds
			validSynonym = syno.ValidSynonym
			staticSynonym = syno.StaticSynonym
			productBoosts = syno.ProductBoosts
			queryTransformed = syno.QueryTransformed

			if len(terms) == 0 {
				terms = []string{normalizedQuery}
			}
		}
	} else {
		var err error
		syn = normalizedQuery
		terms = []string{normalizedQuery}
		recommendedPtys, err = recommendation.Get(ctx, normalizedQuery)
		if err != nil {
			log.ForContext(ctx, "handler", "search").Error(err)
		}
	}

	swg.Wait()

	terms = append(terms, esSynonymTerms...)
	if translatedQuery != "" && translatedQuery != normalizedQuery {
		terms = append(terms, translatedQuery)
	}

	searchOptions := []index.SearchOption{
		index.WithTermQueries(terms...),
		index.WithTermQueries(stemmedQueries...),
		index.WithFuzzyQueries(normalizedQuery),
		index.WithTermFilterInt64("store_id", storeIDs...),
		index.WithTermFilterInt64("taxon_ids", taxonIDs...),
		index.WithTermFilterString("product_type_ids", productTypeIDs...),
		index.WithTermFilterString("in_stock", inStock...),
		index.WithTermFilterInt64("variant_id", variantIDs...),
		index.WithTermFilterString("id", stockItemIDs...),
		index.WithTermFilterString("sku", SKUs...),
		index.WithNoneTermFilterString("sellable_items", nonSellableItems...),
		index.WithNoneTermFilterInt64("taxon_ids", taxonNoneFilter...),
		index.WithNoneTermFilterInt64("price_android", 0),
		index.WithChannel(orderChannel),
		index.WithSorting("in_stock desc"),
		index.WithSorting(sorting),
		index.WithLocale(locale),
		index.WithTermFilterInt64("brand_id", brandIDs...),
		index.WithPagination(int(size), int(page)),
		index.WithAnalyticTags("channel", channel),
		index.WithConversionBoost("product_type_ids", recommendedPtys),
		index.WithResultPinning(pinnedVariantIDs),
		index.WithCountry(country.IsoName),
	}

	var pinnedEsIDs []int64

	if len(promotedSKUs) > 0 {
		promotedSearchOptions := []index.SearchOption{
			index.WithTermFilterString("sku", promotedSKUs...),
			index.WithTermFilterInt64("store_id", storeIDs...),
			index.WithTermFilterString("in_stock", inStock...),
			index.WithNoneTermFilterString("sellable_items", nonSellableItems...),
			index.WithPagination(int(size), int(page)),
			index.WithCountry(country.IsoName),
		}

		promotedIndexSearchResponse, err := i.Search(ctx, promotedSearchOptions...)
		if err != nil {
			e, ok := err.(*index.Error)
			if ok {
				return nil, grpc.NewStatusError(grpc.StatusFromHTTP(e.Code), e.Unwrap())
			}

			return nil, grpc.ErrorFrom(ctx, err)
		}

		if promotedIndexSearchResponse.StockItems != nil {
			stockItemsMap := make(map[string]*index.StockItem)
			for _, item := range promotedIndexSearchResponse.StockItems {
				stockItemsMap[item.SKU] = item
			}

			for _, sku := range promotedSKUs {
				stockItem := stockItemsMap[sku]
				if stockItem == nil {
					continue
				}

				tempEsID, err := strconv.ParseInt(stockItem.EsID, 10, 64)
				if err != nil {
					continue
				}
				pinnedEsIDs = append(pinnedEsIDs, tempEsID)
			}

			if pinnedEsIDs != nil {
				if maxPromotedProductCount > 0 && len(pinnedEsIDs) > int(maxPromotedProductCount) {
					pinnedEsIDs = pinnedEsIDs[:maxPromotedProductCount]
				}

				searchOptions = append(searchOptions, index.WithResultPinning(pinnedEsIDs))
			}
		}
	}

	if vendorID > 0 {
		searchOptions = append(searchOptions, index.WithTermFilterInt64("vendor_id", vendorID))
	}
	if in.CountryCode == "TH" {
		searchOptions = append(
			searchOptions,
			index.WithTermFilterInt64("stock_location_id", stockLocationIDs...),
		)
	}
	if userID != -1 {
		searchOptions = append(searchOptions, index.WithAnalyticTags("user_ids", userID), index.WithConversionBoost("sku", productBoosts))
	}
	if userType != "" {
		searchOptions = append(searchOptions, index.WithAnalyticTags("user_type", userType))
	}
	if normalizedQuery == "" {
		searchOptions = append(searchOptions, index.WithFunctionalBoosts("popularity"))
	}
	if normalizedQuery != "" && !isSND && (sorting == "_score desc" || sorting == "_score asc") {
		searchOptions = append(searchOptions, index.WithTrendingBoost(s.trendingBoostWeight, s.trendingBoostActivePeriod))

		if s.rawPopularityBoostScore != 0 {
			searchOptions = append(searchOptions, index.WithFunctionalBoostsScore(map[string]float64{"raw_popularity": s.rawPopularityBoostScore}))
		}
	}
	if !isSND {
		searchOptions = append(searchOptions, index.WithFacet("brands"))
		searchOptions = append(searchOptions, index.WithFacet("taxons"))
	}
	for _, attrs := range searchIntentionAttributes.AttributeCombinations {
		searchOptions = append(searchOptions, index.WithSearchIntentionAttributes(attrs.TaxonIds, attrs.ProductTypeIds, attrs.BrandIds, attrs.Score))
	}

	indexSearchResponse, err := i.Search(ctx, searchOptions...)
	if err != nil {
		e, ok := err.(*index.Error)
		if ok {
			return nil, grpc.NewStatusError(grpc.StatusFromHTTP(e.Code), e.Unwrap())
		}

		return nil, grpc.ErrorFrom(ctx, err)
	}

	correctedQuery := normalizedQuery
	if normalizedQuery != "" && len(indexSearchResponse.StockItems) == 0 && !s.disableOrion {
		func() {
			c, cqErr := s.orionClient.CorrectQuery(ctx, normalizedQuery, in.CountryCode)
			if cqErr != nil {
				log.ForContext(ctx, "handler", "search").Warn(cqErr)
				return
			}

			if c == "" {
				return
			}

			correctedQuery = c

			swg := &sync.WaitGroup{}
			swg.Add(3)

			var esSynonymTerms []string
			go func() {
				defer swg.Done()

				if !s.enableESSynonym || isSND || correctedQuery == "" {
					return
				}

				esSynonym, err := s.esSynonymAdapters.Get(in.CountryCode)
				if err != nil {
					log.ForContext(ctx, "search", "ES-synonym").Warn(err)
					return
				}
				response, err := esSynonym.Get(ctx, correctedQuery)
				if err != nil {
					log.ForContext(ctx, "search", "ES-synonym").Warn(err)
					return
				}

				esSynonymTerms = response.Terms
			}()

			var stemmedQueries []string
			go func() {
				defer swg.Done()

				stemmed := stemmer.StemQuery(correctedQuery)
				if err == nil {
					log.ForContext(ctx, "handler", "search").Warn(err)
				}

				stemmedQueries = stemmed
			}()

			go func() {
				defer swg.Done()

				if correctedQuery == "" {
					return
				}

				r, err := s.orionClient.ConvertTermToSearchIntention(ctx, correctedQuery, in.CountryCode, "G")
				if err != nil {
					log.ForContext(ctx, "handler", "search").Warn(err)
					return
				}

				searchIntentionAttributes = *r
			}()

			if !s.disableOrion && !isSND {
				sCtx := synonym.NewContext(ctx, "country_iso", in.CountryCode)

				uID := ""
				if userID != -1 {
					uID = strconv.FormatInt(userID, 10)
				}
				sCtx = synonym.NewContext(sCtx, "user_id", uID)

				sCtx = synonym.NewContext(sCtx, "synonym_variant", searchVariant)

				syno, err := synonym.Get(sCtx, correctedQuery)
				if err != nil {
					log.ForContext(ctx, "handler", "search").Error(err)
					syn = correctedQuery
					terms = []string{correctedQuery}
				} else {
					syn = syno.Result
					terms = syno.Terms
					recommendedPtys = syno.ProductTypes
					pTypeLeafIDs = syno.ProductTypeLeafIds
					validSynonym = syno.ValidSynonym
					staticSynonym = syno.StaticSynonym
					productBoosts = syno.ProductBoosts
					queryTransformed = syno.QueryTransformed

					if len(terms) == 0 {
						terms = []string{correctedQuery}
					}
				}
			} else {
				syn = correctedQuery
				terms = []string{correctedQuery}
				recommendedPtys, err = recommendation.Get(ctx, correctedQuery)
				if err != nil {
					log.ForContext(ctx, "handler", "search").Error(err)
				}
			}

			swg.Wait()
			terms = append(terms, esSynonymTerms...)

			searchOptions := []index.SearchOption{
				index.WithTermQueries(terms...),
				index.WithTermQueries(stemmedQueries...),
				index.WithFuzzyQueries(correctedQuery),
				index.WithTermFilterInt64("store_id", storeIDs...),
				index.WithTermFilterInt64("taxon_ids", taxonIDs...),
				index.WithTermFilterString("product_type_ids", productTypeIDs...),
				index.WithTermFilterString("in_stock", inStock...),
				index.WithTermFilterInt64("variant_id", variantIDs...),
				index.WithTermFilterString("id", stockItemIDs...),
				index.WithTermFilterString("sku", SKUs...),
				index.WithNoneTermFilterString("sellable_items", nonSellableItems...),
				index.WithNoneTermFilterInt64("taxon_ids", taxonNoneFilter...),
				index.WithNoneTermFilterInt64("price_android", 0),
				index.WithLocale(locale),
				index.WithChannel(channel),
				index.WithTermFilterInt64("brand_id", brandIDs...),
				index.WithSorting("in_stock desc"),
				index.WithSorting(sorting),
				index.WithPagination(int(size), int(page)),
				index.WithAnalyticTags("channel", channel),
				index.WithConversionBoost("product_type_ids", recommendedPtys),
				index.WithCountry(country.IsoName),
			}
			if in.CountryCode == "TH" {
				searchOptions = append(
					searchOptions,
					index.WithTermFilterInt64("stock_location_id", stockLocationIDs...),
				)
			}
			if userID != -1 {
				searchOptions = append(searchOptions, index.WithAnalyticTags("user_ids", userID), index.WithConversionBoost("sku", productBoosts))
			}
			if userType != "" {
				searchOptions = append(searchOptions, index.WithAnalyticTags("user_type", userType))
			}
			if normalizedQuery == "" {
				searchOptions = append(searchOptions, index.WithFunctionalBoosts("popularity"))
			}
			if normalizedQuery != "" && !isSND && (sorting == "_score desc" || sorting == "_score asc") {
				searchOptions = append(searchOptions, index.WithTrendingBoost(s.trendingBoostWeight, s.trendingBoostActivePeriod))

				if s.rawPopularityBoostScore != 0 {
					searchOptions = append(searchOptions, index.WithFunctionalBoostsScore(map[string]float64{"raw_popularity": s.rawPopularityBoostScore}))
				}
			}
			if !isSND {
				searchOptions = append(searchOptions, index.WithFacet("brands"))
				searchOptions = append(searchOptions, index.WithFacet("taxons"))
			}
			for _, attrs := range searchIntentionAttributes.AttributeCombinations {
				searchOptions = append(searchOptions, index.WithSearchIntentionAttributes(attrs.TaxonIds, attrs.ProductTypeIds, attrs.BrandIds, attrs.Score))
			}

			indexSearchResponse, err = i.Search(ctx, searchOptions...)
		}()
	}
	if err != nil {
		e, ok := err.(*index.Error)
		if ok {
			return nil, grpc.NewStatusError(grpc.StatusFromHTTP(e.Code), e.Unwrap())
		}

		return nil, grpc.ErrorFrom(ctx, err)
	}

	go func() {
		defer filterWG.Done()

		taxonFilters, err := response.MapTaxonFilter(ctx, indexSearchResponse)
		if err != nil {
			log.For("search", "TaxonFilter/GET").Warn(err)
		} else {
			filters.SetTaxons(taxonFilters)
		}

		filterBrandIDs, err := response.MapToBrandIDs(ctx, indexSearchResponse)
		if err != nil {
			log.For("search", "MapToBrandIDs").Warn(err)
			return
		}

		brands, err := s.catalogClient.GetBrands(ctx, filterBrandIDs, locale)
		if err != nil {
			log.For("search", "GetBrands").Warn(err)
			return
		}

		brandFilters, err := response.MapBrandFilter(ctx, brands)
		if err != nil {
			log.For("search", "MapBrandFilter").Warn(err)
			return
		}

		filters.SetBrands(brandFilters)
	}()

	stockItemIDsResult := make([]int64, len(indexSearchResponse.StockItems))
	productIDsResult := make([]int64, len(indexSearchResponse.StockItems))
	variantIDsResult := make([]int64, len(indexSearchResponse.StockItems))
	productSearchScore := make(map[int64]float64)
	productRawPopularity := make(map[int64]float64)
	trendingProductIDs := make([]string, 0)
	for i, s := range indexSearchResponse.StockItems {
		stockItemIDsResult[i] = s.ID
		productIDsResult[i] = s.ProductID
		variantIDsResult[i] = s.VariantID
		productSearchScore[s.ID] = s.Score
		productRawPopularity[s.ID] = s.RawPopularity

		if s.Trending {
			trendingProductIDs = append(trendingProductIDs, strconv.FormatInt(s.ProductID, 10))
		}
	}

	eg, egCtx := errgroup.WithContext(ctx)

	var productProperties []*cat.StoreProducts
	eg.Go(func() error {
		r, err := s.catalogClient.GetStoreProductsPLP(egCtx, variantIDsResult, stockLocationIDs[0], locale)
		if err != nil {
			return grpc.ErrorFrom(egCtx, err)
		}

		productProperties = r
		return nil
	})

	var translatedIDs []*hub.TranslatedID
	eg.Go(func() error {
		r, err := s.idConverter.PLPIDs(egCtx, productIDsResult)
		if err != nil {
			return grpc.ErrorFrom(egCtx, err)
		}

		translatedIDs = r
		return nil
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	productsPrices, err := pricing.Get(
		ctx,
		pricing.WithProducts(productProperties),
		pricing.WithChannel(orderChannel),
		pricing.WithUserType(userType),
		pricing.WithCountryCode(in.CountryCode),
		pricing.WithAbMarkup(abMarkup),
	)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	wg := &sync.WaitGroup{}
	wg.Add(3)

	var searchID int64
	go func() {
		defer wg.Done()

		if normalizedQuery == "" || len(stockLocationIDs) <= 0 || isSynthetic || isSND {
			return
		}

		total := int(indexSearchResponse.Meta.TotalCount)

		boostedProductTypes := &strings.Builder{}
		i := 0
		for k := range recommendedPtys {
			if i > 0 {
				boostedProductTypes.WriteRune(',')
			}

			boostedProductTypes.WriteString(k)
			i++
		}

		boostedSKUs := &strings.Builder{}
		i = 0
		for k := range productBoosts {
			if i > 0 {
				boostedSKUs.WriteRune(',')
			}

			boostedSKUs.WriteString(k)
			i++
		}

		pTypeLeaves := &strings.Builder{}
		for i, p := range pTypeLeafIDs {
			if i > 0 {
				pTypeLeaves.WriteRune(',')
			}
			pTypeLeaves.WriteString(p)
		}

		b := []byte("")
		if len(searchIntentionAttributes.AttributeCombinations) > 0 {
			b, err = json.Marshal(searchIntentionAttributes.AttributeCombinations)
			if err != nil {
				log.ForContext(ctx, "handler", "search").Error(err)
			}
		}

		searchIDOptions := []datastore.SearchIDOption{
			datastore.WithSearchJoyQuery(in.Query),
			datastore.WithSearchJoyNormalizedQuery(normalizedQuery),
			datastore.WithSearchJoyCountryISO(in.CountryCode),
			datastore.WithSearchJoyLocale(locale),
			datastore.WithSearchJoyStockLocationID(int(stockLocationIDs[0])),
			datastore.WithSearchJoyUserID(userID),
			datastore.WithSearchJoyTotalFound(total),
			datastore.WithSearchJoyExternalSearchID(indexSearchResponse.Meta.SearchID),
			datastore.WithSearchJoyProperty("channel", channel),
			datastore.WithSearchJoyProperty("enhanced_query", syn),
			datastore.WithSearchJoyProperty("enhanced_query_terms", strings.Join(terms, ",")),
			datastore.WithSearchJoyProperty("stemmed_query_terms", strings.Join(stemmedQueries, ",")),
			datastore.WithSearchJoyProperty("valid_synonym", strconv.FormatBool(validSynonym)),
			datastore.WithSearchJoyProperty("static_synonym", strconv.FormatBool(staticSynonym)),
			datastore.WithSearchJoyProperty("transformed_query", strconv.FormatBool(queryTransformed)),
			datastore.WithSearchJoyProperty("appsearch_query", strconv.FormatBool(!validSynonym && !staticSynonym && !queryTransformed)),
			datastore.WithSearchJoyProperty("page", strconv.FormatInt(page, 10)),
			datastore.WithSearchJoyProperty("product_type_id", boostedProductTypes.String()),
			datastore.WithSearchJoyProperty("user_history_product", boostedSKUs.String()),
			datastore.WithSearchJoyProperty("search_variant", searchVariant),
			datastore.WithSearchJoyProperty("product_type_leaves", pTypeLeaves.String()),
			datastore.WithSearchJoyProperty("original_normalized_query", normalizedQuery),
			datastore.WithSearchJoyProperty("corrected_normalized_query", correctedQuery),
			datastore.WithSearchJoyProperty("translated_query", translatedQuery),
			datastore.WithSearchJoyProperty("trending", strconv.FormatBool(len(trendingProductIDs) > 0)),
			datastore.WithSearchJoyProperty("trending_product_ids", strings.Join(trendingProductIDs, ",")),
			datastore.WithSearchJoyProperty("sintent_processed_term", searchIntentionAttributes.ProcessedTerm),
			datastore.WithSearchJoyProperty("sintent_collection_used", searchIntentionAttributes.CollectionUsed),
			datastore.WithSearchJoyProperty("sintent_attributes", string(b)),
			datastore.WithSearchJoyProperty("session_id", sessionID),
			datastore.WithSearchJoyProperty("query_id", queryID),
		}
		if len(taxonIDs) > 0 {
			searchIDOptions = append(searchIDOptions, datastore.WithSearchJoyProperty("taxon_ids", str.MaybeString(taxonIDs[0]).String()))
		} else {
			searchIDOptions = append(searchIDOptions, datastore.WithSearchJoyProperty("taxon_ids", ""))
		}

		spreeSearchID, err := datastore.CreateSearchID(ctx, searchIDOptions...)
		if err != nil {
			log.ForContext(ctx, "handler", "search").Error(err)
			return
		}

		searchID = spreeSearchID
	}()

	productsPromotions := map[string]*promotion.Promotion{}
	go func() {
		defer wg.Done()

		pTemp, err := promotion.Get(ctx, productProperties, locale)
		if err != nil {
			log.ForContext(ctx, "handler", "search").Error(err)
		} else {
			productsPromotions = pTemp
		}
	}()

	var isMPQEligible bool
	go func() {
		defer wg.Done()

		isMPQEligible = aloha.IsMpqEligible(ctx, orderChannel, clientVersion)
	}()

	filterWG.Wait()
	wg.Wait()

	go func() {
		if s.disableSync || len(stockLocationIDs) <= 0 || isSynthetic || isSND {
			return
		}

		err := pricingSync.Sync(ctx, indexSearchResponse, productProperties, productsPrices, channel, in.CountryCode)
		if err != nil {
			log.ForContext(ctx, "handler", "search").Error(err)
		}
	}()

	out, err := response.Map(
		ctx,
		productProperties,
		response.RootOptionChain(
			response.WithMetaAndPageLimiter(indexSearchResponse.Meta, s.pageLimiter, s.version),
			response.WithNormalizedQuery(normalizedQuery),
			response.WithSearchID(searchID),
			response.WithTaxonIDsFilter(filters.GetTaxons()),
			response.WithBrandsFilter(filters.GetBrands()),
			response.WithPromotionTypesFilter(filters.GetPromotionTypes()),
			response.WithQuery(normalizedQuery, correctedQuery),
			response.WithSessionID(sessionID),
			response.WithQueryID(queryID),
			response.WithAbMarkup(abMarkup),
		),
		response.ProductOptionChain(
			response.WithNamingAndDescription(locale),
			response.WithBrandNaming(locale),
			response.WithCategories(locale),
			response.WithVendor(locale),
			response.WithAlohaPricing(productsPrices, country.GetCurrency(), in.CountryCode, locale, isMPQEligible, in.DisableLimitedStockQuantity),
			response.WithPromotionLabels(productsPromotions),
			response.WithStorePriceIncluded(isSND),
			response.WithSearchScore(productSearchScore),
			response.WithRawPopularity(productRawPopularity),
			response.WithTranslatedIDs(translatedIDs),
			response.WithGlobalIDs(isSynthetic),
			response.WithProductMOQ(productsPrices, userType, maxMOQ, maxMOQHC, in.CountryCode, in.DisableLimitedStockQuantity),
			response.WithPromotedSKU(promotedSKUs),
		),
	)
	if err != nil {
		return nil, grpc.ErrorFrom(ctx, err)
	}

	return out, nil
}

type option func(s *SearchService)

func New(opts ...option) rpc.SearchServiceServer {
	ssrv := &SearchService{
		disableSync:                true,
		pageLimiter:                100,
		taxonFilterMinimumScore:    2.0,
		bestDealsProductLimit:      1020,
		promotionTypeFilterChannel: []string{"android", "ios"},
		rawPopularityBoostScore:    0.5,
	}
	for _, apply := range opts {
		apply(ssrv)
	}

	return ssrv
}

func WithElasticsearchClient(client index.Client) option {
	return func(s *SearchService) {
		s.elasticsearchClient = client
	}
}

func WithPQClient(pq index.Client) option {
	return func(s *SearchService) {
		s.pqClient = pq
	}
}

func WithSuggesterShards(suggesters index.SuggesterShards) option {
	return func(s *SearchService) {
		s.suggesterShards = suggesters
	}
}

func WithDisableSync(disableSync bool) option {
	return func(s *SearchService) {
		s.disableSync = disableSync
	}
}

func WithDisableSearchSuggestion(disableSearchSuggestion bool) option {
	return func(s *SearchService) {
		s.disableSearchSuggestion = disableSearchSuggestion
	}
}

func WithDisableOrion(disableOrion bool) option {
	return func(s *SearchService) {
		s.disableOrion = disableOrion
	}
}

func WithPageLimiter(pageLimiter int64) option {
	return func(s *SearchService) {
		s.pageLimiter = pageLimiter
	}
}

func WithTaxonFilterMinimumScore(minimumScore float64) option {
	return func(s *SearchService) {
		s.taxonFilterMinimumScore = minimumScore
	}
}

func WithBestDealsProductLimit(limit int64) option {
	return func(s *SearchService) {
		s.bestDealsProductLimit = limit
	}
}

func WithTaxonNoneFilter(taxonIDs []int64) option {
	return func(s *SearchService) {
		s.taxonNoneFilter = taxonIDs

		taxonIDsMap := map[int64]struct{}{}
		for _, taxonID := range taxonIDs {
			taxonIDsMap[taxonID] = struct{}{}
		}
		s.taxonNoneFilterMap = taxonIDsMap
	}
}

func WithRawPopularityBoostScore(score float64) option {
	return func(s *SearchService) {
		s.rawPopularityBoostScore = score
	}
}

func WithIDConverter(idConv idconv.IDConv) option {
	return func(s *SearchService) {
		s.idConverter = idConv
	}
}

func WithCatalogClient(catalogClient catalog.Client) option {
	return func(s *SearchService) {
		s.catalogClient = catalogClient
	}
}

func WithMaxGlobalSLI(maxGlobalSLI int64) option {
	return func(s *SearchService) {
		s.maxGlobalSLI = maxGlobalSLI
	}
}

func WithMaxGlobalProduct(maxGlobalProduct int64) option {
	return func(s *SearchService) {
		s.maxGlobalProduct = maxGlobalProduct
	}
}

func WithOrionClient(orionClient *orionRPCLib.Orion) option {
	return func(s *SearchService) {
		s.orionClient = orionClient
	}
}

func WithHubbleClient(hubbleClient *hubble.Client) option {
	return func(s *SearchService) {
		s.hubbleClient = hubbleClient
	}
}

func WithDefaultMaxMOQ(moq int64) option {
	return func(s *SearchService) {
		s.defaultMaxMOQ = moq
	}
}

func WithDefaultMaxMOQHC(moq int64) option {
	return func(s *SearchService) {
		s.defaultMaxMOQHC = moq
	}
}

func WithESSynonym(adapters synonym.Adapters) option {
	return func(s *SearchService) {
		s.esSynonymAdapters = adapters
	}
}

func WithEnableESSynonym(enableESSynonym bool) option {
	return func(s *SearchService) {
		s.enableESSynonym = enableESSynonym
	}
}

func WithEnableGlobalPriceMap(enableGlobalPriceMap map[string]bool) option {
	return func(s *SearchService) {
		s.enableGlobalPriceMap = enableGlobalPriceMap
	}
}

func WithTranslator(translator translation.Translator) option {
	return func(s *SearchService) {
		s.translator = translator
	}
}

func WithVersion(version string) option {
	return func(s *SearchService) {
		s.version = version
	}
}

func WithTrendingBoostWeight(weight float64) option {
	return func(s *SearchService) {
		s.trendingBoostWeight = weight
	}
}

func WithTrendingBoostActivePeriod(activePeriod int64) option {
	return func(s *SearchService) {
		s.trendingBoostActivePeriod = activePeriod
	}
}

func WithReplacementService(r replacement.ReplacementService) option {
	return func(s *SearchService) {
		s.replacementService = r
	}
}

func WithNullRecommendationService(n nullrecommendation.NullRecommendationService) option {
	return func(s *SearchService) {
		s.nullRecommendationService = n
	}
}
