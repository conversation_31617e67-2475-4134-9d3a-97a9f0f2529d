package middleware

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestString(t *testing.T) {
	assert := assert.New(t)

	contextName := "ctxName"
	expectation := "api/middleware:" + contextName

	key := contextKey{contextName}

	assert.Equal(expectation, key.String())
}

func TestContext(t *testing.T) {
	for _, tc := range []struct {
		name            string
		ctxs            []context.Context
		keyValues       []map[string]interface{}
		expectations    []map[string]interface{}
		strExpectations []map[string]string
	}{
		{
			"Test same key different ctx",
			[]context.Context{context.Background(), context.Background()},
			[]map[string]interface{}{{"akey": "apktntaj"}, {"akey": "apktntajd"}},
			[]map[string]interface{}{{"akey": "apktntaj"}, {"akey": "apktntajd"}},
			[]map[string]string{{"akey": "apktntaj"}, {"akey": "apktntajd"}},
		},
		{
			"Test multi keys",
			[]context.Context{context.Background()},
			[]map[string]interface{}{{"akey": "apktntaj", "bkey": "apajd"}},
			[]map[string]interface{}{{"akey": "apktntaj", "bkey": "apajd", "ckey": nil}},
			[]map[string]string{{"akey": "apktntaj", "bkey": "apajd", "dkey": ""}},
		},
		{
			"Test invalid value type",
			[]context.Context{context.Background()},
			[]map[string]interface{}{{"akey": nil}},
			[]map[string]interface{}{{"akey": nil}},
			[]map[string]string{{"akey": ""}},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			assert := assert.New(t)

			nCtxs := []context.Context{}
			for i, ctx := range tc.ctxs {
				for k, v := range tc.keyValues[i] {
					ctx = NewContext(ctx, k, v)
				}
				nCtxs = append(nCtxs, ctx)
			}

			for i, ctx := range nCtxs {
				for k, expectation := range tc.expectations[i] {
					reality, _ := FromContext(ctx, k)
					assert.Equal(expectation, reality)
				}
			}

			for i, ctx := range nCtxs {
				for k, expectation := range tc.strExpectations[i] {
					reality, _ := FromContextString(ctx, k)
					assert.Equal(expectation, reality)
				}
			}
		})
	}
}
