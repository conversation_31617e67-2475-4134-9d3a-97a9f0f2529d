package middleware

import (
	"net/http"
	"sync"

	"happyfresh.io/lib/log"
	"happyfresh.io/search/core/idconv"
)

var (
	keyFilterProductIDs     string = "filter[product_id][]"
	keyFilterVariantIDs     string = "filter[variant_id][]"
	keyFilterBrandIDs       string = "filter[brand_id][]"
	keyFilterProductTypeIDs string = "filter[product_type_id][]"
)

func GlobalID(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		wg := &sync.WaitGroup{}
		wg.Add(4)

		q := r.URL.Query()
		ctx := r.Context()

		var productIDs []string
		var variantIDs []string
		var brandIDs []string
		var productTypeIDs []string
		go func() {
			defer wg.Done()

			pIDs, err := idconv.ProductToGlobal(r.Context(), q[keyFilterProductIDs])
			if err != nil {
				log.For("GlobalID", "Product").Error(err)
				return
			}

			productIDs = pIDs
		}()

		go func() {
			defer wg.Done()

			var vIDs []string
			ids, _ := FromContext(ctx, keyFilterVariantIDs)
			vIDs, ok := ids.([]string)
			if !ok || len(vIDs) == 0 {
				vIDs = q[keyFilterVariantIDs]
			}

			vIDs, err := idconv.VariantToGlobal(r.Context(), vIDs)
			if err != nil {
				log.For("GlobalID", "Variant").Error(err)
				return
			}

			variantIDs = vIDs
		}()

		go func() {
			defer wg.Done()

			bIDs, err := idconv.BrandToGlobal(r.Context(), q[keyFilterBrandIDs])
			if err != nil {
				log.For("search", "Brand").Error(err)
				return
			}

			brandIDs = bIDs
		}()

		go func() {
			defer wg.Done()

			ptIDs, err := idconv.ProductTypeToGlobal(r.Context(), q[keyFilterProductTypeIDs])
			if err != nil {
				log.For("search", "ProductType").Error(err)
				return
			}

			productTypeIDs = ptIDs
		}()

		wg.Wait()

		ctx = NewContext(ctx, keyFilterProductIDs, productIDs)
		ctx = NewContext(ctx, keyFilterVariantIDs, variantIDs)
		ctx = NewContext(ctx, keyFilterBrandIDs, brandIDs)
		ctx = NewContext(ctx, keyFilterProductTypeIDs, productTypeIDs)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
