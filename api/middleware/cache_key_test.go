package middleware

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCacheKey(t *testing.T) {
	testCases := []struct {
		name             string
		expectedCacheKey string
	}{
		{
			"With country code header",
			"hubble:supplier:1",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				assert := assert.New(t)

				cacheKeyFromCtx, _ := FromContextString(r.Context(), "cache_key")
				assert.Equal(testCase.expectedCacheKey, cacheKeyFromCtx)
			})

			handlerToTest := CacheKey(nextHandler)
			reader := strings.NewReader(fmt.Sprintf("cache_key=%s", testCase.expectedCacheKey))
			testRequest := httptest.NewRequest("POST", "http://test.happyfresh.com", reader)
			testRequest.Header.Add("Content-Type", "application/x-www-form-urlencoded")

			handlerToTest.ServeHTTP(httptest.NewRecorder(), testRequest)
		})
	}
}
