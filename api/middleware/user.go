package middleware

import (
	"net/http"
	"strconv"
	"strings"

	"gopkg.in/square/go-jose.v2/jwt"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/datastore"
)

type claim struct {
	TrackingID string `json:"tracking_id"`
}

// UserID get User ID from jwt or spree token and put it on context
func UserID(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		userID := func() int64 {
			bearer := str.String(r.Header.Get("authorization")).String()

			split := strings.Split(bearer, " ")
			if len(split) > 1 && split[0] == "Bearer" {

				token, err := jwt.ParseSigned(split[1])
				if err == nil {

					var claim claim
					err := token.UnsafeClaimsWithoutVerification(&claim)
					if err == nil {

						userID, err := strconv.ParseInt(claim.TrackingID, 10, 64)
						if err == nil {

							return userID
						}
					}
				}
			}

			ut := r.Header.Get("x-spree-token")
			user, _ := datastore.UserID(r.Context(), ut)
			return user.GetId()
		}()

		if userID == 0 {
			userID = -1
		}

		ctx := NewContext(r.Context(), "user_ids", userID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
