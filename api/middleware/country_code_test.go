package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCountryCode(t *testing.T) {
	testCases := []struct {
		name                  string
		headerCountryCode     string
		filterStockLocationID string
		expectedCountryCode   string
	}{
		{
			"With country code header",
			"MY",
			"",
			"ID",
		},
		{
			"With query params filter stock location id",
			"",
			"3",
			"ID",
		},
		{
			"With no information",
			"",
			"",
			"ID",
		},
		{
			"With invalid query params filter stock location id",
			"",
			"RMPI",
			"ID",
		},
		{
			"With unrecognized query params filter stock location id",
			"",
			"1111111111",
			"ID",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				assert := assert.New(t)

				countryCodeFromCtx, _ := FromContext(r.Context(), "countryCode")
				assert.Equal(testCase.expectedCountryCode, countryCodeFromCtx)
			})

			handlerToTest := CountryCode(nextHandler)
			testRequest := httptest.NewRequest("POST", "http://test.happyfresh.com", nil)
			testRequest.Header.Add("X-Country-Code", testCase.headerCountryCode)

			urlWithQuery := testRequest.URL.Query()
			urlWithQuery.Add("filter[stock_location_id]", testCase.filterStockLocationID)
			testRequest.URL.RawQuery = urlWithQuery.Encode()

			handlerToTest.ServeHTTP(httptest.NewRecorder(), testRequest)
		})
	}
}
