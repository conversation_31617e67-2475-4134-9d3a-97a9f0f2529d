package middleware

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCacheMaxAge(t *testing.T) {
	clientCacheMaxAge := 21600

	nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert := assert.New(t)

		sc := w.Header().Get("Surrogate-Control")
		assert.Equal(fmt.Sprintf("max_age=%d", clientCacheMaxAge), sc)

		vary := w.Header().Get("Vary")
		assert.Equal("Accept-Encoding, Locale, X-Happy-Client-Type, X-Happy-Client-Version, Origin", vary)

		cc := w.<PERSON>er().Get("Cache-Control")
		assert.Equal(fmt.Sprintf("max_age=%d, public", clientCacheMaxAge), cc)
	})

	handlerToTest := CacheMaxAge(nextHandler)
	testRequest := httptest.NewRequest("POST", "http://test.happyfresh.com", nil)
	handlerToTest.ServeHTTP(httptest.NewRecorder(), testRequest)
}
