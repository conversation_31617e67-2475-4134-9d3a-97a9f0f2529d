package middleware

import (
	"fmt"
	"net/http"
	"time"

	chi "github.com/go-chi/chi/v5/middleware"
	"github.com/sirupsen/logrus"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
)

func HFChiLogger() func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ww := chi.NewWrapResponseWriter(w, r.ProtoMajor)

			fields := logrus.Fields{
				"remote_addr": r.Remote<PERSON>ddr,
				"user_agent":  r.<PERSON>(),
				"proto":       r.<PERSON>,
				"req_method":  r.Method,
				"req_path":    r.URL.Path,
			}

			fields["req_country"] = str.String(r.Header.Get("Country")).EmptyOrDefault("country")
			fields["req_locale"] = str.String(r.<PERSON><PERSON>.Get("Locale")).EmptyOrDefault("locale")

			requestID := chi.GetReqID(r.Context())
			if requestID != "" {
				fields["req_id"] = requestID
			}

			for k, v := range r.URL.Query() {
				if len(v) <= 0 {
					continue
				}

				fields[fmt.Sprintf("req_%s", k)] = v
			}

			start := time.Now()
			defer func() {
				fields["took"] = time.Since(start).Nanoseconds() / 1000000
				fields["res_status"] = ww.Status()
				fields["res_len"] = ww.BytesWritten()
				fields["res_h_appsearch_req_id"] = w.Header().Get("X-Request-Id")

				log.WithContext(r.Context()).WithFields(fields).Info()
			}()

			next.ServeHTTP(ww, r)
		})
	}
}
