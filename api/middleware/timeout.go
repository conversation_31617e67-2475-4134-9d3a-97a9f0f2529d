package middleware

import (
	"context"
	"net/http"
	"time"
)

func Timeout(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		r = r.WithContext(ctx)
		next.ServeHTTP(w, r)

		if ctx.Err() != nil {
			if ctx.Err() == context.Canceled {
				w.WriteHeader(http.StatusBadRequest)
			} else if ctx.Err() == context.DeadlineExceeded {
				w.WriteHeader(http.StatusServiceUnavailable)
			}
		}
	})
}
