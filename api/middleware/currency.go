package middleware

import (
	"net/http"
	"strconv"
	"strings"

	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/datastore"
)

func Currency(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Country Code is Hardcoded
		// Context: https://happyfresh-tech.slack.com/archives/C0133FD5LN9/p1696918682513539?thread_ts=1696911546.769799&cid=C0133FD5LN9

		countryCode := "ID"
		var currency string
		if len(countryCode) < 1 {
			s := str.String(
				r.URL.Query().Get("filter[stock_location_id]"),
			).EmptyOrDefault(
				r.PostFormValue("filter[stock_location_id]"),
			)

			if len(s) < 1 {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal", "currencyContext").Info("Cannot Resolve Country.")
				return
			}

			sID, err := strconv.Atoi(s.String())
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal", "currencyContext").Infof("Invalid Stock Location ID: %s", s.String())
				return
			}

			country, err := datastore.CountryByStockLocationID(r.Context(), sID)
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal", "currencyContext").Infof("Couldn't find country: %s", err)
				return
			}

			currency = country.GetCurrency()
		} else {
			country, err := datastore.CountryByISO(r.Context(), strings.ToUpper(countryCode))
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal", "currencyContext").Infof("Couldn't find country: %s", err)
				return
			}

			currency = country.GetCurrency()
		}

		if len(currency) < 1 {
			w.WriteHeader(http.StatusBadRequest)
			log.For("router/internal", "currencyContext").Info("Invalid currency")
			return
		}

		ctx = NewContext(ctx, "currency", currency)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
