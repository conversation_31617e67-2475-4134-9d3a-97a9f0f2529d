package legacy

import (
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/datastore"
)

func CountryCode(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		countryCode := str.String(r.Header.Get("Country")).String()

		if len(countryCode) < 1 {
			stockLocationID := chi.URLParam(r, "stockLocationID")
			sID, err := strconv.Atoi(stockLocationID)
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal/legacy", "CountryCode").Error(err)
				return
			}

			stockLocation, err := datastore.StockLocationByID(r.Context(), sID)
			if err != nil {
				w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusBadRequest)
				log.For("router/internal/legacy", "CountryCode").Error(err)
				return
			}

			countryCode = stockLocation.CountryCode
		}

		if len(countryCode) < 1 {
			w.WriteHeader(http.StatusBadRequest)
			log.For("router/internal/legacy", "CountryCode").Info("Cannot Resolve Country.")
			return
		}

		r.Header.Add("x-country-code", countryCode)
		next.ServeHTTP(w, r)
	})
}
