package legacy

import (
	"net/http"

	"github.com/go-chi/chi/v5"
)

func TaxonID(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		var taxonIDs []string
		taxonID := chi.URLParam(r, "taxonID")

		if taxonID != "" {
			taxonIDs = append(taxonIDs, taxonID)
		}
		if len(taxonIDs) == 0 {
			taxonIDs = r.URL.Query()["taxon_id[]"]
		}
		if len(taxonIDs) == 0 {
			taxonIDs = r.URL.Query()["taxon_id"]
		}
		if len(taxonIDs) == 0 {
			taxonIDs = r.PostForm["taxon_id[]"]
		}
		if len(taxonIDs) == 0 {
			taxonIDs = r.PostForm["taxon_id"]
		}

		query := r.URL.Query()
		query["filter[taxon_id][]"] = taxonIDs
		r.URL.RawQuery = query.Encode()

		next.ServeHTTP(w, r)
	})
}
