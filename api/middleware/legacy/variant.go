package legacy

import (
	"net/http"

	"happyfresh.io/lib/log"
	"happyfresh.io/search/api/middleware"
)

func Variant(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		err := r.ParseForm()
		if err != nil {
			log.For("router/internal", "variantContext").Error(err)
		}

		variantIDs := r.URL.Query()["variant_id[]"]

		// Return empty result if there's not any variant_id passed
		if len(variantIDs) == 0 {
			variantIDs = r.PostForm["variant_id[]"]
		}

		if len(variantIDs) == 0 {
			variantIDs = []string{"0"}
		}

		// TODO: Uncomment this code if there is performance issues
		// in endpoints that implement this middleware due to number of variant_id[]
		//
		if len(variantIDs) > 50 {
			w.WriteHeader(http.StatusBadRequest)
			log.For("router/internal", "variantContext").Infof("Variant IDs size should less than 50")
			return
		}

		ctx := middleware.NewContext(r.Context(), "filter[variant_id][]", variantIDs)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
