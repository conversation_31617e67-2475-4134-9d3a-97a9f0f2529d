package legacy

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/datastore"
)

func ProductType(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		productType := r.URL.Query()["product_type[name]"]
		if len(productType) == 0 {
			productType = r.PostForm["product_type[name]"]
		}

		query := r.URL.Query()
		query["filter[product_type][]"] = productType
		r.URL.RawQuery = query.Encode()

		next.ServeHTTP(w, r)
	})
}

func ProductTypeFromSKU(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		locale := str.MaybeString(r.Header.Get("locale")).EmptyOrDefault(r.Header.Get("Locale")).String()
		if len(locale) == 0 {
			w.WriteHeader(http.StatusBadRequest)
			log.For("router/internal/api", "ProductTypeFromSKU").Info("Cannot Resolve Locale")
			return
		}

		query := r.URL.Query()

		sku := chi.URLParam(r, "SKU")
		productType, err := datastore.ProductTypeBySKU(r.Context(), sku, locale)
		if err != nil {
			query.Add("filter[product_type_id][]", str.MaybeString(-1).String())
			r.URL.RawQuery = query.Encode()
			next.ServeHTTP(w, r)
			return
		}

		query.Add("filter[product_type_id][]", str.MaybeString(float64(productType.Id)).String())
		query.Add("filter[product_type_id][]", str.MaybeString(float64(productType.ParentId)).String())
		r.URL.RawQuery = query.Encode()
		next.ServeHTTP(w, r)
	})
}

func ProductTypeFromProductID(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		locale := str.MaybeString(r.Header.Get("locale")).EmptyOrDefault(r.Header.Get("Locale")).String()
		if len(locale) == 0 {
			w.WriteHeader(http.StatusBadRequest)
			log.For("router/internal/api", "ProductTypeFromSKU").Info("Cannot Resolve Locale")
			return
		}

		query := r.URL.Query()

		productID := str.MaybeString(chi.URLParam(r, "productID")).Int()
		productType, err := datastore.ProductTypeByProductID(r.Context(), productID, locale)
		if err != nil {
			query.Add("filter[product_type_id][]", str.MaybeString(-1).String())
			r.URL.RawQuery = query.Encode()
			next.ServeHTTP(w, r)
			return
		}

		query.Add("filter[product_type_id][]", str.MaybeString(float64(productType.Id)).String())
		query.Add("filter[product_type_id][]", str.MaybeString(float64(productType.ParentId)).String())
		r.URL.RawQuery = query.Encode()
		next.ServeHTTP(w, r)
	})
}
