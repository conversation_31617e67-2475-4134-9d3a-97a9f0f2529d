package legacy

import (
	"net/http"
	"strings"
)

func BrandID(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		brandIDs := []string{}
		brandIDstring := r.URL.Query().Get("brand_id[]")
		if brandIDstring != "" {
			brandIDs = strings.Split(brandIDstring, ",")
		}

		if len(brandIDs) == 0 {
			brandIDs = r.PostForm["brand_id[]"]
		}

		query := r.URL.Query()
		query["filter[brand_id][]"] = brandIDs
		r.URL.RawQuery = query.Encode()

		next.ServeHTTP(w, r)
	})
}
