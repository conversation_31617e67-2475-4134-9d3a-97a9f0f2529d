package legacy

import (
	"net/http"

	"happyfresh.io/lib/str"
)

func Page(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		page := r.URL.Query().Get("page")
		perPage := str.MaybeString(
			r.URL.Query().Get("per_page"),
		).EmptyOrDefault(
			r.URL.Query().Get("perPage"),
		).String()

		query := r.URL.Query()
		query.Set("page[current]", page)
		query.Set("page[size]", perPage)
		r.URL.RawQuery = query.Encode()

		next.ServeHTTP(w, r)
	})
}
