package legacy

import (
	"net/http"

	"happyfresh.io/lib/str"
)

func Channel(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		c := str.String(
			r.Header.Get("x-spree-client-type"),
		).EmptyOrDefault(
			r.Header.Get("X-Spree-Client-Type"),
		).EmptyOrDefault(
			r.Header.Get("x-happy-client-type"),
		).EmptyOrDefault(
			r.Header.Get("X-Happy-Client-Type"),
		).String()

		r.Header.Add("x-channel", c)

		next.ServeHTTP(w, r)
	})
}
