package legacy

import (
	"net/http"
	"strconv"

	"github.com/pkg/errors"

	"github.com/go-chi/chi/v5"
	"happyfresh.io/lib/log"
)

func ProductID(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		productID := chi.URLParam(r, "productID")
		pID := 0

		if productID != "" {
			pid, err := strconv.Atoi(productID)
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal/legacy", "ProductID").Error(err)
				return
			}

			if pid <= 0 {
				w.WriteHeader(http.StatusInternalServerError)
				log.For("router/internal/legacy", "ProductID").Error(errors.New("Invalid / null product ID"))
				return
			}

			pID = pid
		}

		query := r.URL.Query()
		query.Set("filter[product_id][]", strconv.FormatInt(int64(pID), 10))
		r.URL.RawQuery = query.Encode()

		next.ServeHTTP(w, r)
	})
}
