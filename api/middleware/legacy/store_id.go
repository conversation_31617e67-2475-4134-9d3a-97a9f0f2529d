package legacy

import (
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/core/datastore"
)

func StoreID(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		stockLocationID := chi.URLParam(r, "stockLocationID")

		sID, err := strconv.Atoi(stockLocationID)
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			log.For("router/internal/legacy", "StoreID").Error(err)
			return
		}

		stockLocation, err := datastore.StockLocationByID(r.Context(), sID)
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			log.For("router/internal/legacy", "StoreID").Error(err)
			return
		}

		query := r.URL.Query()
		query.Set("filter[store_id][]", strconv.FormatInt(stockLocation.StoreId, 10))
		query.Set("filter[stock_location_id]", stockLocationID)
		r.URL.RawQuery = query.Encode()

		next.ServeHTTP(w, r)
	})
}
