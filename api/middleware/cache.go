package middleware

import (
	"fmt"
	"net/http"
)

func CacheMaxAge(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		clientCacheMaxAge := 21600
		w.<PERSON>er().Set("Surrogate-Control", fmt.Sprintf("max_age=%d", clientCacheMaxAge))
		w.<PERSON><PERSON>().Set("Vary", "Accept-Encoding, Locale, X-Happy-Client-Type, X-Happy-Client-Version, Origin")
		w.<PERSON><PERSON>().Set("Cache-Control", fmt.Sprintf("max_age=%d, public", clientCacheMaxAge))
		next.ServeHTTP(w, r)
	})
}
