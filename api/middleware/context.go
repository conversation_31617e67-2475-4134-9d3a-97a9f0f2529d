package middleware

import (
	"context"
	"sync"

	"github.com/pkg/errors"
)

var (
	contextKeys = make(map[string]*contextKey)
	coLock      sync.RWMutex
)

type contextKey struct {
	Name string
}

func (c *contextKey) String() string {
	return "api/middleware:" + c.Name
}

func NewContext(ctx context.Context, key string, value interface{}) context.Context {
	coLock.Lock()
	cKey, ok := contextKeys[key]
	if !ok {
		cKey = &contextKey{key}
		contextKeys[key] = cKey
	}
	coLock.Unlock()

	ctx = context.WithValue(ctx, cKey, value)

	return ctx
}

func FromContext(ctx context.Context, key string) (interface{}, error) {
	coLock.RLock()
	cKey, ok := contextKeys[key]
	coLock.RUnlock()
	if !ok {
		err := errors.Errorf("Key %s does not exist", key)
		return nil, err
	}

	v := ctx.Value(cKey)
	if v == nil {
		err := errors.New("Invalid value type")
		return nil, err
	}

	return v, nil
}

func FromContextString(ctx context.Context, key string) (string, error) {
	coLock.RLock()
	cKey, ok := contextKeys[key]
	coLock.RUnlock()
	if !ok {
		err := errors.Errorf("Key %s does not exist", key)
		return "", err
	}

	v, ok := ctx.Value(cKey).(string)
	if !ok {
		err := errors.New("Invalid value type")
		return "", err
	}

	return v, nil
}
