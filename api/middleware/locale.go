package middleware

import (
	"net/http"

	"golang.org/x/text/language"
	"happyfresh.io/lib/str"
)

var (
	localeMatcher = language.NewMatcher([]language.Tag{
		language.English,
		language.Indonesian,
		language.Thai,
		language.Malay,
	})
)

func Locale(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		l := str.String(r.Header.Get("x-locale")).String()

		t, _, _ := localeMatcher.Match(language.Make(l))
		b, _ := t.Base()

		ctx := NewContext(r.Context(), "locale", b.String())
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
