package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCurrency(t *testing.T) {
	testCases := []struct {
		name                  string
		headerCountryCode     string
		filterStockLocationID string
		expectedCurrency      string
	}{
		{
			"With country code header",
			"MY",
			"",
			"IDR",
		},
		{
			"With query params filter stock location id",
			"",
			"3",
			"IDR",
		},
		{
			"With no information",
			"",
			"",
			"IDR",
		},
		{
			"With invalid query params filter stock location id",
			"",
			"RMPI",
			"IDR",
		},
		{
			"With unrecognized query params filter stock location id",
			"",
			"1",
			"IDR",
		},
		{
			"With unrecognized country code",
			"FR",
			"",
			"IDR",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				assert := assert.New(t)

				currencyFromCtx, _ := FromContext(r.Context(), "currency")
				assert.Equal(testCase.expectedCurrency, currencyFromCtx)
			})

			handlerToTest := Currency(nextHandler)
			testRequest := httptest.NewRequest("POST", "http://test.happyfresh.com", nil)
			testRequest.Header.Add("X-Country-Code", testCase.headerCountryCode)

			urlWithQuery := testRequest.URL.Query()
			urlWithQuery.Add("filter[stock_location_id]", testCase.filterStockLocationID)
			testRequest.URL.RawQuery = urlWithQuery.Encode()

			handlerToTest.ServeHTTP(httptest.NewRecorder(), testRequest)
		})
	}
}
