package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLocale(t *testing.T) {
	testCases := []struct {
		locale         string
		expectedLocale string
	}{
		{
			locale:         "ID",
			expectedLocale: "id",
		},
		{
			locale:         "MS",
			expectedLocale: "ms",
		},
		{
			locale:         "MY",
			expectedLocale: "en",
		},
		{
			locale:         "TH",
			expectedLocale: "th",
		},
		{
			locale:         "EN",
			expectedLocale: "en",
		},
		{
			locale:         "FR",
			expectedLocale: "en",
		},
	}

	for _, testCase := range testCases {
		nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert := assert.New(t)

			localeFromCtx, _ := FromContext(r.Context(), "locale")
			assert.Equal(testCase.expectedLocale, localeFromCtx)
		})

		handlerToTest := Locale(nextHandler)
		testRequest := httptest.NewRequest("POST", "http://test.happyfresh.com", nil)
		testRequest.Header.Add("X-Locale", testCase.locale)

		handlerToTest.ServeHTTP(httptest.NewRecorder(), testRequest)
	}
}
