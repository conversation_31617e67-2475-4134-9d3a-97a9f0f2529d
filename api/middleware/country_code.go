package middleware

import (
	"net/http"
	"strconv"

	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/datastore"
)

func CountryCode(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		// Force country code to ID
		// Context: https://happyfresh-tech.slack.com/archives/C0133FD5LN9/p1696918682513539?thread_ts=1696911546.769799&cid=C0133FD5LN9

		countryCode := "ID"
		if len(countryCode) < 1 {
			s := str.String(
				r.URL.Query().Get("filter[stock_location_id]"),
			).EmptyOrDefault(
				r.PostFormValue("filter[stock_location_id]"),
			)

			if len(s) < 1 {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal", "countryContext").Info("Cannot Resolve Country.")
				return
			}

			sID, err := strconv.Atoi(s.String())
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal", "countryContext").Infof("Invalid Stock Location ID: %s", s.String())
				return
			}

			country, err := datastore.CountryByStockLocationID(r.Context(), sID)
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal", "countryContext").Infof("Couldn't find country: %s", err)
				return
			}

			countryCode = country.GetIsoName()
			if len(countryCode) < 1 {
				w.WriteHeader(http.StatusBadRequest)
				log.For("router/internal", "countryContext").Infof("Invalid Country Code for: %d", sID)
				return
			}
		}

		ctx = NewContext(ctx, "countryCode", countryCode)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
