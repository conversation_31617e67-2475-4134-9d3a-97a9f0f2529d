package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestUserID(t *testing.T) {
	var userID int64 = 547168
	var token string = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oSz9ihZbb4EoW4Zc6N7jpaYrexJW_3QcGoeFh2D-ZO4"

	nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if userIDFromCtx, _ := FromContext(r.Context(), "user_ids"); userIDFromCtx != userID {
			t.Errorf("\nexpectation: %v\nreality: %v", userID, userIDFromCtx)
		}
	})

	handlerToTest := UserID(nextHandler)
	testRequest := httptest.NewRequest("POST", "http://test.happyfresh.com", nil)
	testRequest.Header.Add("Authorization", token)

	handlerToTest.ServeHTTP(httptest.NewRecorder(), testRequest)
}
