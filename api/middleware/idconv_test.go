package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGlobalID(t *testing.T) {
	testCases := []struct {
		filterProductIDs       []string
		filterVariantIDs       []string
		filterBrandIDs         []string
		filterProductTypeIDs   []string
		expectedProductIDs     []string
		expectedVariantIDs     []string
		expectedBrandIDs       []string
		expectedProductTypeIDs []string
	}{
		{
			filterProductIDs:       []string{"11"},
			filterVariantIDs:       []string{"11"},
			filterBrandIDs:         []string{"11"},
			filterProductTypeIDs:   []string{"11"},
			expectedProductIDs:     []string{"-1"},
			expectedVariantIDs:     []string{"-1"},
			expectedBrandIDs:       []string{"-1"},
			expectedProductTypeIDs: []string{"-1"},
		},
	}

	for _, testCase := range testCases {
		nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert := assert.New(t)

			productIDsFromCtx, _ := FromContext(r.Context(), keyFilterProductIDs)
			assert.Equal(testCase.expectedProductIDs, productIDsFromCtx)

			variantIDsFromCtx, _ := FromContext(r.Context(), keyFilterVariantIDs)
			assert.Equal(testCase.expectedVariantIDs, variantIDsFromCtx)

			brandIDsFromCtx, _ := FromContext(r.Context(), keyFilterBrandIDs)
			assert.Equal(testCase.expectedBrandIDs, brandIDsFromCtx)

			productTypeIDsFromCtx, _ := FromContext(r.Context(), keyFilterProductTypeIDs)
			assert.Equal(testCase.expectedProductTypeIDs, productTypeIDsFromCtx)
		})

		handlerToTest := GlobalID(nextHandler)
		testRequest := httptest.NewRequest("POST", "http://test.happyfresh.com", nil)

		urlWithQuery := testRequest.URL.Query()
		for _, filterProductID := range testCase.filterProductIDs {
			urlWithQuery.Add(keyFilterProductIDs, filterProductID)
		}
		for _, filterVariantID := range testCase.filterVariantIDs {
			urlWithQuery.Add(keyFilterVariantIDs, filterVariantID)
		}
		for _, filterBrandID := range testCase.filterBrandIDs {
			urlWithQuery.Add(keyFilterBrandIDs, filterBrandID)
		}
		for _, filterProductTypeID := range testCase.filterProductTypeIDs {
			urlWithQuery.Add(keyFilterProductTypeIDs, filterProductTypeID)
		}
		testRequest.URL.RawQuery = urlWithQuery.Encode()

		handlerToTest.ServeHTTP(httptest.NewRecorder(), testRequest)
	}
}
