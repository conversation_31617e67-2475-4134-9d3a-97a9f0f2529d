syntax = "proto3";

package rpc;

import "gogoproto/gogo.proto";

option go_package = "rpc";

service SearchService {
    rpc GlobalSearch(SearchRequest) returns (SearchResponse);
    rpc Search(SearchRequest) returns (SearchResponse);
    rpc Browse(SearchRequest) returns (SearchResponse);
    rpc SearchReplacement(SearchRequest) returns (SearchResponse);
    rpc BestDeals(BestDealsRequest) returns (SearchResponse);
    rpc ProductsByVariants(SearchRequest) returns (SearchResponse);
    rpc ProductsByPromotion(ProductsByPromotionRequest) returns (SearchResponse);
    rpc SuggestSearch(SearchSuggestionRequest) returns (SearchSuggestionResponse);
    rpc GlobalBrowse(SearchRequest) returns (SearchResponse);
    rpc NullRecommendation(SearchRequest) returns (SearchResponse);
    rpc BrowseTheme(SearchRequest) returns (SearchResponse);
    rpc GlobalSearchAutocomplete(SearchSuggestionRequest) returns (SearchSuggestionResponse);
}

message SearchRequest {
    string query = 1;
    repeated string stock_item_ids = 2;
    repeated int64 variant_ids = 3;
    repeated string skus = 4;
    repeated int64 taxon_ids = 5;
    repeated string product_type_ids = 6;
    repeated int64 brand_ids = 7;
    repeated int64 store_ids = 8;
    repeated int64 stock_location_ids = 9;
    bool show_oos = 10;
    string sorting = 11;
    int64 per_page = 12;
    int64 page = 13;
    string country_code = 14;
    bool show_promotion_type_filter = 15;
    int64 vendor_id = 16;
    repeated int64 pinned_variant_ids = 17;
    repeated int64 nearby_stores = 18;
    int64 theme_id = 19;
    bool disable_limited_stock_quantity = 20;
    repeated int64 promoted_variant_ids = 21;
    repeated string promoted_skus = 22;
    int64 max_promoted_product_count =  23;
    repeated int64 taxon_none_filter = 24;
}

message BestDealsRequest {
    string promotion_type = 1;
    bool show_promotion_type_filter = 2;
    repeated int64 taxon_ids = 3;
    repeated int64 brand_ids = 4;
    repeated int64 store_ids = 5;
    repeated int64 stock_location_ids = 6;
    bool show_oos = 7;
    string sorting = 8;
    int64 per_page = 9;
    int64 page = 10;
    string country_code = 11;
}

message ProductsByPromotionRequest {
    int64 promotion_id = 1;
    repeated int64 taxon_ids = 5;
    repeated int64 brand_ids = 7;
    repeated int64 store_ids = 8;
    repeated int64 stock_location_ids = 9;
    bool show_oos = 10;
    string sorting = 11;
    int64 per_page = 12;
    int64 page = 13;
    string country_code = 14;
    repeated int64 pinned_variant_ids = 15;
}

message SearchSuggestionRequest {
    string query = 1;
    repeated int64 store_ids = 2;
    repeated int64 stock_location_ids = 3;
    string country_code = 4;
}

message SearchSuggestionResponse {
    repeated string keywords = 1 [(gogoproto.jsontag) = "keywords"];
    repeated KeywordTaxon keywords_in_taxon = 2 [(gogoproto.jsontag) = "keywords_in_taxon"];
    repeated string corrections = 3 [(gogoproto.jsontag) = "corrections"];
    repeated string past_items = 4 [(gogoproto.jsontag) = "past_items"];
    string engine_version = 5;
    repeated Taxon taxons = 6 [(gogoproto.jsontag) = "taxons"];
    repeated Brand brands = 7 [(gogoproto.jsontag) = "brands"];
}

message ProductDetailResponse {
    int64 id = 1;
    string name = 2;
    string description = 3;
    bool in_stock = 4 [(gogoproto.jsontag) = "in_stock"];
    bool stock_prediction = 5 [(gogoproto.jsontag) = "stock_prediction"];
    string stock_prediction_updated_at = 6 [(gogoproto.jsontag) = "stock_prediction_updated_at"];
    string slug = 7;
    int64 max_order_quantity = 8;
    int64 max_promo_quantity = 9;
    string display_unit = 10;
    string supermarket_unit = 11;
    double natural_average_weight = 12;
    string display_average_weight = 13;
    double popularity = 14;
    repeated int64 taxon_ids = 15 [(gogoproto.jsontag) = "taxon_ids"];
    repeated string categories = 16 [(gogoproto.jsontag) = "categories"];
    string product_type_name = 17 [(gogoproto.jsontag) = "product_type_name"];
    repeated string display_banner_text = 18 [(gogoproto.jsontag) = "display_banner_text"];
    int64 quantity = 19 [(gogoproto.jsontag) = "quantity"];
    string normal_price = 20;
    string display_normal_price = 21 [(gogoproto.jsontag) = "display_normal_price"];
    string price = 22;
    string display_price = 23;
    string store_normal_price = 24;
    string display_store_normal_price = 25;
    string store_promo_price = 26;
    string display_store_promo_price = 27;
    string unit_price = 28;
    string display_unit_price = 29;
    string supermarket_unit_cost_price = 30;
    string display_supermarket_unit_cost_price = 31;
    int64 variant_id = 32;
    repeated Variant variants = 33 [(gogoproto.jsontag) = "variants"];
    Brand brand = 34;
    string display_promo_price_percentage = 35 [(gogoproto.jsontag) = "display_promo_price_percentage"];
    string display_promotion_actions_combination_text = 36 [(gogoproto.jsontag) = "display_promotion_actions_combination_text"];
    repeated PromotionAction promotion_actions = 37 [(gogoproto.jsontag) = "promotion_actions"];
    repeated Promotion promotions = 38 [(gogoproto.jsontag) = "promotions"];
    string cost = 39;
    string display_cost = 40;
    string pricing_engine = 41;
    int64 product_global_id = 42;
    int64 variant_global_id = 43;
    int64 brand_global_id = 44;
    string meta_title = 45;
    string meta_description = 46;
    string engine_version = 47;
    int64 delivered_quantity = 48;
    Vendor vendor = 49;
    int64 threshold_quantity = 50  [(gogoproto.jsontag) = "threshold_quantity"];
    bool is_limited_stock = 51  [(gogoproto.jsontag) = "is_limited_stock"];
    repeated string promotion_types = 52 [(gogoproto.jsontag) = "promotion_types"];
    string ab_markup = 53;
    string display_item_unit = 54 [(gogoproto.jsontag) = "display_item_unit"];
}

message KeywordTaxon {
    string keyword = 1;
    Taxon taxon = 2;
}

message Taxon {
    int64 id = 1;
    string name = 2 [(gogoproto.jsontag) = "name"];
}

message SearchResponse {
    int64 count = 1 [(gogoproto.jsontag) = "count"];
    int64 total_count = 2 [(gogoproto.jsontag) = "total_count"];
    int64 current_page = 3 [(gogoproto.jsontag) = "current_page"];
    int64 total_page = 4 [(gogoproto.jsontag) = "pages"];
    int64 per_page = 5 [(gogoproto.jsontag) = "per_page"];
    int64 search_id = 6 [(gogoproto.jsontag) = "search_id"];
    string external_search_id = 7 [(gogoproto.jsontag) = "-"];
    string normalized_query = 8;
    repeated Product products = 9 [(gogoproto.jsontag) = "products"];
    Filter filters = 10;
    Query query = 11;
    Meta meta = 12;
    repeated string enhancements = 13 [(gogoproto.jsontag) = "enhancements"];
}

message Meta {
    string engine_version = 1;
    bool global_price = 2;
    string session_id = 3;
    string query_id = 4;
    string recommendation_type = 5;
    string ab_markup = 6;
}

message Query {
    string before = 1;
    string after = 2;
}

message Product {
    int64 id = 1;
    string name = 2;
    string description = 3;
    bool in_stock = 4 [(gogoproto.jsontag) = "in_stock"];
    bool stock_prediction = 5 [(gogoproto.jsontag) = "stock_prediction"];
    string stock_prediction_updated_at = 6 [(gogoproto.jsontag) = "stock_prediction_updated_at"];
    string slug = 7;
    int64 max_order_quantity = 8;
    int64 max_promo_quantity = 9;
    string display_unit = 10;
    string supermarket_unit = 11;
    double natural_average_weight = 12;
    string display_average_weight = 13;
    double popularity = 14;
    repeated int64 taxon_ids = 15 [(gogoproto.jsontag) = "taxon_ids"];
    repeated string categories = 16 [(gogoproto.jsontag) = "categories"];
    string product_type_name = 17 [(gogoproto.jsontag) = "product_type_name"];
    repeated string display_banner_text = 18 [(gogoproto.jsontag) = "display_banner_text"];
    int64 quantity = 19 [(gogoproto.jsontag) = "quantity"];
    string normal_price = 20;
    string display_normal_price = 21 [(gogoproto.jsontag) = "display_normal_price"];
    string price = 22;
    string display_price = 23;
    string store_normal_price = 24;
    string display_store_normal_price = 25;
    string store_promo_price = 26;
    string display_store_promo_price = 27;
    string unit_price = 28;
    string display_unit_price = 29;
    string supermarket_unit_cost_price = 30;
    string display_supermarket_unit_cost_price = 31;
    int64 variant_id = 32;
    repeated Variant variants = 33 [(gogoproto.jsontag) = "variants"];
    Brand brand = 34;
    string display_promo_price_percentage = 35 [(gogoproto.jsontag) = "display_promo_price_percentage"];
    string display_promotion_actions_combination_text = 36 [(gogoproto.jsontag) = "display_promotion_actions_combination_text"];
    repeated PromotionAction promotion_actions = 37 [(gogoproto.jsontag) = "promotion_actions"];
    repeated Promotion promotions = 38 [(gogoproto.jsontag) = "promotions"];
    string cost = 39;
    string display_cost = 40;
    string pricing_engine = 41;
    double score = 42;
    double raw_popularity = 43;
    int64 product_global_id = 44;
    int64 variant_global_id = 45;
    int64 brand_global_id = 46;
    int64 stock_location_id = 47;
    Vendor vendor = 48;
    bool show_price = 49 [(gogoproto.jsontag) = "show_price"];
    string cost_price = 50;
    string display_cost_price = 51;
    string normal_cost_price = 52;
    string display_normal_cost_price = 53;
    int64 threshold_quantity = 54 [(gogoproto.jsontag) = "threshold_quantity"];
    bool is_limited_stock = 55 [(gogoproto.jsontag) = "is_limited_stock"];
    repeated string promotion_types = 56 [(gogoproto.jsontag) = "promotion_types"];
    bool ads = 57 [(gogoproto.jsontag) = "ads"];
    string display_item_unit = 58 [(gogoproto.jsontag) = "display_item_unit"];
}

message Variant {
    int64 id = 1;
    string name = 2;
    string description = 3;
    bool in_stock = 4 [(gogoproto.jsontag) = "in_stock"];
    bool stock_prediction = 5 [(gogoproto.jsontag) = "stock_prediction"];
    string stock_prediction_updated_at = 6 [(gogoproto.jsontag) = "stock_prediction_updated_at"];
    string slug = 7;
    string sku = 8;
    bool is_master = 9;
    bool track_inventory = 10;
    repeated Image images = 11 [(gogoproto.jsontag) = "images"];
    int64 max_promo_quantity = 12;
}

message Image {
    int64 id = 1;
    int64 position = 2;
    string alt = 3;
    string mini_url = 4 [(gogoproto.jsontag) = "mini_url"];
    string small_url = 5 [(gogoproto.jsontag) = "small_url"];
    string product_url = 6 [(gogoproto.jsontag) = "product_url"];
    string product_hq_url = 7 [(gogoproto.jsontag) = "product_hq_url"];
    string large_url = 8 [(gogoproto.jsontag) = "large_url"];
    string original_url = 9 [(gogoproto.jsontag) = "original_url"];
    string attachment_updated_at = 10;
}

message Brand {
    int64 id = 1;
    string name = 2 [(gogoproto.jsontag) = "name"];
}

message PromotionAction {
    int64 id = 1;
    int64 position = 2;
    string type = 3;
    string display_long_text = 4;
    string display_short_text = 5;
    repeated PromotionProduct products = 6 [(gogoproto.jsontag) = "products"];
}

message PromotionProduct {
    int64 id = 1;
    int64 quantity = 2;
    repeated string display_banner_text = 3 [(gogoproto.jsontag) = "display_banner_text"];
    repeated Variant variants = 4 [(gogoproto.jsontag) = "variants"];
    float discount = 5;
}

message Filter {
    repeated int64 taxon_ids = 1 [(gogoproto.jsontag) = "taxon_ids"];
    repeated Brand brands = 2 [(gogoproto.jsontag) = "brands"];
    repeated PromotionType promotion_types = 3 [(gogoproto.jsontag) = "promotion_types"];
}

message PromotionType {
    string type = 1;
    int64 total = 2;
}

message Promotion {
    int64 id = 1;
    string code = 2;
    repeated PromotionRule rules = 3 [(gogoproto.jsontag) = "rules"];
    repeated PromotionAction actions = 4 [(gogoproto.jsontag) = "actions"];
}

message PromotionRule {
    int64 id = 1;
    string type = 2;
    int64 quantity = 3;
    repeated PromotionProduct products = 4 [(gogoproto.jsontag) = "products"];
}

message Vendor {
    int64 id = 1;
    string name = 2;
    string description = 3;
    string image_url = 4;
}

message TaxonomyListResponse {
    repeated TaxonList taxons = 1;
}

message TaxonList {
    int64 id = 1; 
    string name = 2; 
    int64 parent_id = 3;
    string description = 4 [(gogoproto.jsontag) = "description"];
    string permalink = 5;
    int64 taxonomy_id = 6;
    string meta_description = 7 [(gogoproto.jsontag) = "meta_description"];
    string meta_keywords = 8 [(gogoproto.jsontag) = "meta_keywords"];
    string meta_title = 9 [(gogoproto.jsontag) = "meta_title"];
    int64 position = 10;
    TaxonListImage display_image = 11 [(gogoproto.jsontag) = "display_image"];
    int64 products_count = 12 [(gogoproto.jsontag) = "products_count"];
    string icon_url = 13;
    bool sorting_unit_price = 14 [(gogoproto.jsontag) = "sorting_unit_price"];
    message Promise {}
    repeated Promise promise = 15; // PLACEHOLDER
    string slug = 16;
    repeated TaxonList taxons = 17 [(gogoproto.jsontag) = "taxons"];
    bool is_manual_boosting = 18 [(gogoproto.jsontag) = "is_manual_boosting"];
    bool verify_age = 19 [(gogoproto.jsontag) = "verify_age"];
}

message TaxonListImage {
    int64 id = 1;
    string mini = 2;
    string normal = 3;
}

message StoreTaxon {
    int64 id = 1;
    string name = 2;
    TaxonListImage display_image = 3;
    string permalink = 4;
    bool is_manual_boosting = 5 [(gogoproto.jsontag) = "is_manual_boosting"];
}

message StoreTaxonList {
    int64 stock_location_id = 1;
    repeated StoreTaxon taxons = 2;
}

message StoreTaxonListResponse {
    repeated StoreTaxonList store_taxons = 1;
}

message ThemeListResponse {
    repeated Theme themes = 1;
}

message Theme {
    int64 theme_id = 1;
    string title = 2;
    int64 position = 3;
    string translated_title = 4;
    string image_url = 5;
}
