package request

import (
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"github.com/go-chi/chi/v5"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/api/middleware"
)

var (
	syntheticRegexp = regexp.MustCompile("(?i)(x?).*synthetic")
)

func QueryTerm(r *http.Request) string {
	return r.URL.Query().Get("q")
}

func StockItemIDs(r *http.Request) []string {
	IDs := r.URL.Query()["filter[id][]"]

	sIDs, err := middleware.FromContext(r.Context(), "product_ids")
	if sIDs == nil || err != nil {
		return IDs
	}

	stockItemIDs := make([]string, 0)
	for _, sID := range sIDs.([]string) {
		stockItemIDs = append(stockItemIDs, sID)
	}

	return append(stockItemIDs, IDs...)
}

// TODO: Handle Post Form
func VariantIDs(r *http.Request) []int64 {
	ids, _ := middleware.FromContext(r.Context(), "filter[variant_id][]")
	vIDs, _ := ids.([]string)

	variantIDs := []int64{}
	for _, id := range vIDs {
		variantID, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			continue
		}

		variantIDs = append(variantIDs, variantID)
	}

	return variantIDs
}

func PromotionID(r *http.Request) int64 {
	return str.String(chi.URLParam(r, "promotionID")).Int64()
}

func PromotionType(r *http.Request) string {
	return r.URL.Query().Get("promotion_type[name]")
}

func TaxonIDs(r *http.Request) []int64 {
	tIDs := r.URL.Query()["filter[taxon_id][]"]
	taxonIDs := make([]int64, 0, len(tIDs))
	for _, tID := range tIDs {
		taxonID, err := strconv.ParseInt(tID, 10, 64)
		if err != nil {
			continue
		}

		taxonIDs = append(taxonIDs, taxonID)
	}

	return taxonIDs
}

func ProductTypeIDs(r *http.Request) []string {
	ids, _ := middleware.FromContext(r.Context(), "filter[product_type_id][]")
	tIDs, _ := ids.([]string)
	if len(tIDs) == 0 {
		tIDs = r.URL.Query()["filter[product_type_id][]"]
	}

	return tIDs
}

func BrandIDs(r *http.Request) []int64 {
	ids, _ := middleware.FromContext(r.Context(), "filter[brand_id][]")
	bIDs, _ := ids.([]string)
	if len(bIDs) == 0 {
		bIDs = r.URL.Query()["filter[brand_id][]"]
	}

	brandIDs := make([]int64, 0, len(bIDs))
	for _, bID := range bIDs {
		brandID, err := strconv.ParseInt(bID, 10, 64)
		if err != nil {
			continue
		}

		brandIDs = append(brandIDs, brandID)
	}

	return brandIDs
}

func StoreIDs(r *http.Request) []int64 {
	sIDs := r.URL.Query()["filter[store_id][]"]
	storeIDs := make([]int64, 0, len(sIDs))
	for _, sID := range sIDs {
		storeID, err := strconv.ParseInt(sID, 10, 64)
		if err != nil {
			continue
		}

		storeIDs = append(storeIDs, storeID)
	}

	return storeIDs
}

func StockLocationIDs(r *http.Request) []int64 {
	sIDs := r.URL.Query()["filter[stock_location_id]"]
	stockLocationIDs := make([]int64, 0, len(sIDs))
	for _, sID := range sIDs {
		stockLocationID, err := strconv.ParseInt(sID, 10, 64)
		if err != nil {
			continue
		}

		stockLocationIDs = append(stockLocationIDs, stockLocationID)
	}

	return stockLocationIDs
}

func SLIs(r *http.Request) []int64 {
	sIDs := r.URL.Query()["sli[]"]
	SLIs := make([]int64, 0, len(sIDs))
	for _, sID := range sIDs {
		SLI, err := strconv.ParseInt(sID, 10, 64)
		if err != nil {
			continue
		}

		SLIs = append(SLIs, SLI)
	}

	return SLIs
}

func ProductIDs(r *http.Request) []int64 {
	ids, _ := middleware.FromContext(r.Context(), "filter[product_id][]")
	pIDs, _ := ids.([]string)
	if len(pIDs) == 0 || pIDs[0] == "-1" {
		pIDs = r.URL.Query()["filter[product_id][]"]
	}
	productIDs := make([]int64, 0, len(pIDs))
	for _, pID := range pIDs {
		productID, err := strconv.ParseInt(pID, 10, 64)
		if err != nil {
			continue
		}

		productIDs = append(productIDs, productID)
	}

	return productIDs
}

func ShowOOS(r *http.Request) bool {
	return str.String(r.URL.Query().Get("filter[show_oos]")).Bool()
}

func IsPinOOS(r *http.Request) string {
	return str.String(r.URL.Query().Get("pin_oos")).String()
}

func Sorting(r *http.Request) string {
	return str.String(r.URL.Query().Get("sort")).EmptyOrDefault("_score desc").String()
}

func PageCurrent(r *http.Request) int64 {
	current := str.String(r.URL.Query().Get("page[current]")).EmptyOrDefault("1").Int()
	if current <= 0 {
		current = 1
	}

	return int64(current)
}

func PageSize(r *http.Request) int64 {
	pageSize := str.String(r.URL.Query().Get("page[size]")).EmptyOrDefault("20").Int64()
	if pageSize > 100 {
		pageSize = 100
	}

	return pageSize
}

func CountryCode(r *http.Request) string {
	// Value is hardcoded on purpose
	// Context: Context: https://happyfresh-tech.slack.com/archives/C0133FD5LN9/p1696918682513539?thread_ts=1696911546.769799&cid=C0133FD5LN9
	return "ID"
}

func ShowPromotionFilter(r *http.Request) bool {
	return str.String(r.URL.Query().Get("promotion_filter")).Bool()
}

func VendorID(r *http.Request) int64 {
	return str.String(r.URL.Query().Get("vendor_id")).Int64()
}

func ThemeId(r *http.Request) int64 {
	return str.String(chi.URLParam(r, "themeID")).Int64()
}

func Category(r *http.Request) string {
	return r.URL.Query().Get("category")
}

func Channel(r *http.Request) string {
	return r.Header.Get("x-channel")
}

func OrderChannel(r *http.Request) string {
	if IsSND(r) {
		return r.Header.Get("x-order-client-type")
	}

	return Channel(r)
}

func Locale(r *http.Request) string {
	locale, _ := middleware.FromContextString(r.Context(), "locale")
	return locale
}

func ClientVersion(r *http.Request) string {
	return str.String(
		r.Header.Get("X-Happy-Client-Version"),
	).EmptyOrDefault(
		r.Header.Get("x-happy-client-version"),
	).EmptyOrDefault(
		"0",
	).String()
}

func DeviceID(r *http.Request) string {
	return r.Header.Get("X-DEVICE-ID")
}

func UserToken(r *http.Request) string {
	return r.Header.Get("x-spree-token")
}

func UserType(r *http.Request) string {
	return r.Header.Get("x-spree-user-type")
}

func IsSynthetic(r *http.Request) string {
	for k := range r.Header {
		if syntheticRegexp.MatchString(k) {
			return "true"
		}
	}

	return "false"
}

func RawPopularityBoostScore(r *http.Request) string {
	return r.URL.Query().Get("raw_popularity_boost_score")
}

func IsSND(r *http.Request) bool {
	return strings.ToLower(Channel(r)) == "snd"
}

// UserID get user id from context as string
func UserID(r *http.Request) string {
	userIDFromCtx, err := middleware.FromContext(r.Context(), "user_ids")
	if err != nil {
		return "-1"
	}

	userID, ok := userIDFromCtx.(int64)
	if !ok {
		return "-1"
	}

	return strconv.FormatInt(userID, 10)
}

func AnonymousID(r *http.Request) string {
	return str.String(r.Header.Get("x-anonymous-id")).String()
}

func SearchVariant(r *http.Request) string {
	return r.URL.Query().Get("search_variant")
}

func OrderNumber(r *http.Request) string {
	return r.URL.Query().Get("order_number")
}

func GlobalSearchVariant(r *http.Request) string {
	return r.URL.Query().Get("global_search_variant")
}

func NullRecommendationVariant(r *http.Request) string {
	return r.URL.Query().Get("null_recommendation_variant")
}

func StockLocationID(r *http.Request) int64 {
	return str.String(r.URL.Query().Get("stock_location_id")).Int64()
}

func TaxonomyID(r *http.Request) int64 {
	return str.String(chi.URLParam(r, "taxonomyID")).Int64()
}

func CacheKey(r *http.Request) string {
	key, _ := middleware.FromContextString(r.Context(), "cache_key")
	return key
}

func Size(r *http.Request) int64 {
	size := str.String(r.URL.Query().Get("size")).EmptyOrDefault("10").Int64()
	if size <= 0 {
		size = 10
	}

	return size
}

func TaxonID(r *http.Request) int64 {
	return str.String(r.URL.Query().Get("taxon_id")).Int64()
}

func TaxonPinMode(r *http.Request) string {
	isGlobalSearch := str.String(r.URL.Query().Get("is_global_search")).Bool()
	if isGlobalSearch {
		return "global_search"
	}
	return "store_home"
}
