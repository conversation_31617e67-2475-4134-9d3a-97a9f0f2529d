package router

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"happyfresh.io/search/api/handler/gateway"
	"happyfresh.io/search/api/middleware"
	"happyfresh.io/search/api/middleware/legacy"
)

// Default router list
func Default(m map[string]http.HandlerFunc) http.Handler {
	r := chi.NewRouter()
	rr := r.With(middleware.Timeout)
	rr.Get("/ping", gateway.Ping)
	rr.Route("/api/v3", V3)
	rr.Route("/api", Legacy)
	for k, v := range m {
		rr.HandleFunc(k, v)
	}

	return r
}

// Legacy route
// TODO:
// - Use context instead of new query param to support legacy
// - Fix all variant ID possible parameter (query param, form, etc) in non legacy middleware
func Legacy(r chi.Router) {
	r.Route("/stock_locations/{stockLocationID}", func(r chi.Router) {
		routes := r.With(
			legacy.IsLegacy,
			legacy.Channel,
			legacy.CountryCode,
			legacy.StoreID,
			legacy.TaxonID,
			legacy.ProductType,
			legacy.BrandID,
			legacy.Locale,
			legacy.ShowOOS,
			legacy.Page,
			legacy.Sort,
			legacy.ProductID,
			middleware.CountryCode,
			middleware.Currency,
			middleware.Locale,
			middleware.UserID,
		)

		rg := routes.With(middleware.GlobalID)

		rg.Route("/taxons/{taxonID}", func(r chi.Router) {
			r.Get("/products", gateway.Browse)
		})

		rg.Route("/products", func(r chi.Router) {
			r.Get("/search", gateway.Search)
			r.Get("/replacements/search", gateway.SearchReplacement)
			r.With(legacy.ProductTypeFromProductID).Get("/replacement_suggestions/{productID}", gateway.Browse)
			r.With(legacy.ProductTypeFromSKU).Get("/replacement_suggestions_by_sku/{SKU}", gateway.Browse)
			r.Get("/", gateway.PLPCategory)

			r.Get("/search_suggestions", gateway.SuggestSearch)
			r.With(legacy.PopularSuggest).Get("/search_popular_suggestions", gateway.SuggestSearch)
		})

		rg.Get("/products/{productID:[0-9]+}", gateway.ProductDetail)
		rg.Get("/promotions/{promotionID}/products", gateway.ProductsByPromotion)

		routes.With(
			legacy.Variant,
			middleware.GlobalID,
		).Route("/product_by_variants", func(r chi.Router) {
			r.Get("/", gateway.ProductsByVariants)
			r.Post("/", gateway.ProductsByVariants)
		})
	})

	r.Route("/taxonomies/{taxonomyID}", func(r chi.Router) {
		r.With(
			legacy.Channel,
			legacy.CountryCode,
			legacy.Locale,
			middleware.CountryCode,
			middleware.Locale,
			middleware.UserID,
			middleware.CacheMaxAge,
		).Get("/taxons", gateway.TaxonomyList)
	})

	r.Route("/taxonomies_by_stores", func(r chi.Router) {
		r.With(
			legacy.Channel,
			legacy.CountryCode,
			legacy.Locale,
			middleware.CountryCode,
			middleware.Locale,
			middleware.UserID,
			middleware.CacheMaxAge,
		).Get("/", gateway.TaxonomiesByStores)
	})
}

// V3 route
func V3(r chi.Router) {
	rr := r.With(
		middleware.CountryCode,
		middleware.Currency,
		middleware.Locale,
		middleware.UserID,
	)

	rr.Route("/documents", func(r chi.Router) {
		r.Post("/products/{productID:[0-9]+}", gateway.IndexProduct)
		r.Route("/store_products", func(r chi.Router) {
			r.Post("/{storeProductID:[0-9]+}", gateway.IndexStockItem)
			r.Post("/", gateway.UpdateStockItem)
		})
	})

	rr.Route("/products", func(r chi.Router) {
		r.Post("/track/{action:\\b(click|atc|purchased)\\b}", gateway.TrackUserEvent)
		r.Post("/{stockItemID:[0-9]+}", gateway.Deprecated)
		r.Post("/reindex", gateway.Deprecated)
		r.Post("/reindex/{spreeProductID:[0-9]+}", gateway.Deprecated)
		r.With(
			legacy.Channel,
			legacy.Locale,
			middleware.Locale,
		).Get("/search", gateway.GlobalSearch)
		r.With(
			legacy.Channel,
			legacy.Locale,
			middleware.Locale,
		).Get("/browse", gateway.GlobalBrowse)
		r.With(
			legacy.Channel,
			legacy.Locale,
			middleware.Locale,
		).Get("/search/autocomplete", gateway.GlobalSearchAutoComplete)
	})

	r.Route("/stock_locations/{stockLocationID}", func(r chi.Router) {
		r.With(
			legacy.Channel,
			legacy.CountryCode,
			legacy.StoreID,
			legacy.ProductID,
			legacy.Locale,
			middleware.CountryCode,
			middleware.Currency,
			middleware.Locale,
			middleware.GlobalID,
			middleware.UserID,
		).Get("/products/{productID:[0-9]+}", gateway.ProductDetail)
		r.With(
			legacy.Channel,
			legacy.CountryCode,
			legacy.StoreID,
			legacy.ProductID,
			legacy.Locale,
			middleware.CountryCode,
			middleware.Currency,
			middleware.Locale,
			middleware.GlobalID,
			middleware.UserID,
		).Get("/null_recommendation", gateway.NullRecommendation)

		// Themes
		r.With(
			legacy.Page,
			legacy.Channel,
			legacy.CountryCode,
			legacy.StoreID,
			legacy.Locale,
			middleware.Locale,
			middleware.UserID,
		).Route("/themes", func(rr chi.Router) {
			rr.Get("/", gateway.ThemesList)
			rr.Get("/{themeID}", gateway.BrowseTheme)
		})
	})

	r.Route("/cache", func(r chi.Router) {
		r.With(
			middleware.CacheKey,
		).Post("/clear", gateway.ClearCache)
	})
}
