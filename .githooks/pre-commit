#!/bin/sh

GOLANGCILINT=$GOPATH/bin/golangci-lint

# Check for golangci-lint
if [[ ! -x "$GOLANGCILINT" ]]; then
  printf "\t\033[41mPlease install golangci-lint\033[0m (https://github.com/golangci/golangci-lint)"
  exit 1
fi

$GOLANGCILINT run "--issues-exit-code" 1
if [[ $? == 0 ]]; then
  printf "\t\033[32mgolangci-lint run \033[0;30m\033[42mPASS\033[0m\n"
else
  printf "\t\033[31mgolangci-lint run \033[0;30m\033[41mFAILED!\033[0m\n"
  exit 1
fi

exit 0
