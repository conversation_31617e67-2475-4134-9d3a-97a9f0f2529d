# This is a sample build configuration for Go.
# Check our guides at https://confluence.atlassian.com/x/5Q4SMw for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: golang:1.19

definitions:
  caches:
    protoc: /tmp/protoc3
  services:
    redis:
      image: redis
    postgres:
      image: postgres
      variables:
        POSTGRES_HOST_AUTH_METHOD: trust
  steps:
    - step: &build-and-test
        name: Build and Test
        services:
          - postgres
          - redis
        caches:
          - protoc
        script:
          - ./ci/.ci.sh ubuntu
          - export GOSUMDB=off
          - go mod tidy
          - go mod download
          - go generate ./...
          - go build -v
          - golangci-lint run --skip-dirs=vendor
          - export TEST_DATABASE_URL="postgres://postgres@localhost:5432/postgres?sslmode=disable"
          - go clean -testcache
          - go run ./test/cli/seed.go
          - go test -race -v -coverprofile=testdata/coverage.out -short ./...
        artifacts:
          - testdata/coverage.out
    - step: &build-and-test-gateway
        name: Build and Test Gateway
        services:
          - postgres
          - redis
        caches:
          - protoc
        script:
          - ./ci/.ci.sh ubuntu
          - export GOSUMDB=off
          - go mod tidy
          - go mod download
          - go generate ./...
          - go build -v
          - go run ./test/integration/seed/seed.go
          - go clean -testcache
          - go test -race -v -timeout 20m ./test/integration/...
        artifacts:
          - testdata/coverage-gateway.out
    - step: &analyze-sast-code
        name: Analyze Pull Request Code Quality
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:golang119-latest
        script:
          - task inspect-code-quality
    - step: &code-quality-check
        name: Check Code Quality Snyk
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:golang119-latest
        caches:
          - node
        script:
          - task inspect-security-sca
        artifacts:
          - sca-result.html
    - step: &container-check
        name: Container Scan Snyk
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:golang119-latest
        caches:
          - node
        script:
          - task inspect-security-container
        artifacts:
          - container-result.html
    - step: &build-tarbal-staging
        name: Build and Package
        services:
          - postgres
          - redis
        caches:
          - protoc
        script:
          - ./ci/.ci.sh ubuntu
          - apt-get update && apt-get -y -qq install zip tar
          - export GOSUMDB=off
          - go mod tidy
          - go mod download
          - go generate ./...
          - go build -race -v -o resources/deploy/staging/search cli/search.go
          - cp VERSION resources/deploy/staging/
          - cd resources/deploy/staging/ && tar -cvzf ../../../artifact.tar.gz .
        artifacts:
          - Dockerfile
          - artifact.tar.gz
    - step: &build-tarbal-production
        name: Build and Package
        services:
          - postgres
          - redis
        caches:
          - protoc
        script:
          - ./ci/.ci.sh ubuntu
          - apt-get update && apt-get -y -qq install zip tar
          - export GOSUMDB=off
          - go mod tidy
          - go mod download
          - go generate ./...
          - go build -race -v -o resources/deploy/production/search cli/search.go
          - cp VERSION resources/deploy/production/
          - cd resources/deploy/production/ && tar -cvzf ../../../artifact.tar.gz .
        artifacts:
          - Dockerfile
          - artifact.tar.gz
    - step: &deploy-ecs
        name: Deploy to ECS
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:golang119-latest
        services:
          - docker
        caches:
          - docker
        script:
          - task publish
        artifacts:
          - container-result.html
pipelines:
  default:
    - parallel:
        - step: *build-and-test
        - step: *code-quality-check
        - step: *analyze-sast-code
  pull-requests:
    "**":
      - parallel:
          - step: *build-and-test
          - step: *code-quality-check
          - step: *analyze-sast-code
  custom:
    scan-container:
      - step: *container-check
    scan-snyk-sca:
      - step: *code-quality-check
    run-develop-branch-code-analysis:
      - step: *build-and-test
      - step: *analyze-sast-code
    run-long-running-tests:
      - step: *build-and-test-gateway
    deploy-ecs-sandbox:
      - step: *build-tarbal-staging
      - step:
          <<: *deploy-ecs
          name: Deploy to ECS Sandbox
          deployment: sandbox-ecs
          services:
            - docker
          caches:
            - docker
    deploy-ecs-staging:
      - step: *build-tarbal-staging
      - step:
          <<: *deploy-ecs
          name: Stage Build with Docker, upload to ECR, update ECS task
          deployment: staging-ecs
    deploy-ecs-staging-canary:
      - step: *build-tarbal-staging
      - step:
          <<: *deploy-ecs
          name: Deploy to ECS Staging - Canary Version
          deployment: staging-canary
          services:
            - docker
          caches:
            - docker
    deploy-ecs-production:
      - step: *build-tarbal-production
      - step:
          <<: *deploy-ecs
          name: Deploy to ECS Production
          deployment: production-ecs
          trigger: manual
          services:
            - docker
          caches:
            - docker
    deploy-ecs-production-canary:
      - step: *build-tarbal-production
      - step:
          <<: *deploy-ecs
          name: Deploy to ECS Production - Canary Version
          deployment: production-canary
          trigger: manual
          services:
            - docker
          caches:
            - docker
    upload-s3-datalake:
      - step: *build-tarbal-production
      - step:
          name: Upload to S3 Datalake
          deployment: s3-datalake
          image: banst/awscli
          trigger: manual
          script:
            # Extract from tarball
            - tar -xvf artifact.tar.gz
            # Upload file to S3
            - aws s3 cp ./search s3://datalake.happyfresh.net/binary/
