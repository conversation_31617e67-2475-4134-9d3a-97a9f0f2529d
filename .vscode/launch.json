{"version": "0.2.0", "configurations": [{"name": "Server", "type": "go", "request": "launch", "mode": "debug", "remotePath": "", "program": "${workspaceRoot}/cli/search.go", "env": {}, "args": ["server"], "showLog": true}, {"name": "GRPC Server", "type": "go", "request": "launch", "mode": "debug", "remotePath": "", "program": "${workspaceRoot}/cli/search.go", "env": {}, "args": ["grpc"], "showLog": true}, {"name": "GRPC Gateway", "type": "go", "request": "launch", "mode": "debug", "remotePath": "", "program": "${workspaceRoot}/cli/search.go", "env": {}, "args": ["gateway"], "showLog": true}, {"name": "Worker", "type": "go", "request": "launch", "mode": "debug", "remotePath": "", "program": "${workspaceRoot}/cli/search.go", "env": {}, "args": ["worker"], "showLog": true}]}