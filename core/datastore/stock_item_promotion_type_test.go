package datastore

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStockItemPromotionTypeCount(t *testing.T) {
	testCases := []struct {
		name               string
		storeID            int
		promotionTypeCount int
	}{
		{"With promotion type count", 1003, 3},
		{"With no promotion type", 445, 0},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()

			sips, err := StockItemPromotionTypeCount(ctx, testCase.storeID)

			assert := assert.New(t)
			assert.NoError(err)
			assert.Equal(testCase.promotionTypeCount, len(sips.StockItemPromotions))
		})
	}
}
