package datastore

import (
	"database/sql"
	"encoding/json"
	"sort"
	"testing"

	"github.com/lib/pq/hstore"
)

func TestClientTypePromoPrice(t *testing.T) {
	for _, tc := range []struct {
		name        string
		raw         *RawStockItem
		expectation map[string]float32
	}{
		{
			"Valid Values",
			&RawStockItem{
				RawClientTypePromoPrice: hstore.Hstore{
					Map: map[string]sql.NullString{
						"j2me":         {String: "20000", Valid: true},
						"windowsphone": {String: "22000", Valid: true},
					},
				},
			},
			map[string]float32{
				"j2me":         20000,
				"windowsphone": 22000,
			},
		},
		{
			"Invalid Values",
			&RawStockItem{
				RawClientTypePromoPrice: hstore.Hstore{
					Map: map[string]sql.NullString{
						"j2me":         {String: "20000", Valid: true},
						"windowsphone": {String: "22000fff", Valid: true},
						"gofresh":      {String: "23000", Valid: false},
					},
				},
			},
			map[string]float32{
				"j2me": 20000,
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.raw.ClientTypePromoPrice()
			assertMarshal(t, tc.expectation, reality)
		})
	}
}

func TestTaxons(t *testing.T) {
	for _, tc := range []struct {
		name                 string
		raw                  *RawStockItem
		expectationIDs       []int64
		expectationName      []string
		expectationNameLocal []string
	}{
		{
			"Valid Values",
			&RawStockItem{
				RawTranslatedTaxon: hstore.Hstore{
					Map: map[string]sql.NullString{
						"{212,en}": {String: "Ready to Eat", Valid: true},
						"{212,th}": {String: "อาหารพร้อมรับประทาน", Valid: true},
						"{680,en}": {String: "Meals", Valid: true},
					},
				},
			},
			[]int64{212, 680},
			[]string{"Ready to Eat", "Meals"},
			[]string{"อาหารพร้อมรับประทาน"},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			realityIDs, realityNames, realityNameLocals := tc.raw.Taxons()

			rIds := []int{}
			for _, rID := range realityIDs {
				rIds = append(rIds, int(rID))
			}
			sort.Ints(rIds)
			sort.Strings(realityNames)
			sort.Strings(realityNameLocals)

			eIds := []int{}
			for _, eID := range tc.expectationIDs {
				eIds = append(eIds, int(eID))
			}
			sort.Ints(eIds)
			sort.Strings(tc.expectationName)
			sort.Strings(tc.expectationNameLocal)

			assertMarshal(t, eIds, rIds)
			assertMarshal(t, tc.expectationName, realityNames)
			assertMarshal(t, tc.expectationNameLocal, realityNameLocals)
		})
	}
}

func TestProductTypes(t *testing.T) {
	for _, tc := range []struct {
		name                 string
		raw                  *RawStockItem
		expectationIDs       []int64
		expectationName      []string
		expectationNameLocal []string
		expectationPath      string
	}{
		{
			"Valid Values",
			&RawStockItem{
				RawTranslatedProductType: hstore.Hstore{
					Map: map[string]sql.NullString{
						"{\"Ready to Eat\",\"อาหารพร้อมรับประทาน\"}": {String: "212", Valid: true},
						"{\"Meals\",\"\"}": {String: "680", Valid: true},
					},
				},
				RawProductTypePath: sql.NullString{String: "rme/meals", Valid: true},
			},
			[]int64{212, 680},
			[]string{"Ready to Eat", "Meals"},
			[]string{"อาหารพร้อมรับประทาน"},
			"rme/meals",
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			realityIDs, realityNames, realityNameLocals, realityPath := tc.raw.ProductTypes()

			rIds := []int{}
			for _, rID := range realityIDs {
				rIds = append(rIds, int(rID))
			}
			sort.Ints(rIds)
			sort.Strings(realityNames)
			sort.Strings(realityNameLocals)

			eIds := []int{}
			for _, eID := range tc.expectationIDs {
				eIds = append(eIds, int(eID))
			}
			sort.Ints(eIds)
			sort.Strings(tc.expectationName)
			sort.Strings(tc.expectationNameLocal)

			assertMarshal(t, eIds, rIds)
			assertMarshal(t, tc.expectationName, realityNames)
			assertMarshal(t, tc.expectationNameLocal, realityNameLocals)

			if tc.expectationPath != realityPath {
				t.Errorf("\nexpectation %v\nreality %v", tc.expectationPath, realityPath)
			}
		})
	}
}

func TestUnitPriceUnit(t *testing.T) {
	c := func(supermarketUnit string) hstore.Hstore {
		return hstore.Hstore{
			Map: map[string]sql.NullString{
				propertiesSupermarketUnitTag: {
					String: supermarketUnit,
					Valid:  true,
				},
			},
		}
	}

	for _, tc := range []struct {
		name        string
		properties  hstore.Hstore
		expectation string
	}{
		{"Unit l", c("1 l"), "100 ml"},
		{"Unit ml", c("100 ml"), "100 ml"},
		{"Unit kg", c("1 kg"), "100 g"},
		{"Unit g", c("100 g"), "100 g"},
		{"Unit mg", c("100 mg"), "mg"},
		{"Unit each", c("1 pack"), "each"},
	} {
		t.Run(tc.name, func(t *testing.T) {
			si := &RawStockItem{RawProperties: tc.properties}
			reality := si.UnitPriceUnit()
			if tc.expectation != reality {
				t.Errorf("\nexpectation %v\nreality %v", tc.expectation, reality)
			}
		})
	}
}

func TestNaturalUnit(t *testing.T) {
	c := func(pkg, size, unit string) hstore.Hstore {
		return hstore.Hstore{
			Map: map[string]sql.NullString{
				propertiesPackageTag: {
					String: pkg,
					Valid:  true,
				},
				propertiesSizeTag: {
					String: size,
					Valid:  true,
				},
				propertiesUnitTag: {
					String: unit,
					Valid:  true,
				},
			},
		}
	}

	for _, tc := range []struct {
		name        string
		properties  hstore.Hstore
		expectation string
	}{
		{"pkg <= 1, sz < 1", c("0", "0", "gr"), "gr"},
		{"pkg > 1, sz < 1", c("2", "0", "gr"), "2 x gr"},
		{"pkg > 1, sz >= 1", c("2", "100", "gr"), "2 x 100 gr"},
		{"pkg <= 1, sz >= 1", c("0", "100", "gr"), "100 gr"},
	} {
		t.Run(tc.name, func(t *testing.T) {
			si := &RawStockItem{RawProperties: tc.properties}
			reality := si.NaturalUnit()
			if tc.expectation != reality {
				t.Errorf("\nexpectation %v\nreality %v", tc.expectation, reality)
			}
		})
	}
}

func TestNatualAverageWeight(t *testing.T) {
	c := func(avgWeight, size string) hstore.Hstore {
		return hstore.Hstore{
			Map: map[string]sql.NullString{
				propertiesAverageWeightTag: {
					String: avgWeight,
					Valid:  true,
				},
				propertiesSizeTag: {
					String: size,
					Valid:  true,
				},
			},
		}
	}

	for _, tc := range []struct {
		name        string
		properties  hstore.Hstore
		expectation float64
	}{
		{"avg_weight > 0, size > 1", c("0.5", "2"), 0.25},
		{"avg_weight > 0, size > 0", c("0.5", "1"), 0.5},
		{"avg_weight > 0, size <= 0", c("0.5", "0"), 0.5},
		{"avg_weight <= 0, size <= 0", c("", ""), 0},
	} {
		t.Run(tc.name, func(t *testing.T) {
			si := &RawStockItem{RawProperties: tc.properties}
			reality := si.NaturalAverageWeight()
			if tc.expectation != reality {
				t.Errorf("\nexpectation %v\nreality %v", tc.expectation, reality)
			}
		})
	}
}

func assertMarshal(t *testing.T, expectation, reality interface{}) {
	e, err := json.Marshal(expectation)
	if err != nil {
		t.Error(err)
	}

	r, err := json.Marshal(reality)
	if err != nil {
		t.Error(err)
	}

	if string(e) != string(r) {
		t.Errorf("\nexpectation %v\nreality %v", string(e), string(r))
	}
}

func TestStockItemRawNaturalUnit(t *testing.T) {
	createNullString := func(value string) sql.NullString {
		valid := true
		if value == "" {
			valid = false
		}

		return sql.NullString{
			String: value,
			Valid:  valid,
		}
	}

	testCases := []struct {
		name            string
		propertyPackage sql.NullString
		propertySize    sql.NullString
		propertyUnit    sql.NullString
		expectation     string
	}{
		{"Complete natural unit", createNullString("5"), createNullString("250.00"), createNullString("gr"), "5 x 250 gr"},
		{"1 package", createNullString("1"), createNullString("250.50"), createNullString("gr"), "250.5 gr"},
	}
	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			stockItemRaw := &RawStockItem{
				RawProperties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"package": testCase.propertyPackage,
						"size":    testCase.propertySize,
						"unit":    testCase.propertyUnit,
					},
				},
			}

			naturalUnit := stockItemRaw.NaturalUnit()
			if naturalUnit != testCase.expectation {
				t.Errorf("expectation: %v\nreality: %v", testCase.expectation, naturalUnit)
			}
		})
	}
}
