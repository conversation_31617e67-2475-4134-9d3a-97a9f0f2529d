package datastore

import (
	"os"
	"testing"

	_ "github.com/lib/pq"
	"happyfresh.io/search/lib/paperclip-go"
)

func TestMain(m *testing.M) {
	p, err := paperclip.New(paperclip.Config{
		"cdn":        "http://cdn.test",
		"secret_key": "123456789",
		"attachment": map[string]string{
			"path": "attachments",
			"name": "attachment",
		},
		"class": map[string]string{
			"path": "spree/images",
			"name": "Spree::Image",
		},
		"format": map[string]string{
			"hash_data": ":class/:attachment/:id/:style/:updated_at",
			"path":      "/t/:transform/:class/:attachment/:hash-:style.:extension",
		},
	})
	if err != nil {
		os.Exit(1)
	}

	done := OpenTest(m, LocalQuery, os.Getenv("TEST_DATABASE_URL"), p)
	defer done()

	os.Exit(m.Run())
}

func TestMars<PERSON>Key(t *testing.T) {
	for _, tt := range []struct {
		query string
		args  []interface{}
		want  string
	}{
		{"a", []interface{}{"10", 92.7}, "613a5b223130222c39322e375d"},
		{"data-should-without-collon", []interface{}{1}, "646174612d73686f756c642d776974686f75742d636f6c6c6f6e3a5b315d"},
	} {
		key, err := MarshalKey(tt.query, tt.args...)
		if err != nil {
			t.Error(err)
		}

		if key != tt.want {
			t.Errorf("\nexpectation: %s\nreality: %s", tt.want, key)
		}
	}
}
