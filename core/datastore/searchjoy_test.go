package datastore

import (
	"context"
	"database/sql"
	"testing"

	"github.com/stretchr/testify/assert"
)

func HelperCreateSearchID() (int64, error) {
	searchIdOptions := []SearchIDOption{
		WithSearchJoyQuery("beras"),
		WithSearchJoyUserID(int64(12)),
		WithSearchJoyNormalizedQuery("beras"),
		WithSearchJoyCountryISO("ID"),
		WithSearchJoyLocale("ID"),
		WithSearchJoyStockLocationID(123),
		WithSearchJoyUserID(int64(13)),
		WithSearchJoyTotalFound(20),
		WithSearchJoyExternalSearchID("externalId"),
		WithSearchJoyProperty("taxon_ids", ""),
		WithSearchJoyProperty("channel", "channel"),
	}

	return CreateSearchID(context.Background(), searchIdOptions...)
}

func TestCreateSearchID(t *testing.T) {

	searchId, _ := HelperCreateSearchID()

	assert := assert.New(t)

	assert.Greater(searchId, int64(0))

	searchId2, _ := HelperCreateSearchID()

	assert.Greater(searchId2, searchId)

}

func TestMapWithSearchIDOption(t *testing.T) {
	tableTest := []struct {
		name         string
		searchOption SearchIDOption
		expectation  interface{}
		comparedAttr func(*searchIDBody) interface{}
	}{
		{
			name:         "Test WithSearchJoyQuery",
			searchOption: WithSearchJoyQuery("beras"),
			expectation:  "beras",
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.query
			},
		},
		{
			name:         "Test WithSearchJoyNormalizedQuery",
			searchOption: WithSearchJoyNormalizedQuery("beras"),
			expectation:  "beras",
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.normalizedQuery
			},
		},
		{
			name:         "Test WithSearchJoyCountryISO",
			searchOption: WithSearchJoyCountryISO("ID"),
			expectation:  "ID",
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.countryISO
			},
		},
		{
			name:         "Test WithSearchJoyLocale",
			searchOption: WithSearchJoyLocale("ID"),
			expectation:  "ID",
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.locale
			},
		},
		{
			name:         "Test WithSearchJoyStockLocationID",
			searchOption: WithSearchJoyStockLocationID(123),
			expectation:  123,
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.stockLocationID
			},
		},
		{
			name:         "Test WithSearchJoyUserID",
			searchOption: WithSearchJoyUserID(int64(123)),
			expectation:  "123",
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.userID
			},
		},
		{
			name:         "Test WithSearchJoyUserID Zero",
			searchOption: WithSearchJoyUserID(int64(0)),
			expectation:  "NULL",
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.userID
			},
		},
		{
			name:         "Test WithSearchJoyTotalFound",
			searchOption: WithSearchJoyTotalFound(10),
			expectation:  10,
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.totalFound
			},
		},
		{
			name:         "Test WithSearchJoyExternalSearchID",
			searchOption: WithSearchJoyExternalSearchID("externalId"),
			expectation:  "externalId",
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.externalSearchID
			},
		},
		{
			name:         "Test WithSearchJoyProperty",
			searchOption: WithSearchJoyProperty("field", "value"),
			expectation:  sql.NullString{String: "value", Valid: true},
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.properties.Map["field"]
			},
		},
		{
			name:         "Test WithSearchJoyProperty Empty string",
			searchOption: WithSearchJoyProperty("field", ""),
			expectation:  sql.NullString{},
			comparedAttr: func(searchBody *searchIDBody) interface{} {
				return searchBody.properties.Map["field"]
			},
		},
	}

	for _, tc := range tableTest {
		t.Run(tc.name, func(t *testing.T) {
			searchBody := &searchIDBody{}
			tc.searchOption(searchBody)
			assert.Equal(t, tc.expectation, tc.comparedAttr(searchBody))
		})
	}

}

func TestFindSearchjoyByID(t *testing.T) {
	// Cannot mock static function MarshalKey
	assert := assert.New(t)
	searchId, _ := HelperCreateSearchID()
	searchjoyRecord, _ := FindSearchjoyByID(context.Background(), searchId)
	assert.NotNil(searchjoyRecord)

	searchjoyRecord, err := FindSearchjoyByID(context.Background(), int64(-1))
	assert.Nil(searchjoyRecord)
	assert.NotNil(err)

}

func TestScanSearchjoy(t *testing.T) {
	searchId, _ := HelperCreateSearchID()
	queries, _ := getQueries("/resources/sql/searchjoy.sql")
	conn := ro()
	sqlRow := conn.QueryRowContext(context.Background(), queries["find-searchjoy-by-id"], searchId)
	result, _ := scanSearchjoy(sqlRow)
	assert.NotNil(t, result) // Dont know how to validate this proto message

	_, err := scanSearchjoy(nil)
	assert.Error(t, err)

}
