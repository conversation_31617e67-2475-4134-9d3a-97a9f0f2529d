package datastore

import (
	"context"
	"fmt"
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

func TestBrandID(t *testing.T) {
	type want struct {
		id  int64
		err string
	}

	assert := func(t *testing.T, w want, c *Brand, err error) {
		if w.id != c.ID {
			t.<PERSON>("ID: Want %v Got %v", w.id, c.ID)
		}
	}

	for _, tt := range []struct {
		name      string
		brandName string
		want      want
	}{
		{"Brand Name inputted", "Bintang", want{1, ""}},
	} {
		t.Run(tt.name, func(t *testing.T) {
			u, err := BrandByName(context.Background(), tt.brandName)
			if err != nil {
				if len(tt.want.err) < 1 {
					t.Error(errors.Wrap(err, fmt.Sprintf("Failed to get: %v", tt.brandName)))
				}
			}

			assert(t, tt.want, u, err)
		})
	}
}

func TestBrandByNames(t *testing.T) {
	for _, tc := range []struct {
		name               string
		brandNamesParam    []string
		expectError        bool
		expectedBrandIDs   []int64
		expectedBrandNames []string
	}{
		{
			"Returns relevant brand",
			[]string{"Oreo", "Chiki", "Bintang"},
			false,
			[]int64{2, 3, 1},
			[]string{"Oreo", "Chiki", "Bintang"},
		},
		{
			"Returns relevant brand",
			[]string{"Oreo", "Chiki", "Bintangf"},
			false,
			[]int64{2, 3, 0},
			[]string{"Oreo", "Chiki", ""},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := BrandByNames(ctx, tc.brandNamesParam...)
			assert.Equal(t, tc.expectError, err != nil)
			if tc.expectError {
				return
			}

			resultIDs := []int64{}
			resultNames := []string{}
			for _, b := range result {
				resultIDs = append(resultIDs, b.ID)
				resultNames = append(resultNames, b.Name)
			}

			assert.ElementsMatch(t, tc.expectedBrandIDs, resultIDs)
			assert.ElementsMatch(t, tc.expectedBrandNames, resultNames)
		})
	}
}
