package datastore

import (
	"bytes"
	"context"
	"database/sql"
	fmt "fmt"
	"strconv"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

var (
	stockItemIDsReadGetter chacha.Getter
	stockItemIDsReadQuery  map[string]string
)

func StockItemIDsByVariantStore(ctx context.Context, variantIDs []int, filteredTaxonIDs []int64, storeID int, sort, direction string, nonSellableItems []string, pinnedVariantIDs []int64) ([]int, error) {
	buf := &bytes.Buffer{}
	buf.WriteString("{")
	for i, variantID := range variantIDs {
		if i > 0 {
			buf.WriteString(",")
		}
		buf.WriteString(strconv.Itoa(variantID))
	}
	buf.WriteString("}")

	vIDs := buf.String()

	buf = &bytes.Buffer{}
	buf.WriteString("{")
	for i, taxonID := range filteredTaxonIDs {
		if i > 0 {
			buf.WriteString(",")
		}
		buf.WriteString(strconv.FormatInt(taxonID, 10))
	}
	buf.WriteString("}")

	tIDs := buf.String()

	buf = &bytes.Buffer{}
	buf.WriteString("{")
	for i, item := range nonSellableItems {
		if i > 0 {
			buf.WriteString(",")
		}
		buf.WriteString(item)
	}
	buf.WriteString("}")

	nonSellables := buf.String()

	buf = &bytes.Buffer{}
	buf.WriteString("{")
	for i, variantID := range pinnedVariantIDs {
		if i > 0 {
			buf.WriteString(",")
		}
		buf.WriteString(strconv.FormatInt(variantID, 10))
	}
	buf.WriteString("}")

	pvIDs := buf.String()

	if sort == "popularity" || sort == "" || sort == "promotion_type" {
		sort = "popularity"
		direction = "desc"
	}

	if direction == "" {
		direction = "asc"
	}

	if sort == "variant" {
		direction = "id"
	}

	q := fmt.Sprintf("stock-item-ids-by-variant-store-%s-%s", sort, direction)
	key, err := MarshalKey(q, vIDs, storeID, tIDs, nonSellables)

	// case for manual sku boost
	if len(pinnedVariantIDs) > 0 && sort == "popularity" {
		q = "stock-item-ids-by-variant-store-pinned-popularity-desc"
		key, err = MarshalKey(q, vIDs, storeID, tIDs, nonSellables, pvIDs)
	}

	if err != nil {
		return nil, err
	}

	c := &StockItems{}
	err = stockItemIDsReadGetter.Get(ctx, key, c)
	if err != nil {
		return nil, err
	}

	ids := []int{}
	for _, stockItem := range c.Items {
		ids = append(ids, int(stockItem.Id))
	}

	return ids, nil
}

func scanStockItemIDRead(r *sql.Rows) (proto.Message, error) {
	stockItems := []*StockItem{}
	for r.Next() {
		var ord interface{}
		stockItem := &StockItem{}
		err := r.Scan(
			&stockItem.Id,
			&ord,
		)
		if err != nil {
			return nil, err
		}

		stockItems = append(stockItems, stockItem)
	}

	return &StockItems{Items: stockItems}, nil
}

func init() {
	queries, err := getQueries("/resources/sql/stock_item_id_read.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	stockItemIDsReadQuery = queries

	configures = append(configures, func() {
		stockItemIDsReadGetter = chacha.NewGetter("stock:item:ids:read",
			chacha.WithQueries(ro(), stockItemIDsReadQuery),
			chacha.WithRowsScanner(scanStockItemIDRead, 1*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
