package datastore

import (
	"context"
	"database/sql"
	"strings"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. ab_markup.proto

var (
	abMarkupGetter chacha.Getter
)

// UserID get user id for particular spree api token
func GetAbMarkupByUserID(ctx context.Context, userID int) string {
	if userID == 0 {
		return ""
	}

	var key string
	var err error

	key, err = cacheKeyEncoder(ctx, "ab-markup-by-user-id", userID)
	if err != nil {
		return ""
	}

	abMarkup := &AbMarkup{}
	err = abMarkupGetter.Get(ctx, key, abMarkup)
	if err != nil {
		return ""
	}

	return abMarkup.AbMarkup
}

func GetAbMarkupByOrderNumber(ctx context.Context, orderNumber string) string {
	if len(orderNumber) == 0 {
		return ""
	}

	var key string
	var err error

	key, err = cacheKeyEncoder(ctx, "ab-markup-by-order-number", orderNumber)
	if err != nil {
		return ""
	}

	abMarkup := &AbMarkup{}
	err = abMarkupGetter.Get(ctx, key, abMarkup)
	if err != nil {
		return ""
	}

	return abMarkup.AbMarkup
}

func scanAbMarkup(r *sql.Row) (proto.Message, error) {
	abMarkup := &AbMarkup{}
	err := r.Scan(
		&abMarkup.AbMarkup,
	)
	if err != nil {
		return nil, err
	}

	return abMarkup, nil
}

func init() {
	queries, err := getQueries("/resources/sql/ab_markup.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		abMarkupGetter = chacha.NewGetter("ab-markup",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanAbMarkup, 1*time.Hour),
			cacheBackend,
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
		)
	})
}

func cacheKeyEncoder(ctx context.Context, s string, args ...interface{}) (string, error) {
	sb := strings.Builder{}
	sb.WriteString(s)
	for _, arg := range args {
		sb.WriteString(":")
		sb.WriteString(str.MaybeString(arg).String())
	}

	return sb.String(), nil
}

func cacheKeyDecoder(ctx context.Context, s string) (string, []interface{}, error) {
	keys := strings.Split(s, ":")

	var getterName string
	args := make([]interface{}, len(keys)-1)
	for i, key := range keys {
		if i == 0 {
			getterName = key
			continue
		}

		args[i-1] = key
	}

	return getterName, args, nil
}
