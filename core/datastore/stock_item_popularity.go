package datastore

import (
	"context"
	"database/sql"
	fmt "fmt"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

var (
	stockItemPopularityGetter        chacha.Getter
	stockItemAveragePopularityGetter chacha.Getter
	stockItemPopularityQuery         map[string]string
)

func StockItemPopularityByVariantAndStoreID(ctx context.Context, variantID int64, storeID int64) (popularity float64, err error) {
	q := fmt.Sprintf("stock-item-popularity-by-id")
	key, err := MarshalKey(q, variantID, storeID)
	if err != nil {
		return
	}

	c := &StockItems{}
	if err = stockItemPopularityGetter.Get(ctx, key, c); err != nil {
		return
	}

	if len(c.Items) > 0 {
		popularity = c.Items[0].Popularity
	}

	return
}

func scanStockItemsPopularity(r *sql.Rows) (proto.Message, error) {
	stockItems := []*StockItem{}
	for r.Next() {
		stockItem := &StockItem{}
		err := r.Scan(
			&stockItem.VariantId,
			&stockItem.Popularity,
		)
		if err != nil {
			return nil, err
		}

		stockItems = append(stockItems, stockItem)
	}

	return &StockItems{Items: stockItems}, nil
}

func StockItemPopularityAverage(ctx context.Context, variantID int64) (popularity float64, err error) {
	q := fmt.Sprintf("stock-item-popularity-average-by-id")
	key, err := MarshalKey(q, variantID)
	if err != nil {
		return
	}

	c := &StockItems{}
	if err = stockItemAveragePopularityGetter.Get(ctx, key, c); err != nil {
		return
	}

	if len(c.Items) > 0 {
		popularity = c.Items[0].Popularity
	}

	return
}

func init() {
	queries, err := getQueries("/resources/sql/stock_item_popularity.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	stockItemPopularityQuery = queries

	configures = append(configures, func() {
		stockItemPopularityGetter = chacha.NewGetter("stock:item:popularity",
			chacha.WithQueries(ro(), stockItemPopularityQuery),
			chacha.WithRowsScanner(scanStockItemsPopularity, 7*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)

		stockItemAveragePopularityGetter = chacha.NewGetter("stock:item:averagepopularity",
			chacha.WithQueries(ro(), stockItemPopularityQuery),
			chacha.WithRowsScanner(scanStockItemsPopularity, 7*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
