package datastore

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStockItemIDsByVariantStore(t *testing.T) {
	for _, tc := range []struct {
		name                  string
		variantIDsParam       []int
		filteredTaxonIDsParam []int64
		storeIDParam          int
		sortParam             string
		directionParam        string
		nonSellableItemsParam []string
		pinnedVariantIDsParam []int64
		expectError           bool
		expectedStockItemIDs  []int
	}{
		{
			"Returns stock item IDs with default params / sorted by popularity desc",
			[]int{91, 92},
			[]int64{},
			1003,
			"",
			"",
			[]string{},
			[]int64{},
			false,
			[]int{12, 11},
		},
		{
			"Returns stock item IDs, sorted by name desc",
			[]int{91, 92},
			[]int64{},
			1003,
			"name",
			"asc",
			[]string{},
			[]int64{},
			false,
			[]int{11, 12},
		},
		{
			"Returns stock item IDs, sorted by name desc",
			[]int{91, 92},
			[]int64{},
			1003,
			"name",
			"desc",
			[]string{},
			[]int64{},
			false,
			[]int{12, 11},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := StockItemIDsByVariantStore(ctx, tc.variantIDsParam, tc.filteredTaxonIDsParam, tc.storeIDParam, tc.sortParam, tc.directionParam, tc.nonSellableItemsParam, tc.pinnedVariantIDsParam)
			assert.Equal(t, tc.expectError, err != nil)
			if tc.expectError {
				return
			}

			assert.Equal(t, tc.expectedStockItemIDs, result)
		})
	}
}
