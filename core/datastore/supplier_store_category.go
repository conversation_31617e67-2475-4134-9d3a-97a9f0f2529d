package datastore

import (
	"context"
	"database/sql"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. supplier_store_category.proto

var storeSupplierCategoryGetter chacha.Getter

func SupplierStoreCategoryIDsBySupplierID(ctx context.Context, supplierID int64) ([]int64, error) {
	key, err := MarshalKey("supplier-store-category-by-supplier-id", supplierID)
	if err != nil {
		return nil, err
	}

	supplierStoreCategories := &SupplierStoreCategories{}
	err = storeSupplierCategoryGetter.Get(ctx, key, supplierStoreCategories)
	if err != nil {
		return nil, err
	}

	result := make([]int64, len(supplierStoreCategories.SupplierStoreCategories))
	for i, ssc := range supplierStoreCategories.SupplierStoreCategories {
		result[i] = ssc.StoreCategoryId
	}

	return result, nil
}

func SupplierStoreCategoryIDsBySupplierIDs(ctx context.Context, supplierIDs []int64) ([]int64, error) {
	result := make([]proto.Message, len(supplierIDs))
	keys := make([]string, len(supplierIDs))

	for i, id := range supplierIDs {
		k, err := MarshalKey("supplier-store-category-by-supplier-id", id)
		if err != nil {
			return nil, err
		}

		keys[i] = k
		result[i] = proto.Message(&SupplierStoreCategories{})
	}

	err := storeSupplierCategoryGetter.GetMany(ctx, keys, result)
	if err != nil {
		return nil, err
	}

	storeCategories := map[int64]struct{}{}
	for _, r := range result {
		for _, storeCategory := range r.(*SupplierStoreCategories).SupplierStoreCategories {
			storeCategories[storeCategory.StoreCategoryId] = struct{}{}
		}
	}

	ids := []int64{}
	for k, _ := range storeCategories {
		ids = append(ids, k)
	}

	return ids, nil
}

func scanSupplierStoreCategories(r *sql.Rows) (proto.Message, error) {
	supplierStoreCategories := []*SupplierStoreCategory{}

	for r.Next() {
		supplierStoreCategory := &SupplierStoreCategory{}
		var supplierID sql.NullInt64
		var storeCategoryID sql.NullInt64

		if err := r.Scan(
			&supplierID,
			&storeCategoryID,
		); err != nil {
			return nil, err
		}

		if supplierID.Valid {
			supplierStoreCategory.SupplierId = supplierID.Int64
		}
		if storeCategoryID.Valid {
			supplierStoreCategory.StoreCategoryId = storeCategoryID.Int64
		}

		supplierStoreCategories = append(supplierStoreCategories, supplierStoreCategory)
	}

	return &SupplierStoreCategories{SupplierStoreCategories: supplierStoreCategories}, nil
}

func init() {
	queries, err := getQueries("/resources/sql/store_supplier_category.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}
	configures = append(configures, func() {
		storeSupplierCategoryGetter = chacha.NewGetter("store:supplier:category",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowsScanner(scanSupplierStoreCategories, 30*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
