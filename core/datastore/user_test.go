package datastore

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/pkg/errors"
)

func TestUserID(t *testing.T) {
	type want struct {
		id  int64
		err string
	}

	assert := func(t *testing.T, w want, c *User, err error) {
		if len(w.err) > 1 {
			if !strings.Contains(err.<PERSON>r(), w.err) {
				t.<PERSON>("Err: Want %s Got %s", w.err, err.<PERSON>rror())
			}
			return
		}

		if w.id != c.Id {
			t.<PERSON><PERSON>("ID: Want %d Got %d", w.id, c.Id)
		}
	}

	for _, tt := range []struct {
		name, spreeToken string
		want             want
	}{
		{"Spree API token inputted", "ecd65021997ed79ded87a9c5db882494d9fb2aa920889e87", want{6, ""}},
		{"Spree API token not inputted", "", want{err: "Invalid"}},
		{"API token didnt match any user ID", "xxx", want{err: "no rows"}},
	} {
		t.Run(tt.name, func(t *testing.T) {
			u, err := UserID(context.Background(), tt.spreeToken)
			if err != nil {
				if len(tt.want.err) < 1 {
					t.Error(errors.Wrap(err, fmt.Sprintf("Failed to get: %s", tt.spreeToken)))
				}
			}

			assert(t, tt.want, u, err)
		})
	}
}
