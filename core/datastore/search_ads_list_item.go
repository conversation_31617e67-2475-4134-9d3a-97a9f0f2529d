package datastore

import (
	"context"
	"database/sql"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. search_ads_list_item.proto

var (
	searchAdsListItemsGetter chacha.Getter
)

func SearchAdsListItemsByKeyword(ctx context.Context, keyword string) ([]*SearchAdsListItem, error) {
	searchAdsListItems := &SearchAdsListItems{}

	q := "variant-id-sku-max-product-count-by-keyword"
	key, err := MarshalKey(q, keyword)
	if err != nil {
		log.For("datastore", "SearchAdsListItemsByKeyword").Errorf(err.Error())
		return nil, err
	}

	err = searchAdsListItemsGetter.Get(ctx, key, searchAdsListItems)
	if err != nil {
		log.For("datastore", "SearchAdsListItemsByKeyword").<PERSON><PERSON><PERSON>(err.Error())
		return nil, err
	}

	return searchAdsListItems.SearchAdsListItems, nil
}

func scanSearchAdsListItems(r *sql.Rows) (proto.Message, error) {
	searchAdsListItems := []*SearchAdsListItem{}

	for r.Next() {
		searchAdsListItem := &SearchAdsListItem{}
		err := r.Scan(
			&searchAdsListItem.VariantId,
			&searchAdsListItem.Sku,
			&searchAdsListItem.MaxProductCount,
		)
		if err != nil {
			return nil, err
		}

		searchAdsListItems = append(searchAdsListItems, searchAdsListItem)
	}

	return &SearchAdsListItems{SearchAdsListItems: searchAdsListItems}, nil
}

func init() {
	queries, err := getQueries("/resources/sql/search_ads_list_item.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		searchAdsListItemsGetter = chacha.NewGetter("search:ads:list:items",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowsScanner(scanSearchAdsListItems, 3*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
