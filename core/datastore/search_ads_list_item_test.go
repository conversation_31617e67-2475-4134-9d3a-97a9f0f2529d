package datastore

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestVariantIdsByKeyword(t *testing.T) {
	for _, tc := range []struct {
		name           string
		keyword        string
		expectedResult []int64
	}{
		{
			"Return variant ids",
			"apple",
			[]int64{23, 21, 24, 22},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			_, err := SearchAdsListItemsByKeyword(ctx, tc.keyword)
			// TOOD: set Seed data to meet this expectation. currently connect to actual db
			assert.Error(t, err)
			// assert.NoError(t, err)
			// assert.NotEmpty(t, result)
		})
	}
}
