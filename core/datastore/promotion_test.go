package datastore

import (
	"context"
	"testing"
)

func TestPromotionByID(t *testing.T) {
	for _, tc := range []struct {
		name       string
		param      int
		promoIDExp int
		isErrExp   bool
	}{
		{"Test Exist", 1, 1, false},
		{"Test Not Exist", 900, 0, true},
	} {
		p, err := PromotionByID(context.Background(), tc.param)
		if (err != nil) != tc.isErrExp {
			t.<PERSON><PERSON>rf("expectation: %v; reality: %v", tc.isErrExp, err != nil)
		}

		if tc.isErrExp {
			return
		}

		if int(p.Id) != tc.promoIDExp {
			t.<PERSON>("expectation: %v; reality: %v", tc.promoIDExp, p.Id)
		}
	}
}
