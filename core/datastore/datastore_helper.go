package datastore

import (
	"bytes"
	fmt "fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"

	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/paperclip-go"
)

func OpenTest(m *testing.M, s QuerySource, dsn string, p paperclip.Paperclip) (done func()) {
	Open(Option{
		QuerySource: s,
		Driver:      "postgres",
		WriteDB:     dsn,
		Paperclip:   p,
	})

	done = func() {
		err := conn.Close()
		if err != nil {
			log.For("test", "Database").Fatal(err)
		}
	}

	return
}

func Seed() error {
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Dir(b)
	fs, err := filepath.Glob(basepath + "/../.." + "/testdata/sql/*.sql")
	if err != nil {
		return err
	}

	ddl := &bytes.Buffer{}
	for _, p := range fs {
		f, err := os.Open(p)
		if err != nil {
			return err
		}

		b, err := ioutil.ReadAll(f)
		if err != nil {
			return err
		}

		ddl.Write(b)
	}

	_, err = conn.Exec(ddl.String())
	if err != nil {
		log.For("test", "Database").Fatal(err)
	}

	return nil
}

func Truncate() error {
	tables := []string{
		"searchjoy_searches",
		"analytics.stock_item_promotions_v5",
		"spree_stock_items",
		"spree_variants",
		"spree_products_taxons",
		"spree_product_translations",
		"spree_products",
		"spree_brands",
		"spree_product_types",
		"spree_taxon_translations",
		"spree_taxons",
		"spree_stock_locations",
		"spree_countries",
		"spree_users",
		"spree_line_items",
		"spree_orders",
		"spree_taxons_promotion_rules",
		"spree_products_promotion_rules",
		"spree_stock_locations_promotion_rules",
		"spree_promotion_rules",
		"spree_promotions",
		"spree_assets",
		"spree_supplier_store_categories",
	}

	_, err := conn.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s;", strings.Join(tables, ", ")))
	if err != nil {
		return err
	}

	return nil
}
