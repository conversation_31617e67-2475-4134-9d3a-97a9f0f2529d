package datastore

import (
	"context"
	"database/sql"
	"strings"
	"time"

	"github.com/pkg/errors"

	proto "github.com/golang/protobuf/proto"
	"github.com/lib/pq"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. promotion_datastore.proto

var promotions chacha.Getter

func PromotionByID(ctx context.Context, promotionID int) (*Promotion, error) {
	key, err := MarshalKey("promotion-detail-by-id", promotionID)
	if err != nil {
		return nil, err
	}

	promotion := &Promotion{}
	err = promotions.Get(ctx, key, promotion)
	if err != nil {
		return nil, err
	}

	return promotion, nil
}

func scanPromotionDetail(r *sql.Rows) (proto.Message, error) {
	var promotion *Promotion
	for r.Next() {
		if promotion == nil {
			promotion = &Promotion{}
		}

		var rulesMatchPolicy string
		var ruleType string
		var ids pq.Int64Array
		var displayName sql.NullString
		var code sql.NullString
		err := r.Scan(
			&promotion.Id,
			&displayName,
			&ruleType,
			&rulesMatchPolicy,
			&ids,
			&code,
		)
		if err != nil {
			return nil, err
		}

		matchPolicy := "any"
		rmp := strings.Split(rulesMatchPolicy, "match_policy: ")
		if len(rmp) >= 2 {
			matchPolicy = strings.TrimSpace(rmp[1])
		}

		if displayName.Valid {
			promotion.DisplayName = displayName.String
		}

		if code.Valid {
			promotion.Code = code.String
		}

		switch ruleType {
		case "Spree::Promotion::Rules::Product":
			promotion.ProductRulesMatchPolicy = matchPolicy
			promotion.ProductRules = ids
		case "Spree::Promotion::Rules::StockLocationRule":
			promotion.StockLocationRulesMatchPolicy = matchPolicy
			promotion.StockLocationRules = ids
		case "Spree::Promotion::Rules::TaxonRule":
			promotion.TaxonRulesMatchPolicy = matchPolicy
			promotion.TaxonRules = ids
		}
	}
	if promotion == nil {
		return nil, errors.New("Cannot find row")
	}

	return promotion, nil
}

func init() {
	queries, err := getQueries("/resources/sql/promotion.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		promotions = chacha.NewGetter("promotions",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowsScanner(scanPromotionDetail, 1*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
