package datastore // import "happyfresh.io/search/core/datastore"

import (
	"context"
	"database/sql"
	"strings"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. country.proto

var (
	countries chacha.Getter
)

func getCountry(ctx context.Context, name string, args ...interface{}) (*Country, error) {
	c := &Country{}
	key, err := <PERSON><PERSON><PERSON>(name, args...)
	if err != nil {
		return nil, err
	}

	err = countries.Get(ctx, key, c)
	if err != nil {
		return nil, err
	}

	return c, nil
}

func scanCountry(r *sql.Row) (proto.Message, error) {
	c := &Country{}
	err := r.<PERSON>an(
		&c.Id,
		&c.IsoName,
		&c.Name,
		&c.Currency,
	)
	if err != nil {
		return nil, err
	}

	return c, nil
}

// CountryByStockLocationID get country for particular store
func CountryByStockLocationID(ctx context.Context, id int) (*Country, error) {
	return getCountry(ctx, "country-by-stock-location-id", id)
}

func CountryByISO(ctx context.Context, iso string) (*Country, error) {
	iso = strings.ToUpper(iso)
	return getCountry(ctx, "country-by-iso", iso)
}

func init() {
	queries, err := getQueries("/resources/sql/country.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		countries = chacha.NewGetter("countries",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanCountry, 30*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
