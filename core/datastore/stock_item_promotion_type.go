package datastore

import (
	"context"
	"database/sql"
	"time"

	"github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. stock_item_promotion.proto

var (
	stockItemPromotionTypes chacha.Getter
)

func StockItemPromotionTypeCount(ctx context.Context, storeId int) (*StockItemPromotions, error) {
	s, err := getStockItemPromotionTypes(ctx, "stock-item-promotion-count-by-store-id", storeId)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func getStockItemPromotionTypes(ctx context.Context, name string, args ...interface{}) (*StockItemPromotions, error) {
	c := &StockItemPromotions{}
	key, err := MarshalKey(name, args...)
	if err != nil {
		log.For("datastore", "StockItemPromotionType").Errorf(err.Error())
		return nil, err
	}

	err = stockItemPromotionTypes.Get(ctx, key, c)
	if err != nil {
		log.For("datastore", "StockItemPromotionType").Errorf(err.Error())
		return nil, err
	}

	return c, nil
}

func scanStockItemPromotionTypes(r *sql.Rows) (proto.Message, error) {
	stockItemPromotions := []*StockItemPromotion{}
	for r.Next() {
		stockItemPromotion := &StockItemPromotion{}
		err := r.Scan(
			&stockItemPromotion.Type,
			&stockItemPromotion.Total,
		)
		if err != nil {
			return nil, err
		}

		stockItemPromotions = append(stockItemPromotions, stockItemPromotion)
	}

	return &StockItemPromotions{StockItemPromotions: stockItemPromotions}, nil
}

func init() {
	queries, err := getQueries("/resources/sql/stock_item_promotion.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		stockItemPromotionTypes = chacha.NewGetter("stock:item:promotion:types",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowsScanner(scanStockItemPromotionTypes, time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
