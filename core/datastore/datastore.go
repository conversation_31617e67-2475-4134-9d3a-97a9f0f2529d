package datastore

import (
	"context"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"math/rand"
	"strings"
	"sync"

	"github.com/nleof/goyesql"
	"github.com/pkg/errors"
	"happyfresh.io/search/lib/chacha"
	"happyfresh.io/search/lib/paperclip-go"
)

//go:generate esc -o datastore.sql.go -pkg datastore -ignore ../../resources/sql/stock_item_promotion_matview.sql ../../resources/sql/

type QuerySource uint8

const (
	_ QuerySource = iota
	LocalQuery
	EmbeddedQuery
)

var (
	source       QuerySource
	conn         *sql.DB
	roConn       []*sql.DB
	cacheBackend chacha.Option
	dbOnce       sync.Once

	imageGenerator paperclip.Paperclip
	configures     []func()

	cacheEncoding = chacha.WithKeyEncoding(KeyEncoder, KeyDecoder)
)

func GetCurrentConn() (*sql.DB, []*sql.DB) {
	return conn, roConn
}

func (s QuerySource) UseLocal() bool {
	return s == LocalQuery
}

type Option struct {
	Driver       string
	WriteDB      string
	ReadDB       []string
	Paperclip    paperclip.Paperclip
	CacheBackend chacha.Option
	QuerySource  QuerySource
	MaxOpenConns int
	MaxIdleConns int
}

func Open(o Option) {
	dbOnce.Do(func() {
		source = o.QuerySource
		imageGenerator = o.Paperclip
		cacheBackend = o.CacheBackend

		db, err := sql.Open(o.Driver, o.WriteDB)
		if err != nil {
			panic(errors.Wrapf(err, "[datastore] Error opening database"))
		}
		db.SetMaxIdleConns(o.MaxIdleConns)
		db.SetMaxOpenConns(o.MaxOpenConns)

		conn = db

		for _, v := range o.ReadDB {
			db, err := sql.Open(o.Driver, v)
			if err == nil {
				db.SetMaxIdleConns(o.MaxIdleConns)
				db.SetMaxOpenConns(o.MaxOpenConns)
				roConn = append(roConn, db)
			}
		}

		for _, call := range configures {
			call()
		}
	})
}

func getQueries(name string) (map[string]string, error) {
	b, err := FSByte(source.UseLocal(), name)
	if err != nil {
		return nil, err
	}
	a, err := goyesql.ParseBytes(b)
	if err != nil {
		return nil, err
	}

	m := map[string]string{}
	for k, v := range a {
		m[string(k)] = v
	}

	return m, nil
}

func ro() *sql.DB {
	n := len(roConn)
	if n == 1 {
		return roConn[0]
	}
	if n > 0 {
		return roConn[rand.Intn(n)]
	}

	return conn
}

func KeyEncoder(ctx context.Context, s string, args ...interface{}) (string, error) {
	return MarshalKey(s, args...)
}

func MarshalKey(queryName string, args ...interface{}) (string, error) {
	var b strings.Builder
	b.WriteString(queryName)
	b.WriteString(":")
	j, err := json.Marshal(args)
	if err != nil {
		return b.String(), err
	}
	b.Write(j)

	return hex.EncodeToString([]byte(b.String())), nil
}

func KeyDecoder(ctx context.Context, s string) (string, []interface{}, error) {
	return UnmarshalKey(s)
}

func UnmarshalKey(hash string) (query string, args []interface{}, err error) {
	key, err := hex.DecodeString(hash)
	if err != nil {
		return "", nil, err
	}
	keys := strings.SplitN(string(key), ":", 2)
	query = keys[0]
	err = json.Unmarshal([]byte(keys[1]), &args)
	return
}
