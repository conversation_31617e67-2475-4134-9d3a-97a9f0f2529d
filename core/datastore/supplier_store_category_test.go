package datastore

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSupplierStoreCategoryIDsBySupplierID(t *testing.T) {
	testCases := []struct {
		name         string
		supplierID   int64
		expectedSCIs []int64
	}{
		{"Find store category IDs by supplier ID", 11, []int64{1, 2, 3, 4, 5}},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()

			scis, err := SupplierStoreCategoryIDsBySupplierID(ctx, testCase.supplierID)

			assert := assert.New(t)
			assert.NoError(err)
			assert.Equal(testCase.expectedSCIs, scis)
		})
	}
}
