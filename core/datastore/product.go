package datastore

import (
	"context"
	"database/sql"
	"strings"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=.  product.proto

var products chacha.Getter

func ProductByID(ctx context.Context, locale string, stock_location_id, id int64) (*Product, error) {
	s, err := getProduct(ctx, products, "product-by-id", strings.ToLower(locale), stock_location_id, id)
	if err != nil {
		return nil, err
	}

	s.Locale = locale
	return s, nil
}

func (p *Product) ProductUnitPriceFactor() float64 {
	unitPieces := float64(p.UnitPieces)
	naturalAvgWeight := float64(p.AverageWeight)
	size := float64(p.Size_)

	factor := 1.0

	if unitPieces > 0 {
		factor = factor / unitPieces
	}

	if naturalAvgWeight > 0 {
		factor = factor / naturalAvgWeight
	} else if size > 0 {
		factor = factor / size
	}

	switch strings.ToLower(strings.TrimSpace(p.SupermarketUnit)) {
	case "kg", "l":
		factor = factor / 1000
		fallthrough
	case "ml", "g":
		factor = factor * 100
	case "packet", "each", "pack":
		// Do Nothing
	default:
		factor = 1.0
	}

	return factor
}

func getProduct(ctx context.Context, getter chacha.Getter, name string, args ...interface{}) (*Product, error) {
	c := &Product{}
	key, err := MarshalKey(name, args...)
	if err != nil {
		return nil, err
	}

	err = products.Get(ctx, key, c)
	if err != nil {
		return nil, err
	}

	taxons, err := TaxonsByProductID(ctx, int(c.Id), c.Locale)
	if err != nil {
		return nil, err
	}

	resTaxonIDs := make([]int64, 0, len(taxons))
	resTaxonNames := make([]string, 0, len(taxons))
	for _, taxn := range taxons {
		if taxn.Id > 0 {
			resTaxonIDs = append(resTaxonIDs, taxn.Id)
		}

		if len(taxn.NameLocale) > 0 {
			resTaxonNames = append(resTaxonNames, taxn.NameLocale)
		} else if len(taxn.Name) > 0 {
			resTaxonNames = append(resTaxonNames, taxn.Name)
		}
	}

	c.TaxonIds = resTaxonIDs
	c.Categories = resTaxonNames
	c.PricePromotion = &ProductPricePromotion{
		Sku: c.Sku,
	}

	return c, nil
}

func scanProduct(r *sql.Row) (proto.Message, error) {
	raw := &ProductRaw{}
	err := r.Scan(
		&raw.Id,
		&raw.Name,
		&raw.NameLocal,
		&raw.Description,
		&raw.DescriptionLocal,
		&raw.VariantId,
		&raw.Sku,
		&raw.IsMaster,
		&raw.TrackInventory,
		&raw.InStock,
		&raw.ProductType,
		&raw.BrandId,
		&raw.BrandName,
		&raw.StockItemId,
		&raw.Properties,
		&raw.Popularity,
		&raw.BoostingPoint,
		&raw.PromotionId,
		&raw.MaxOrderQuantity,
		&raw.ShippingCategoryId,
		&raw.AvailableOn,
		&raw.Locale,
	)
	if err != nil {
		return nil, err
	}

	p := &Product{}
	p.Id = raw.Id
	p.Name = raw.ProductName()
	p.Description = raw.ProductDescription()
	p.VariantId = raw.VariantId
	p.Sku = raw.ProductSku()
	p.IsMaster = raw.ProductIsMaster()
	p.TrackInventory = raw.ProductTrackInventory()
	p.InStock = raw.ProductInStock()
	p.ProductType = raw.ProductProductType()
	p.BrandId, p.BrandName = raw.ProductBrand()
	p.StockItemId = raw.StockItemId
	p.SellNatural = raw.ProductSellNatural()
	p.AverageWeight = raw.ProductAverageWeight()
	p.UnitPieces = raw.ProductUnitPieces()
	p.SupermarketUnit = raw.ProductSupermarketUnit()
	p.NaturalUnit = raw.ProductNaturalUnit()
	p.Size_ = raw.ProductSize()
	p.Popularity = raw.ProductPopularity()
	p.PromotionId = raw.ProductPromotionId()
	p.BoostingPoint = raw.ProductBoostingPoint()
	p.MaxOrderQuantity = raw.ProductMaxOrderQuantity()
	p.ShippingCategoryId = raw.ProductShippingCategoryID()
	p.AvailableOn = raw.ProductAvailableOn()
	p.Locale = raw.Locale

	return p, nil
}

func init() {
	queries, err := getQueries("/resources/sql/product.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		products = chacha.NewGetter("products",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanProduct, 3*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
