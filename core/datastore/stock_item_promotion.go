package datastore

import (
	"bytes"
	"context"
	"database/sql"
	"time"

	"github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. stock_item_promotion.proto

var (
	stockItemPromotions chacha.Getter
)

func StockItemPromotionByStoreAndTypes(ctx context.Context, storeId int, promoTypes ...string) (*StockItemPromotions, error) {
	buf := &bytes.Buffer{}
	buf.WriteString("{")
	for i, promoType := range promoTypes {
		if i > 0 {
			buf.WriteString(",")
		}
		buf.WriteString(promoType)
	}
	buf.WriteString("}")

	s, err := getStockItemPromotion(ctx, "stock-item-promotion-by-store-id-and-types", storeId, buf.String())
	if err != nil {
		return nil, err
	}

	return s, nil
}

func StockItemPromotionByStoreAndIsExclusive(ctx context.Context, storeId int, isExclusive bool) (*StockItemPromotions, error) {
	s, err := getStockItemPromotion(ctx, "stock-item-promotion-by-store-id-and-is-exclusive", storeId, isExclusive)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func StockItemPromotionByStoreIDAndSKU(ctx context.Context, storeID int64, sku string) (*StockItemPromotions, error) {
	s, err := getStockItemPromotion(ctx, "stock-item-promotion-by-store-id-sku", storeID, sku)
	if err != nil {
		return nil, err
	}

	return s, nil
}

func StockItemPromotionByStoreIDAndSKUs(ctx context.Context, storeID int64, skus ...string) (map[string]*StockItemPromotions, error) {
	result := make([]proto.Message, len(skus))
	keys := make([]string, len(skus))

	for i, sku := range skus {
		k, err := MarshalKey("stock-item-promotion-by-store-id-sku", storeID, sku)
		if err != nil {
			return nil, err
		}

		keys[i] = k
		result[i] = proto.Message(&StockItemPromotions{})
	}

	err := stockItemPromotions.GetMany(ctx, keys, result)
	if err != nil {
		return nil, err
	}

	promotions := map[string]*StockItemPromotions{}
	for i, v := range result {
		p := v.(*StockItemPromotions)
		if p == nil || len(p.StockItemPromotions) == 0 {
			continue
		}

		promotions[skus[i]] = p
	}

	return promotions, nil
}

func StockItemPromotionBySKUAndStoreIDs(ctx context.Context, sku string, storeIDs ...int64) (map[int64]*StockItemPromotions, error) {
	result := make([]proto.Message, len(storeIDs))
	keys := make([]string, len(storeIDs))

	for i, storeID := range storeIDs {
		k, err := MarshalKey("stock-item-promotion-by-store-id-sku", storeID, sku)
		if err != nil {
			return nil, err
		}

		keys[i] = k
		result[i] = proto.Message(&StockItemPromotions{})
	}

	err := stockItemPromotions.GetMany(ctx, keys, result)
	if err != nil {
		return nil, err
	}

	promotions := map[int64]*StockItemPromotions{}
	for i, v := range result {
		p := v.(*StockItemPromotions)
		if p == nil || len(p.StockItemPromotions) == 0 {
			continue
		}

		promotions[storeIDs[i]] = p
	}

	return promotions, nil
}

func getStockItemPromotion(ctx context.Context, name string, args ...interface{}) (*StockItemPromotions, error) {
	c := &StockItemPromotions{}
	key, err := MarshalKey(name, args...)
	if err != nil {
		log.For("datastore", "StockItemPromotion").Errorf(err.Error())
		return nil, err
	}

	err = stockItemPromotions.Get(ctx, key, c)
	if err != nil {
		log.For("datastore", "StockItemPromotion").Errorf(err.Error())
		return nil, err
	}

	return c, nil
}

func scanStockItemPromotions(r *sql.Rows) (proto.Message, error) {
	stockItemPromotions := []*StockItemPromotion{}

	for r.Next() {
		stockItemPromotion := &StockItemPromotion{}
		err := r.Scan(
			&stockItemPromotion.Id,
			&stockItemPromotion.StoreId,
			&stockItemPromotion.Sku,
			&stockItemPromotion.Type,
			&stockItemPromotion.BuyXQuantity,
			&stockItemPromotion.GetYQuantity,
			&stockItemPromotion.PromotionId,
			&stockItemPromotion.GetPPercent,
		)
		if err != nil {
			return nil, err
		}

		stockItemPromotions = append(stockItemPromotions, stockItemPromotion)
	}

	return &StockItemPromotions{StockItemPromotions: stockItemPromotions}, nil
}

func init() {
	queries, err := getQueries("/resources/sql/stock_item_promotion.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		stockItemPromotions = chacha.NewGetter("stock:item:promotions",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowsScanner(scanStockItemPromotions, time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
