package datastore

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStockLocationByID(t *testing.T) {
	testCases := []struct {
		name                  string
		stockLocationID       int
		hasError              bool
		expectedStockLocation *StockLocation
	}{
		{"With stock location id", 3, false, &StockLocation{Id: 3, StoreId: 1003, CountryCode: "ID", SupplierId: 0}},
		{"With invalid stock location id", 33, true, nil},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()

			sl, err := StockLocationByID(ctx, testCase.stockLocationID)

			assert := assert.New(t)

			if testCase.hasError {
				assert.Error(err)
				return
			}

			assert.NoError(err)
			assert.Equal(testCase.expectedStockLocation, sl)
		})
	}
}
