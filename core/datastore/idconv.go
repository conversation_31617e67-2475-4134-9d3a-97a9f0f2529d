package datastore

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. idconv.proto

var (
	idConvGetter chacha.Getter
)

// TODO: Unit test
func GlobalIDsByLegacyIDs(ctx context.Context, entity string, legacyIDs []string) ([]string, error) {
	result := make([]proto.Message, len(legacyIDs))
	keys := make([]string, len(legacyIDs))

	for i, id := range legacyIDs {
		e, err := strconv.Atoi(id)
		if err != nil {
			return nil, err
		}

		k, err := MarshalKey(fmt.Sprintf("global-id-%s", entity), e)
		if err != nil {
			return nil, err
		}

		keys[i] = k
		result[i] = proto.Message(&Global{})
	}

	err := idConvGetter.GetMany(ctx, keys, result)
	if err != nil {
		return nil, err
	}

	ids := make([]string, len(legacyIDs))
	for i, r := range result {
		g, ok := r.(*Global)
		if !ok || g.ID == 0 {
			ids[i] = "-1"
			continue
		}

		ids[i] = strconv.FormatInt(g.ID, 10)
	}

	return ids, nil
}

func scanIDConv(r *sql.Row) (proto.Message, error) {
	g := &Global{}
	err := r.Scan(
		&g.ID,
	)
	if err != nil {
		return nil, err
	}

	return g, nil
}

func init() {
	queries, err := getQueries("/resources/sql/idconv.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		idConvGetter = chacha.NewGetter("idconv",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanIDConv, 30*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
