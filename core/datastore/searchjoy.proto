syntax = "proto3";

package datastore;

message SearchjoyKeyword {
    int64 id = 1;
    string normalized_query = 2;
    int64 conversions = 3;
    int64 occurences = 4;
    string country_iso = 5;
    string locale = 6;
    int64 stock_location_id = 7;
}

message SearchjoyKeywords {
    repeated SearchjoyKeyword keywords = 1;
}

message SearchjoyRecord {
    string query = 1;
    string normalized_query = 2;
    string country_iso = 3;
    string locale = 4;
    int64 stock_location_id = 5;
    int64 user_id = 6;
    string created_at = 7;
    string external_search_id = 8;
    map<string, string> properties = 9;
}
