package datastore

import (
	"context"
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestProductTypeBySKU(t *testing.T) {
	for _, tc := range []struct {
		name         string
		sku          string
		expectNil    bool
		expectHasErr bool
		expected     int
	}{
		{"returns correct data", "SKU01", false, false, 9101},
		{"returns error", "SKU06", true, true, -1},
	} {
		t.Run(tc.name, func(t *testing.T) {
			pty, err := ProductTypeBySKU(context.Background(), tc.sku, "ID")
			if err != nil && !tc.expectHasErr {
				t.<PERSON><PERSON>rf("\nexpectation: %v\nreality: %v", nil, err)
			}

			if tc.expectNil != (pty == nil) {
				t.<PERSON><PERSON>rf("\nexpectation: %v\nreality: %v", tc.expectNil, (pty == nil))
			}

			if tc.expectNil {
				return
			}

			if pty.Id != int64(tc.expected) {
				t.<PERSON><PERSON><PERSON>("\nexpectation: %v\nreality: %v", tc.expected, pty.Id)
			}
		})
	}
}

func TestProductTypeByProductID(t *testing.T) {
	testCases := []struct {
		name              string
		productID         int
		countryISO        string
		expectHasErr      bool
		expectedID        int64
		expectedName      string
		expectedNameLocal string
	}{
		{
			"returns correct data",
			1,
			"ID",
			false,
			9101,
			"PTY-01-01-EN",
			"PTY-01-01-ID",
		},
		{
			"returns correct data - country TH",
			4,
			"TH",
			false,
			9202,
			"PTY-02-02-EN",
			"PTY-02-02-TH",
		},
		{
			"returns correct data - country lowercase",
			4,
			"th",
			false,
			9202,
			"PTY-02-02-EN",
			"PTY-02-02-TH",
		},
		{
			"returns no data for product with no product type",
			5,
			"id",
			true,
			0,
			"",
			"",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			assert := assert.New(t)

			pty, err := ProductTypeByProductID(context.Background(), testCase.productID, testCase.countryISO)
			assert.True(err == nil || testCase.expectHasErr)

			if err != nil {
				return
			}

			assert.Equal(testCase.expectedID, pty.Id)
			assert.Equal(testCase.expectedName, pty.Name)
			assert.Equal(testCase.expectedNameLocal, pty.NameLocal)
		})
	}
}

func TestProductTypesByProductID(t *testing.T) {
	for _, tc := range []struct {
		name               string
		productID          int
		countryISO         string
		expectNil          bool
		expectHasErr       bool
		expectedIDs        []int
		expectedNames      []string
		expectedNameLocals []string
	}{
		{
			"returns correct data",
			94, "ID", false, false,
			[]int{9202, 9200},
			[]string{"PTY-02-02-EN", "PTY-02-EN"},
			[]string{"PTY-02-02-ID", "PTY-02-ID"},
		},
		{
			"returns correct data - country TH",
			94, "TH", false, false,
			[]int{9202, 9200},
			[]string{"PTY-02-02-EN", "PTY-02-EN"},
			[]string{"PTY-02-02-TH"},
		},
		{
			"returns correct data - country lowercase",
			94, "th", false, false,
			[]int{9202, 9200},
			[]string{"PTY-02-02-EN", "PTY-02-EN"},
			[]string{"PTY-02-02-TH"},
		},
		{
			"returns no data for product with no product type",
			95, "id", true, true,
			nil,
			nil,
			nil,
		},
		{
			"returns error",
			9900, "id", true, true,
			nil,
			nil,
			nil,
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ptys, err := ProductTypesByProductID(context.Background(), tc.productID, tc.countryISO)
			if err != nil && !tc.expectHasErr {
				t.Errorf("\nexpectation: %v\nreality: %v", nil, err)
			}

			if tc.expectNil != (ptys == nil || len(ptys) == 0) {
				t.Errorf("\nexpectation: %v\nreality: %v", tc.expectNil, (ptys == nil))
			}

			if tc.expectNil {
				return
			}

			resPtyIDs := make([]int, 0, len(ptys))
			resPtyNames := make([]string, 0, len(ptys))
			resPtyNameLocals := make([]string, 0, len(ptys))
			for _, pty := range ptys {
				if pty.Id > 0 {
					resPtyIDs = append(resPtyIDs, int(pty.Id))
				}

				if len(pty.Name) > 0 {
					resPtyNames = append(resPtyNames, pty.Name)
				}

				if len(pty.NameLocal) > 0 {
					resPtyNameLocals = append(resPtyNameLocals, pty.NameLocal)
				}
			}

			if !arrayIntEqual(tc.expectedIDs, resPtyIDs) {
				t.Errorf("expectation: %v; reality: %v", tc.expectedIDs, resPtyIDs)
			}

			if !arrayStringEqual(tc.expectedNames, resPtyNames) {
				t.Errorf("expectation: %v; reality: %v", tc.expectedNames, resPtyNames)
			}

			if !arrayStringEqual(tc.expectedNameLocals, resPtyNameLocals) {
				t.Errorf("expectation: %v; reality: %v", tc.expectedNameLocals, resPtyNameLocals)
			}
		})
	}
}

func TestProductTypesByProductIDs(t *testing.T) {
	testCases := []struct {
		name                  string
		productIDs            []int64
		countryISO            string
		expectHasErr          bool
		expectedIDsMap        map[int64][]int64
		expectedNamesMap      map[int64][]string
		expectedNameLocalsMap map[int64][]string
	}{
		{
			"returns correct data",
			[]int64{94},
			"ID",
			false,
			map[int64][]int64{94: {9200, 9202}},
			map[int64][]string{94: {"PTY-02-02-EN", "PTY-02-EN"}},
			map[int64][]string{94: {"PTY-02-02-ID", "PTY-02-ID"}},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			assert := assert.New(t)

			ptysMap, err := ProductTypesByProductIDs(context.Background(), testCase.productIDs, testCase.countryISO)
			assert.True(err == nil || testCase.expectHasErr)

			if err != nil {
				return
			}

			ptyIDsMap := make(map[int64][]int64)
			ptyNamesMap := make(map[int64][]string)
			ptyNameLocalsMap := make(map[int64][]string)

			for productID, ptys := range ptysMap {
				ptyIDs := make([]int64, len(ptys))
				ptyNames := make([]string, len(ptys))
				ptyNameLocals := make([]string, len(ptys))

				for i, pty := range ptys {
					if pty.Id > 0 {
						ptyIDs[i] = pty.Id
					}

					if len(pty.Name) > 0 {
						ptyNames[i] = pty.Name
					}

					if len(pty.NameLocal) > 0 {
						ptyNameLocals[i] = pty.NameLocal
					}
				}

				sort.SliceStable(ptyIDs, func(i, j int) bool { return ptyIDs[i] < ptyIDs[j] })
				ptyIDsMap[productID] = ptyIDs
				sort.SliceStable(ptyNames, func(i, j int) bool { return ptyNames[i] < ptyNames[j] })
				ptyNamesMap[productID] = ptyNames
				sort.SliceStable(ptyNameLocals, func(i, j int) bool { return ptyNameLocals[i] < ptyNameLocals[j] })
				ptyNameLocalsMap[productID] = ptyNameLocals
			}

			assert.Equal(testCase.expectedIDsMap, ptyIDsMap)
			assert.Equal(testCase.expectedNamesMap, ptyNamesMap)
			assert.Equal(testCase.expectedNameLocalsMap, ptyNameLocalsMap)
		})
	}
}
