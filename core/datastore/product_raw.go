package datastore

import (
	"bytes"
	"database/sql"
	fmt "fmt"
	"strconv"

	"github.com/lib/pq/hstore"
	"happyfresh.io/lib/str"
)

type ProductRaw struct {
	Id                 int64
	Name               sql.NullString
	NameLocal          sql.NullString
	Description        sql.NullString
	DescriptionLocal   sql.NullString
	VariantId          int64
	Sku                sql.NullString
	IsMaster           sql.NullBool
	TrackInventory     sql.NullBool
	InStock            sql.NullBool
	ProductType        sql.NullString
	BrandId            sql.NullInt64
	BrandName          sql.NullString
	StockItemId        int64
	Properties         hstore.Hstore
	Popularity         sql.NullFloat64
	BoostingPoint      sql.NullFloat64
	PromotionId        sql.NullInt64
	MaxOrderQuantity   int64
	ShippingCategoryId sql.NullInt64
	AvailableOn        sql.NullString
	Locale             string
}

func (r *ProductRaw) ProductName() (name string) {
	if r.NameLocal.Valid {
		name = r.NameLocal.String
	} else if r.Name.Valid {
		name = r.Name.String
	}

	return
}

func (r *ProductRaw) ProductDescription() (description string) {
	if r.DescriptionLocal.Valid {
		description = r.DescriptionLocal.String
	} else if r.Description.Valid {
		description = r.Description.String
	}

	return
}

func (r *ProductRaw) ProductSku() (sku string) {
	if r.Sku.Valid {
		sku = r.Sku.String
	}

	return
}

func (r *ProductRaw) ProductIsMaster() (isMaster bool) {
	if r.IsMaster.Valid {
		isMaster = r.IsMaster.Bool
	}

	return
}

func (r *ProductRaw) ProductTrackInventory() (trackInventory bool) {
	if r.TrackInventory.Valid {
		trackInventory = r.TrackInventory.Bool
	}

	return
}

func (r *ProductRaw) ProductInStock() (inStock bool) {
	if r.InStock.Valid {
		inStock = r.InStock.Bool
	}

	return
}

func (r *ProductRaw) ProductProductType() (productType string) {
	if r.ProductType.Valid {
		productType = r.ProductType.String
	}

	return
}

func (r *ProductRaw) ProductBrand() (id int64, brand string) {
	if r.BrandId.Valid {
		id = r.BrandId.Int64
	}

	if r.BrandName.Valid {
		brand = r.BrandName.String
	}

	return
}

func (r *ProductRaw) ProductSellNatural() (sellNatural string) {
	properties := r.Properties.Map

	if v, ok := properties[propertiesSellNaturalTag]; ok && v.Valid {
		sellNatural = v.String
	}

	return
}

func (r *ProductRaw) ProductAverageWeight() (avgWeight float64) {
	properties := r.Properties.Map

	if v, ok := properties[propertiesAverageWeightTag]; ok && v.Valid {
		avg, err := strconv.ParseFloat(v.String, 64)
		if err == nil {
			avgWeight = float64(avg)
		}
	}

	if avgWeight <= 0 {
		return
	}

	size := r.ProductSize()
	if size <= 0 {
		size = 1
	}

	avgWeight = avgWeight * (1 / size)

	return
}

func (r *ProductRaw) ProductUnitPieces() (unitPieces float64) {
	properties := r.Properties.Map

	if v, ok := properties[propertiesUnitPiecesTag]; ok && v.Valid {
		up, err := strconv.ParseFloat(v.String, 64)
		if err == nil {
			unitPieces = float64(up)
		}
	}

	return
}

func (r *ProductRaw) ProductSupermarketUnit() (supermarketUnit string) {
	properties := r.Properties.Map

	if v, ok := properties[propertiesSupermarketUnitTag]; ok && v.Valid {
		supermarketUnit = v.String
	}

	return
}

func (r *ProductRaw) ProductNaturalUnit() (naturalUnit string) {
	properties := r.Properties.Map

	if v, ok := properties[propertiesNaturalUnitTag]; ok && v.Valid {
		naturalUnit = v.String
	}

	if naturalUnit == "" {
		buf := &bytes.Buffer{}

		if v, ok := properties[propertiesPackageTag]; ok && v.Valid {
			pkg := str.String(v.String).Int()
			if pkg > 1 {
				buf.WriteString(fmt.Sprintf("%d x ", pkg))
			}
		}

		if v, ok := properties[propertiesSizeTag]; ok && v.Valid {
			size := str.String(v.String).Float64()
			if size >= 1 {
				buf.WriteString(fmt.Sprintf("%g ", size))
			}
		}

		if v, ok := properties[propertiesUnitTag]; ok && v.Valid {
			buf.WriteString(v.String)
		}

		naturalUnit = buf.String()
	}

	return
}

func (r *ProductRaw) ProductSize() (size float64) {
	properties := r.Properties.Map

	if v, ok := properties[propertiesSizeTag]; ok && v.Valid {
		sz, err := strconv.ParseFloat(v.String, 64)
		if err == nil {
			size = float64(sz)
		}
	}

	return
}

func (r *ProductRaw) ProductPopularity() (popularity float64) {
	popularity = 0.0
	if r.Popularity.Valid {
		popularity = popularity + r.Popularity.Float64
	}

	if r.BoostingPoint.Valid {
		popularity = popularity + r.BoostingPoint.Float64
	}

	return
}

func (r *ProductRaw) ProductBoostingPoint() (boostingPoint float64) {
	if r.BoostingPoint.Valid {
		boostingPoint = r.BoostingPoint.Float64
	}

	return
}

func (r *ProductRaw) ProductPromotionId() (promotionId int64) {
	if r.PromotionId.Valid {
		promotionId = r.PromotionId.Int64
	}

	return
}

func (s *ProductRaw) ProductMaxOrderQuantity() (maxOrderQty int64) {
	properties := s.Properties.Map
	stockItemMoq := s.MaxOrderQuantity

	if stockItemMoq > 0 {
		maxOrderQty = stockItemMoq
		return
	}

	if v, ok := properties[propertiesMaxOrderQuantity]; ok && v.Valid {
		if moq, err := strconv.Atoi(v.String); err == nil {
			maxOrderQty = int64(moq)
		}
	}

	return
}

func (s *ProductRaw) ProductShippingCategoryID() (shippingCategoryID int64) {
	if s.ShippingCategoryId.Valid {
		shippingCategoryID = s.ShippingCategoryId.Int64
	}

	return
}

func (s *ProductRaw) ProductAvailableOn() (availableOn string) {
	if s.AvailableOn.Valid {
		availableOn = s.AvailableOn.String
	}

	return
}
