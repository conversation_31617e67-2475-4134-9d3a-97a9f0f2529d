package datastore // import "happyfresh.io/search/core/datastore"

import (
	"context"
	"database/sql"
	"strings"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. taxon.proto

var (
	taxon  chacha.Getter
	taxons chacha.Getter
)

// TaxonByID get Taxon name by ID
func TaxonByID(ctx context.Context, id int) (*Taxon, error) {
	c := &Taxon{}
	key, err := MarshalKey("taxon-name-by-taxon-id", id)
	if err != nil {
		return nil, err
	}

	err = taxon.Get(ctx, key, c)
	if err != nil {
		return nil, err
	}

	return c, nil
}

func TaxonsByProductID(ctx context.Context, productID int, countryISO string) ([]*Taxon, error) {
	key, err := MarshalKey("taxon-by-product-id", productID, strings.ToLower(countryISO))
	if err != nil {
		return nil, err
	}

	c := &Taxons{}
	err = taxons.Get(ctx, key, c)
	if err != nil {
		return nil, err
	}

	return c.Taxons, nil
}

func TaxonsByProductIDs(ctx context.Context, productIDs []int64, locale string) (map[int64][]*Taxon, error) {
	keys := make([]string, len(productIDs))
	results := make([]proto.Message, len(productIDs))
	for i, id := range productIDs {
		k, _ := MarshalKey("taxon-by-product-id", id, strings.ToLower(locale))
		keys[i] = k
		results[i] = &Taxons{}
	}

	err := taxons.GetMany(ctx, keys, results)
	if err != nil {
		return nil, err
	}

	out := make(map[int64][]*Taxon)
	for i, id := range productIDs {
		r, ok := results[i].(*Taxons)
		if !ok || r.Taxons == nil {
			out[id] = []*Taxon{}
		}

		out[id] = r.Taxons
	}

	return out, nil
}

func scanTaxon(r *sql.Row) (proto.Message, error) {
	taxon := &Taxon{}
	err := r.Scan(
		&taxon.Id,
		&taxon.Name,
	)
	if err != nil {
		return nil, err
	}

	return taxon, nil
}

func scanTaxons(r *sql.Rows) (proto.Message, error) {
	taxons := []*Taxon{}
	for r.Next() {
		var taxonName sql.NullString
		var taxonNameLocale sql.NullString
		taxon := &Taxon{}
		if err := r.Scan(
			&taxon.Id,
			&taxonName,
			&taxonNameLocale,
		); err != nil {
			return nil, err
		}

		if taxonName.Valid {
			taxon.Name = taxonName.String
		}

		if taxonNameLocale.Valid {
			taxon.NameLocale = taxonNameLocale.String
		}

		taxons = append(taxons, taxon)
	}

	return &Taxons{Taxons: taxons}, nil
}

func init() {
	queries, err := getQueries("/resources/sql/taxon.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		taxon = chacha.NewGetter("taxon",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanTaxon, 24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)

		taxons = chacha.NewGetter("taxons",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowsScanner(scanTaxons, 24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
