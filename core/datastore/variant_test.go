package datastore

import (
	"context"
	"sort"
	"testing"
)

func TestVariantsByPromotionDetailAndStockLocationID(t *testing.T) {
	getPromotion := func(id int) *Promotion {
		p, err := PromotionByID(context.Background(), id)
		if err != nil {
			return nil
		}

		return p
	}

	for _, tc := range []struct {
		name            string
		promotionID     int
		stockLocationID int
		expect          []int
	}{
		{"Any Product Any Taxon", 6, 3, []int{91, 93, 94}},
		{"Any Product None Taxon", 7, 3, []int{91, 92, 93}},
		{"None Product Any Taxon", 8, 3, []int{92, 93, 94}},
		{"None Product None Taxon", 9, 3, []int{92, 93}},
		{"Any Product", 2, 3, []int{92}},
		{"None Product", 5, 3, []int{91, 93, 94}},
		{"Any Taxon", 1, 3, []int{91, 93}},
		{"None Taxon", 4, 3, []int{91, 92, 93}},
		{"Any Product Empty Stock Location", 3, 3, []int{94}},
	} {
		promotion := getPromotion(tc.promotionID)
		if promotion == nil {
			t.Errorf("expectation: not nil; reality: %v", promotion)
		}

		results, err := VariantsByPromotionDetailAndStockLocationID(context.Background(), promotion, tc.stockLocationID)
		if err != nil {
			t.Error(err)
		}

		resultIDs := []int{}
		for _, result := range results.Variants {
			resultIDs = append(resultIDs, int(result.Id))
		}

		sort.Ints(resultIDs)
		sort.Ints(tc.expect)

		if !arrayEqual(tc.expect, resultIDs) {
			t.Errorf("expectation: %v\nreality: %v", tc.expect, resultIDs)
		}
	}
}

func arrayEqual(a, b []int) bool {
	if (a == nil) != (b == nil) {
		return false
	}

	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}

	return true
}
