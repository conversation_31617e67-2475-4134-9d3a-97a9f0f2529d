package datastore

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStockItemPromotionByStoreAndTypes(t *testing.T) {
	testCases := []struct {
		name            string
		storeID         int
		promoTypes      []string
		stockItemLength int
	}{
		{"With stock item result", 441, []string{"discount", "free_shipping", "buy_x_get_y"}, 1},
		{"With multiple stock item result", 1003, []string{"discount", "free_shipping", "buy_x_get_y"}, 5},
		{"With no matching stock items", 443, []string{"discount", "free_shipping", "buy_x_get_y"}, 0},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()

			sips, err := StockItemPromotionByStoreAndTypes(ctx, testCase.storeID, testCase.promoTypes...)

			assert := assert.New(t)
			assert.NoError(err)
			assert.Equal(testCase.stockItemLength, len(sips.StockItemPromotions))
		})
	}
}

func TestStockItemPromotionByStoreAndIsExclusive(t *testing.T) {
	testCases := []struct {
		name                 string
		storeID              int
		isExclusive          bool
		expectedProductCodes []string
	}{
		{"With stock item result", 1992, true, []string{"wow1", "wow2", "wow3"}},
		{"With no stock item result", 1992, false, []string{}},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()
			productCodes := []string{}
			sips, err := StockItemPromotionByStoreAndIsExclusive(ctx, testCase.storeID, testCase.isExclusive)

			for _, s := range sips.StockItemPromotions {
				productCodes = append(productCodes, s.Sku)
			}
			assert := assert.New(t)
			assert.NoError(err)
			assert.Equal(len(testCase.expectedProductCodes), len(productCodes))
			assert.Equal(testCase.expectedProductCodes, productCodes)
		})
	}
}

func TestStockItemPromotionByStoreIDAndSKU(t *testing.T) {
	testCases := []struct {
		name            string
		storeID         int64
		sku             string
		stockItemLength int
	}{
		{"With stock item result", 1003, "SKU01", 2},
		{"With no matching stock items", 1003, "SKU05", 0},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()

			sips, err := StockItemPromotionByStoreIDAndSKU(ctx, testCase.storeID, testCase.sku)

			assert := assert.New(t)
			assert.NoError(err)
			assert.Equal(testCase.stockItemLength, len(sips.StockItemPromotions))
		})
	}
}

func TestStockItemPromotionByStoreIDAndSKUs(t *testing.T) {
	testCases := []struct {
		name               string
		storeID            int64
		skus               []string
		mapStockItemLength map[string]int
	}{
		{"With stock item result", 1003, []string{"SKU01"}, map[string]int{"SKU01": 2}},
		{"With no matching stock items", 1003, []string{"SKU05"}, map[string]int{"SKU05": 0}},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()

			mapSIPs, err := StockItemPromotionByStoreIDAndSKUs(ctx, testCase.storeID, testCase.skus...)

			assert := assert.New(t)
			assert.NoError(err)

			for sku, stockItemLength := range testCase.mapStockItemLength {
				sips, ok := mapSIPs[sku]

				if stockItemLength > 0 {
					if assert.True(ok) {
						assert.Equal(stockItemLength, len(sips.StockItemPromotions))
					}
				} else {
					assert.False(ok)
				}
			}
		})
	}
}

func TestStockItemPromotionBySKUAndStoreIDs(t *testing.T) {
	testCases := []struct {
		name               string
		storeIDs           []int64
		sku                string
		mapStockItemLength map[int64]int
	}{
		{"With stock item result", []int64{1003}, "SKU01", map[int64]int{1003: 2}},
		{"With no matching stock items", []int64{1003}, "SKU05", map[int64]int{1003: 0}},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()

			mapSIPs, err := StockItemPromotionBySKUAndStoreIDs(ctx, testCase.sku, testCase.storeIDs...)

			assert := assert.New(t)
			assert.NoError(err)

			for sku, stockItemLength := range testCase.mapStockItemLength {
				sips, ok := mapSIPs[sku]

				if stockItemLength > 0 {
					if assert.True(ok) {
						assert.Equal(stockItemLength, len(sips.StockItemPromotions))
					}
				} else {
					assert.False(ok)
				}
			}
		})
	}
}
