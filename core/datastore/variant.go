package datastore

import (
	"bytes"
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. variant.proto

var variants chacha.Getter

func VariantsByPromotionDetailAndStockLocationID(ctx context.Context, promotion *Promotion, stockLocationID int) (*Variants, error) {
	toSQLArrayInt := func(ns []int64) string {
		buf := &bytes.Buffer{}
		buf.WriteString("{")
		for i, n := range ns {
			if i > 0 {
				buf.WriteString(",")
			}
			buf.WriteString(strconv.FormatInt(n, 10))
		}
		buf.WriteString("}")

		return buf.String()
	}
	productIDsSQL := toSQLArrayInt(promotion.ProductRules)
	taxonIDsSQL := toSQLArrayInt(promotion.TaxonRules)

	var stockLocationIDsSQL string
	if len(promotion.StockLocationRules) > 0 {
		stockLocationIDsSQL = toSQLArrayInt(promotion.StockLocationRules)
	} else {
		stockLocationIDsSQL = toSQLArrayInt([]int64{int64(stockLocationID)})
	}

	querySuffix := &strings.Builder{}
	if promotion.StockLocationRulesMatchPolicy == "none" {
		querySuffix.WriteString("none-")
	}
	querySuffix.WriteString("stock-location-id-")

	var args []interface{}
	if len(promotion.ProductRulesMatchPolicy) <= 0 {
		querySuffix.WriteString(fmt.Sprintf("%s-taxon", promotion.TaxonRulesMatchPolicy))
		args = []interface{}{stockLocationID, taxonIDsSQL, stockLocationIDsSQL}
	} else if len(promotion.TaxonRulesMatchPolicy) <= 0 {
		querySuffix.WriteString(fmt.Sprintf("%s-product", promotion.ProductRulesMatchPolicy))
		args = []interface{}{stockLocationID, productIDsSQL, stockLocationIDsSQL}
	} else {
		querySuffix.WriteString(fmt.Sprintf("%s-product-%s-taxon", promotion.ProductRulesMatchPolicy, promotion.TaxonRulesMatchPolicy))
		args = []interface{}{stockLocationID, productIDsSQL, taxonIDsSQL, stockLocationIDsSQL}
	}

	key, err := MarshalKey(fmt.Sprintf("variants-by-promotion-details-%s", querySuffix.String()), args...)
	if err != nil {
		return nil, err
	}

	vs := &Variants{}
	err = variants.Get(ctx, key, vs)
	if err != nil {
		return nil, err
	}

	return vs, nil
}

func scanVariants(r *sql.Rows) (proto.Message, error) {
	variants := &Variants{}
	for r.Next() {
		v := &Variant{}
		err := r.Scan(
			&v.Id,
		)
		if err != nil {
			return nil, err
		}

		variants.Variants = append(variants.Variants, v)
	}

	return variants, nil
}

func init() {
	queries, err := getQueries("/resources/sql/variant.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		variants = chacha.NewGetter("variants",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowsScanner(scanVariants, 24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
