package datastore

import (
	"context"
	"database/sql"
	"testing"

	"github.com/lib/pq/hstore"
	"github.com/stretchr/testify/assert"
	"happyfresh.io/lib/str"
)

func TestProductByID(t *testing.T) {
	for _, tc := range []struct {
		name                string
		productIDParam      int64
		localeParam         string
		expectError         bool
		expectedProductID   int64
		expectedProductName string
	}{
		{
			"Returns relevant product",
			91,
			"en",
			false,
			91,
			"P01-EN",
		},
		{
			"Returns relevant product, localized",
			91,
			"id",
			false,
			91,
			"P01-ID",
		},
		{
			"Returns error on not found",
			1,
			"id",
			true,
			0,
			"",
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := ProductByID(ctx, tc.localeParam, 3, tc.productIDParam)
			assert.Equal(t, tc.expectError, err != nil)
			if tc.expectError {
				return
			}

			assert.Equal(t, tc.expectedProductID, result.Id)
			assert.Equal(t, tc.expectedProductName, result.Name)
		})
	}
}

func TestProductUnitPriceFactor(t *testing.T) {
	c := func(supermarketUnit, unitPieces, size, avgWeight string) hstore.Hstore {
		return hstore.Hstore{
			Map: map[string]sql.NullString{
				propertiesSupermarketUnitTag: {String: supermarketUnit, Valid: true},
				propertiesUnitPiecesTag:      {String: unitPieces, Valid: true},
				propertiesSizeTag:            {String: size, Valid: true},
				propertiesAverageWeightTag:   {String: avgWeight, Valid: true},
			},
		}
	}

	tenKTest := func(factor, p float64) bool {
		return p/factor == 10000.0
	}

	tests := []struct {
		name     string
		fields   hstore.Hstore
		want     float64
		tenKTest float64
	}{
		{"Valid Unit 10K Price => 1 500g", c("g", "1", "500", ""), 0.2, 2e3},
		{"Valid Unit 10K Price => 1 25g", c("g", "1", "25", ""), 4, 4e4},
		{"Valid Unit 10K Price => 10 40ml", c("ml", "10", "40", ""), 0.25, 2500},
		{"Valid Unit 10K Price => 10 5ml", c("ml", "10", "5", ""), 2, 2e4},
		{"Valid Unit 10K Price => 10 pack", c("pack", "1", "10", ""), 0.10, 1e3},
		{"Valid Unit 10K Price => 1 each", c("each", "1", "1", ""), 1, 1e4},
		{"Valid Unit 10K Price => 1 kg", c("kg", "1", "1", ""), 0.1, 1e3},
		{"Valid Unit 10K Price => 1 L", c("l", "1", "1", ""), 0.1, 1e3},
		{"Invalid Unit => 1 bundle", c("bundle", "", "", ""), 1, 1e4},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				SupermarketUnit: tt.fields.Map[propertiesSupermarketUnitTag].String,
				UnitPieces:      str.MaybeString(tt.fields.Map[propertiesUnitPiecesTag].String).Float64(),
				Size_:           str.MaybeString(tt.fields.Map[propertiesSizeTag].String).Float64(),
				AverageWeight:   str.MaybeString(tt.fields.Map[propertiesAverageWeightTag].String).Float64(),
			}

			if got := p.ProductUnitPriceFactor(); got != tt.want {
				t.Errorf("UnitPriceFactor:\nexpectation: %.2f\nreality: %.2f", tt.want, got)
			}

			if !tenKTest(p.ProductUnitPriceFactor(), tt.tenKTest) {
				t.Errorf("With 10.000 Price it should display %.2f", tt.tenKTest)
			}
		})
	}
}
