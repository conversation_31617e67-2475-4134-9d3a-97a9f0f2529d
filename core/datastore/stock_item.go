package datastore

import (
	"context"
	"database/sql"
	fmt "fmt"
	"strconv"
	"strings"
	"time"

	"github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=.  stock_item.proto

var (
	stockitems     chacha.Getter
	stockItemsRead chacha.Getter
)

// StockItemByID queries a stock item data from spree_stock_items and related tables.
// Uses 1m cache expiration. Used in product and stock item indexing.
func StockItemByID(ctx context.Context, iso string, id int64) (*StockItem, error) {
	countryISO := strings.ToLower(iso)
	s, err := getStockItem(ctx, stockitems, "stock-item-by-id", countryISO, id, countryISO)
	if err != nil {
		return nil, err
	}

	s.CountryIso = iso
	return s, nil
}

func StockItemsByIDs(ctx context.Context, locale string, ids []int) ([]*StockItem, error) {
	stockItems := make([]*StockItem, 0, len(ids))
	for _, id := range ids {
		s, err := getStockItem(ctx, stockItemsRead, "stock-items-by-ids", strings.ToLower(locale), id)
		if err != nil {
			return nil, err
		}

		stockItems = append(stockItems, s)
	}

	return stockItems, nil
}

func GetStockItemByIDs(ctx context.Context, iso string, ids []int64, locale string) ([]*StockItem, error) {
	keys := make([]string, 0, len(ids))
	results := make([]proto.Message, 0, len(ids))
	for _, id := range ids {
		k, err := MarshalKey("stock-item-by-id", strings.ToLower(iso), id, strings.ToLower(locale))
		if err != nil {
			continue
		}

		keys = append(keys, k)
		results = append(results, &StockItem{})
	}

	err := stockItemsRead.GetMany(ctx, keys, results)
	if err != nil {
		return nil, err
	}

	stockItems := make([]*StockItem, len(results))
	for i, r := range results {
		stockItems[i] = r.(*StockItem)
	}

	return stockItems, nil
}

func (s *StockItem) DisplayAvgWeight() string {
	return fmt.Sprintf("~ %s %s", strconv.FormatFloat(float64(s.AvgWeight), 'f', -1, 64), s.SupermarketUnit)
}

func (s *StockItem) UnitPriceFactor() float64 {
	unitPieces := float64(s.UnitPieces)
	naturalAvgWeight := float64(s.AvgWeight)
	size := float64(s.Size_)

	factor := 1.0

	if unitPieces > 0 {
		factor = factor / unitPieces
	}

	if naturalAvgWeight > 0 {
		factor = factor / naturalAvgWeight
	} else if size > 0 {
		factor = factor / size
	}

	switch strings.ToLower(strings.TrimSpace(s.SupermarketUnit)) {
	case "kg", "l":
		factor = factor / 1000
		fallthrough
	case "ml", "g":
		factor = factor * 100
	case "packet", "each", "pack":
		// Do Nothing
	default:
		factor = 1.0
	}

	return factor
}

func getStockItem(ctx context.Context, getter chacha.Getter, name string, args ...interface{}) (*StockItem, error) {
	c := &StockItem{}
	key, err := MarshalKey(name, args...)
	if err != nil {
		return nil, err
	}

	err = getter.Get(ctx, key, c)
	if err != nil {
		return nil, err
	}

	taxons, err := TaxonsByProductID(ctx, int(c.ProductId), c.CountryIso)
	if err != nil {
		return nil, err
	}

	resTaxonIDs := make([]int64, 0, len(taxons))
	resTaxonNames := make([]string, 0, len(taxons))
	resTaxonNameLocals := make([]string, 0, len(taxons))
	for _, taxn := range taxons {
		if taxn.Id > 0 {
			resTaxonIDs = append(resTaxonIDs, taxn.Id)
		}

		if len(taxn.Name) > 0 {
			resTaxonNames = append(resTaxonNames, taxn.Name)
		}

		if len(taxn.NameLocale) > 0 {
			resTaxonNameLocals = append(resTaxonNameLocals, taxn.NameLocale)
		}
	}

	c.TaxonIds = resTaxonIDs
	c.TaxonName = resTaxonNames
	c.TaxonNameLocal = resTaxonNameLocals

	ptys, err := ProductTypesByProductID(ctx, int(c.ProductId), c.CountryIso)
	if err != nil {
		return nil, err
	}

	resPtyIDs := make([]int64, 0, len(ptys))
	resPtyNames := make([]string, 0, len(ptys))
	resPtyNameLocals := make([]string, 0, len(ptys))
	for _, pty := range ptys {
		if pty.Id > 0 {
			resPtyIDs = append(resPtyIDs, pty.Id)
		}

		if len(pty.Name) > 0 {
			resPtyNames = append(resPtyNames, pty.Name)
		}

		if len(pty.NameLocal) > 0 {
			resPtyNameLocals = append(resPtyNameLocals, pty.NameLocal)
		}
	}

	c.ProductTypeIds = resPtyIDs
	c.ProductTypeName = resPtyNames
	c.ProductTypeNameLocal = resPtyNameLocals

	return c, nil
}

func scanStockItem(r *sql.Row) (proto.Message, error) {
	s := &RawStockItem{}
	err := r.Scan(
		&s.RawID,
		&s.RawSku,
		&s.RawName,
		&s.RawNameLocal,
		&s.RawDescription,
		&s.RawDescriptionLocal,
		&s.RawStoreID,
		&s.RawInStock,
		&s.RawPrice,
		&s.RawNormalPrice,
		&s.RawCost,
		&s.RawNormalCost,
		&s.RawOriginalPromoCost,
		&s.RawClientTypePromoPrice,
		&s.RawPopularity,
		&s.RawBoostingPoint,
		&s.RawPromotionID,
		&s.RawStoreLocation,
		&s.RawVariantID,
		&s.RawProductID,
		&s.RawProductTypePath,
		&s.RawBrandID,
		&s.RawBrandName,
		&s.RawBrandNameLocal,
		&s.RawProperties,
		&s.RawCountryISO,
		&s.RawStockLocationID,
		&s.RawProductSlug,
		&s.RawDeletedAt,
		&s.RawMaxOrderQuantity,
	)
	if err != nil {
		return nil, err
	}

	c := &StockItem{}
	c.Id = s.RawID
	c.Sku = s.SKU()
	c.Name, c.NameLocal = s.Name()
	c.Description, c.DescriptionLocal = s.Description()
	c.StoreId = s.StoreID()
	c.InStock = s.InStock()
	c.Price = s.Price()
	c.NormalPrice = s.NormalPrice()
	c.Cost = s.Cost()
	c.NormalCost = s.NormalCost()
	c.OriginalPromoCost = s.OriginalPromoCost()
	c.RawPopularity = s.UnboostedPopularity()
	c.Popularity = s.Popularity()
	c.BoostingPoint = s.BoostingPoint()
	c.MaxOrderQuantity = s.MaxOrderQuantity()
	c.PromotionId = s.PromotionID()
	c.StoreLocation = s.StoreLocation()
	c.VariantId = s.RawVariantID
	c.ProductId = s.RawProductID
	c.ProductTypeIds, c.ProductTypeName, c.ProductTypeNameLocal, c.ProductTypePath = s.ProductTypes()
	c.BrandId, c.BrandName, c.BrandNameLocal = s.Brand()
	c.AvgWeight = s.NaturalAverageWeight()
	c.DisplayUnit = s.NaturalUnit()
	c.SupermarketUnit = s.SupermarketUnit()
	c.Size_ = s.Size()
	c.UnitPriceUnit = s.UnitPriceUnit()
	c.UnitPieces = s.UnitPieces()
	c.PriceAndroid = s.PriceForChannel("android")
	c.PriceIos = s.PriceForChannel("ios")
	c.PriceWebapp = s.PriceForChannel("webapp")
	c.PriceMobileweb = s.PriceForChannel("mobileweb")
	c.PriceGrabfresh = s.PriceForChannel("grabfresh")
	c.CountryIso = s.CountryISO()
	c.UnitPrice = c.Price * c.UnitPriceFactor()
	c.UnitPriceAndroid = c.PriceAndroid * c.UnitPriceFactor()
	c.UnitPriceIos = c.PriceIos * c.UnitPriceFactor()
	c.UnitPriceWebapp = c.PriceWebapp * c.UnitPriceFactor()
	c.UnitPriceMobileweb = c.PriceMobileweb * c.UnitPriceFactor()
	c.UnitPriceGrabfresh = c.PriceGrabfresh * c.UnitPriceFactor()
	c.SellNatural = s.SellNatural()
	c.StockLocationId = s.StockLocationID()
	c.ProductSlug = s.ProductSlug()
	c.DeletedAt = s.DeletedAt()

	return c, nil
}

func init() {
	queries, err := getQueries("/resources/sql/stock_item.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		stockitems = chacha.NewGetter("stock:item:m",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanStockItem, time.Minute),
			cacheBackend,
			cacheEncoding,
		)

		stockItemsRead = chacha.NewGetter("stock:item",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanStockItem, 24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
