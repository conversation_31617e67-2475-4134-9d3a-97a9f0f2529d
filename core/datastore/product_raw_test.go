package datastore

import (
	"database/sql"
	"testing"

	"github.com/lib/pq/hstore"
	"github.com/stretchr/testify/assert"
)

func TestProductRawNaturalUnit(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name            string
		propertyPackage sql.NullString
		propertySize    sql.NullString
		propertyUnit    sql.NullString
		expected        string
	}{
		{"Complete natural unit", parseToNullString("5"), parseToNullString("250.00"), parseToNullString("gr"), "5 x 250 gr"},
		{"1 package", parseToNullString("1"), parseToNullString("250.50"), parseToNullString("gr"), "250.5 gr"},
	}
	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			productRaw := &ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"package": testCase.propertyPackage,
						"size":    testCase.propertySize,
						"unit":    testCase.propertyUnit,
					},
				},
			}

			naturalUnit := productRaw.ProductNaturalUnit()
			assert.Equal(testCase.expected, naturalUnit)
		})
	}
}

func TestProductRawName(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected string
	}{
		{
			"Case name exist",
			&ProductRaw{
				Name:      parseToNullString("Paseo Velvet Alcohol Free Baby Wipes"),
				NameLocal: parseToNullString(""),
			},
			"Paseo Velvet Alcohol Free Baby Wipes",
		},
		{
			"Case name not exist",
			&ProductRaw{
				Name:      parseToNullString(""),
				NameLocal: parseToNullString("Paseo Velvet Tisu Basah Bayi Bebas Alkohol"),
			},
			"Paseo Velvet Tisu Basah Bayi Bebas Alkohol",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductName()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawDescription(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected string
	}{
		{
			"Case description local exist",
			&ProductRaw{
				Description:      parseToNullString("Alcohol Free Baby Wipes with Aloe Vera and Vitamin E (10 sheets)"),
				DescriptionLocal: parseToNullString("Tisu Basah Bayi Bebas Alkohol dengan Lidah Buaya dan Vitamin E (10 lembar)"),
			},
			"Tisu Basah Bayi Bebas Alkohol dengan Lidah Buaya dan Vitamin E (10 lembar)",
		},
		{
			"Case description local not exist",
			&ProductRaw{
				Description:      parseToNullString("Alcohol Free Baby Wipes with Aloe Vera and Vitamin E (10 sheets)"),
				DescriptionLocal: parseToNullString(""),
			},
			"Alcohol Free Baby Wipes with Aloe Vera and Vitamin E (10 sheets)",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductDescription()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawSku(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected string
	}{
		{
			"Case sku exist",
			&ProductRaw{
				Sku: parseToNullString("8993053651027-ID"),
			},
			"8993053651027-ID",
		},
		{
			"Case sku not exist",
			&ProductRaw{
				Sku: parseToNullString(""),
			},
			"",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductSku()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawIsMaster(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected bool
	}{
		{
			"Case is master true",

			&ProductRaw{
				IsMaster: parseToNullBool(true),
			},
			true,
		},
		{
			"Case is master false",
			&ProductRaw{
				IsMaster: parseToNullBool(false),
			},
			false,
		},
		{
			"Case is master null",
			&ProductRaw{
				IsMaster: parseToNullBool(false),
			},
			false,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductIsMaster()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawTrackInventory(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected bool
	}{
		{
			"Case track inventory true",
			&ProductRaw{
				TrackInventory: parseToNullBool(true),
			},
			true,
		},
		{
			"Case track inventory false",
			&ProductRaw{
				TrackInventory: parseToNullBool(false),
			},
			false,
		},
		{
			"Case track inventory null",
			&ProductRaw{
				TrackInventory: parseToNullBool(nil),
			},
			false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductTrackInventory()
			assert.Equal(tc.expected, reality)

		})
	}

}

func TestProductRawInStock(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected bool
	}{
		{
			"Case in stock true",
			&ProductRaw{
				InStock: parseToNullBool(true),
			},
			true,
		},
		{
			"Case in stock false",
			&ProductRaw{
				InStock: parseToNullBool(false),
			},
			false,
		},
		{
			"Case in stock null",
			&ProductRaw{
				InStock: parseToNullBool(nil),
			},
			false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductInStock()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawProductType(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected string
	}{
		{
			"Case product type exist",
			&ProductRaw{
				ProductType: parseToNullString("beverages/tea/ice_tea/milk_tea"),
			},
			"beverages/tea/ice_tea/milk_tea",
		},
		{
			"Case product type not exist",
			&ProductRaw{
				ProductType: parseToNullString(""),
			},
			"",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductProductType()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawBrand(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name         string
		p            *ProductRaw
		expectedID   int64
		expectedName string
	}{
		{
			"Case brand id and name exist",
			&ProductRaw{
				BrandId:   parseToNullInt64(int64(12459)),
				BrandName: parseToNullString("paseo"),
			},
			12459,
			"paseo",
		},
		{
			"Case brand id and name not exist",
			&ProductRaw{
				BrandId:   parseToNullInt64(nil),
				BrandName: parseToNullString(""),
			},
			0,
			"",
		},
		{
			"Case brand id not exist",
			&ProductRaw{
				BrandId:   parseToNullInt64(nil),
				BrandName: parseToNullString("paseo"),
			},
			0,
			"paseo",
		},
		{
			"Case brand name not exist",
			&ProductRaw{
				BrandId:   parseToNullInt64(int64(12459)),
				BrandName: parseToNullString(""),
			},
			12459,
			"",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			rID, rName := tc.p.ProductBrand()
			assert.Equal(tc.expectedName, rName)
			assert.Equal(tc.expectedID, rID)
		})
	}
}

func TestProductRawSellNatural(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected string
	}{
		{
			"Case sell natural properties exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"sell_natural": parseToNullString("false"),
					},
				},
			},
			"false",
		},
		{
			"Case sell natural properties not exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"sell_natural": parseToNullString(""),
					},
				},
			},
			"",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductSellNatural()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawAverageWeight(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected float64
	}{
		{
			"Case average weight = 0",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"avg_weight": parseToNullString("0"),
						"size":       parseToNullString("1"),
					},
				},
			},
			0,
		},
		{
			"Case average weight > 0",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"avg_weight": parseToNullString("1"),
						"size":       parseToNullString("1"),
					},
				},
			},
			1,
		},
		{
			"Case size = 0",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"avg_weight": parseToNullString("1"),
						"size":       parseToNullString("0"),
					},
				},
			},
			1,
		},
		{
			"Case average weight not exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"avg_weight": parseToNullString(""),
						"size":       parseToNullString("1"),
					},
				},
			},
			0,
		},
		{
			"Case size not exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"avg_weight": parseToNullString("1"),
						"size":       parseToNullString(""),
					},
				},
			},
			1,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductAverageWeight()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawUnitPieces(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected float64
	}{
		{
			"Case unit pieces properties exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"unit_pieces": parseToNullString("10"),
					},
				},
			},
			10,
		},
		{
			"Case unit pieces properties not exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"unit_pieces": parseToNullString(""),
					},
				},
			},
			0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductUnitPieces()
			assert.Equal(tc.expected, reality)
		})
	}
}

func TestProductRawSupermarketUnit(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected string
	}{
		{
			"Case super market unit propertis exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"supermarket_unit": parseToNullString("packet"),
					},
				},
			},
			"packet",
		},
		{
			"Case super market unit properties not exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"supermarket_unit": parseToNullString(""),
					},
				},
			},
			"",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductSupermarketUnit()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawSize(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected float64
	}{
		{
			"Case size properties exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"size": parseToNullString("1"),
					},
				},
			},
			1,
		},
		{
			"Case size properties not exist",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"size": parseToNullString(""),
					},
				},
			},
			0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductSize()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawPopularity(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected float64
	}{
		{
			"Case popularity and boosting point exist",
			&ProductRaw{
				Popularity:    parseToNullFloat64(float64(59.75)),
				BoostingPoint: parseToNullFloat64(float64(1)),
			},
			60.75,
		},
		{
			"Case boosting point not exist",
			&ProductRaw{
				Popularity:    parseToNullFloat64(float64(59.75)),
				BoostingPoint: parseToNullFloat64(nil),
			},
			59.75,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductPopularity()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawBoostingPoint(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected float64
	}{
		{
			"Case boosting point exist",
			&ProductRaw{
				BoostingPoint: parseToNullFloat64(float64(1)),
			},
			1,
		},
		{
			"Case boosting point not exist",
			&ProductRaw{
				BoostingPoint: parseToNullFloat64(nil),
			},
			0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductBoostingPoint()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawPromotionId(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected int64
	}{
		{
			"Case promotion id exist",
			&ProductRaw{
				PromotionId: parseToNullInt64(int64(1963518)),
			},
			1963518,
		},
		{
			"Case promotion id not exist",
			&ProductRaw{
				PromotionId: parseToNullInt64(nil),
			},
			0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductPromotionId()
			assert.Equal(tc.expected, reality)
		})
	}
}

func TestProductRawMaxOrderQuantity(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected int64
	}{
		{
			"Case if stock item moq = 0",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"max_order_quantity": parseToNullString("10"),
					},
				},
				MaxOrderQuantity: 0,
			},
			10,
		},
		{
			"Case if stock item moq > 0",
			&ProductRaw{
				Properties: hstore.Hstore{
					Map: map[string]sql.NullString{
						"max_order_quantity": parseToNullString("50"),
					},
				},
				MaxOrderQuantity: 20,
			},
			20,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductMaxOrderQuantity()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawShippingCategoryID(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected int64
	}{
		{
			"Case shipping category id exist",
			&ProductRaw{
				ShippingCategoryId: parseToNullInt64(int64(1)),
			},
			1,
		},
		{
			"Case shipping category id not exist",
			&ProductRaw{
				ShippingCategoryId: parseToNullInt64(nil),
			},
			0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductShippingCategoryID()
			assert.Equal(tc.expected, reality)

		})
	}
}

func TestProductRawAvailableOn(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        *ProductRaw
		expected string
	}{
		{
			"Case available on exist",
			&ProductRaw{
				AvailableOn: parseToNullString("2017-04-13 17:54:18.725628"),
			},
			"2017-04-13 17:54:18.725628",
		},
		{
			"Case available on not exist",
			&ProductRaw{
				AvailableOn: parseToNullString(""),
			},
			"",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reality := tc.p.ProductAvailableOn()
			assert.Equal(tc.expected, reality)

		})
	}
}

func parseToNullString(value string) sql.NullString {
	valid := true
	if value == "" {
		valid = false
	}

	return sql.NullString{
		String: value,
		Valid:  valid,
	}
}

func parseToNullBool(value interface{}) sql.NullBool {
	if value == nil {
		return sql.NullBool{
			Valid: false,
		}
	}

	v := value.(bool)

	return sql.NullBool{
		Bool:  v,
		Valid: true,
	}
}

func parseToNullInt64(value interface{}) sql.NullInt64 {
	if value == nil {
		return sql.NullInt64{
			Valid: false,
		}
	}

	v := value.(int64)

	return sql.NullInt64{
		Int64: v,
		Valid: true,
	}
}

func parseToNullFloat64(value interface{}) sql.NullFloat64 {
	if value == nil {
		return sql.NullFloat64{
			Valid: false,
		}
	}

	v := value.(float64)

	return sql.NullFloat64{
		Float64: v,
		Valid:   true,
	}
}
