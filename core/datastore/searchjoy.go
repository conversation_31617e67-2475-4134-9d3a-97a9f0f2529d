package datastore

import (
	"context"
	"database/sql"
	fmt "fmt"
	"strconv"
	"time"

	proto "github.com/golang/protobuf/proto"
	"github.com/lib/pq/hstore"
	"github.com/pkg/errors"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. searchjoy.proto

var (
	searchJoyQueries map[string]string
	searchjoyGetter  chacha.Getter
)

type RawSearchjoyKeyword struct {
	ID              int64
	NormalizedQuery sql.NullString
	Conversions     sql.NullInt64
	Occurences      sql.NullInt64
	CountryISO      sql.NullString
	Locale          sql.NullString
	StockLocationID sql.NullInt64
}

type searchIDBody struct {
	query            string
	normalizedQuery  string
	countryISO       string
	locale           string
	stockLocationID  int
	userID           string
	totalFound       int
	searchType       string
	createdAt        time.Time
	externalSearchID string
	properties       hstore.Hstore
}

type SearchIDOption func(*searchIDBody)

func CreateSearchID(ctx context.Context, applicables ...SearchIDOption) (searchID int64, err error) {
	b := &searchIDBody{
		searchType: "Spree::Product",
		createdAt:  time.Now(),
	}

	for _, apply := range applicables {
		apply(b)
	}

	err = conn.QueryRowContext(
		ctx,
		fmt.Sprintf(searchJoyQueries["create-search-id"], b.userID),
		b.totalFound, b.query, b.normalizedQuery, b.searchType, b.createdAt,
		b.countryISO, b.locale, b.stockLocationID, b.externalSearchID, b.properties,
	).Scan(&searchID)

	return
}

func WithSearchJoyQuery(q string) SearchIDOption {
	return func(s *searchIDBody) {
		s.query = q
	}
}

func WithSearchJoyNormalizedQuery(q string) SearchIDOption {
	return func(s *searchIDBody) {
		s.normalizedQuery = q
	}
}

func WithSearchJoyCountryISO(c string) SearchIDOption {
	return func(s *searchIDBody) {
		s.countryISO = c
	}
}

func WithSearchJoyLocale(l string) SearchIDOption {
	return func(s *searchIDBody) {
		s.locale = l
	}
}

func WithSearchJoyStockLocationID(id int) SearchIDOption {
	return func(s *searchIDBody) {
		s.stockLocationID = id
	}
}

func WithSearchJoyUserID(id int64) SearchIDOption {
	return func(s *searchIDBody) {
		var uID string
		if id > 0 {
			uID = strconv.FormatInt(id, 10)
		} else {
			uID = "NULL"
		}

		s.userID = uID
	}
}

func WithSearchJoyTotalFound(t int) SearchIDOption {
	return func(s *searchIDBody) {
		s.totalFound = t
	}
}

func WithSearchJoyExternalSearchID(id string) SearchIDOption {
	return func(s *searchIDBody) {
		s.externalSearchID = id
	}
}

func WithSearchJoyProperty(field string, value string) SearchIDOption {
	return func(s *searchIDBody) {
		if s.properties.Map == nil {
			s.properties = hstore.Hstore{Map: make(map[string]sql.NullString)}
		}

		v := sql.NullString{String: value, Valid: true}
		if len(value) == 0 {
			v = sql.NullString{}
		}

		s.properties.Map[field] = v
	}
}

func FindSearchjoyByID(ctx context.Context, id int64) (*SearchjoyRecord, error) {
	key, err := MarshalKey("find-searchjoy-by-id", id)
	if err != nil {
		return nil, err
	}

	result := &SearchjoyRecord{}
	err = searchjoyGetter.Get(ctx, key, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func scanSearchjoy(r *sql.Row) (proto.Message, error) {
	var query sql.NullString
	var normQuery sql.NullString
	var countryIso sql.NullString
	var locale sql.NullString
	var stockLocationID sql.NullInt64
	var userID sql.NullInt64
	var createdAt sql.NullString
	var externalID sql.NullString
	var properties hstore.Hstore
	if r == nil {
		return nil, errors.New("SQL Row is nil")
	}
	if err := r.Scan(
		&query,
		&normQuery,
		&countryIso,
		&locale,
		&stockLocationID,
		&userID,
		&createdAt,
		&externalID,
		&properties,
	); err != nil {
		return nil, err
	}

	result := &SearchjoyRecord{Properties: map[string]string{}}
	if query.Valid {
		result.Query = query.String
	}
	if normQuery.Valid {
		result.NormalizedQuery = normQuery.String
	}
	if countryIso.Valid {
		result.CountryIso = countryIso.String
	}
	if locale.Valid {
		result.Locale = locale.String
	}
	if stockLocationID.Valid {
		result.StockLocationId = stockLocationID.Int64
	}
	if userID.Valid {
		result.UserId = userID.Int64
	}
	if createdAt.Valid {
		result.CreatedAt = createdAt.String
	}
	if externalID.Valid {
		result.ExternalSearchId = externalID.String
	}
	for k, v := range properties.Map {
		if !v.Valid {
			result.Properties[k] = ""
			continue
		}

		result.Properties[k] = v.String
	}

	return result, nil
}

func init() {
	queries, err := getQueries("/resources/sql/searchjoy.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		searchjoyGetter = chacha.NewGetter("searchjoy",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanSearchjoy, time.Minute),
			cacheBackend,
			cacheEncoding,
		)
	})

	searchJoyQueries = queries
}
