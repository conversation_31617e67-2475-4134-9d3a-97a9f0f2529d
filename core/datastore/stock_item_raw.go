package datastore

import (
	"bytes"
	"database/sql"
	fmt "fmt"
	"strconv"
	"strings"
	"time"

	"github.com/lib/pq"
	"github.com/lib/pq/hstore"
	"happyfresh.io/lib/str"
)

const (
	// propertiesKeywordTag         = "keyword"
	propertiesNaturalUnitTag     = "natural_unit"
	propertiesSupermarketUnitTag = "supermarket_unit"
	propertiesAverageWeightTag   = "avg_weight"
	propertiesPackageTag         = "package"
	propertiesSizeTag            = "size"
	propertiesUnitTag            = "unit"
	propertiesUnitPiecesTag      = "unit_pieces"
	// propertiesBundleTag        = "bundle"
	propertiesSellNaturalTag   = "sell_natural"
	propertiesMaxOrderQuantity = "max_order_quantity"
)

type RawStockItem struct {
	RawID                    int64
	RawSku                   sql.NullString
	RawName                  sql.NullString
	RawNameLocal             sql.NullString
	RawDescription           sql.NullString
	RawDescriptionLocal      sql.NullString
	RawStoreID               sql.NullInt64
	RawInStock               sql.NullString
	RawPrice                 sql.NullFloat64
	RawNormalPrice           sql.NullFloat64
	RawCost                  sql.NullFloat64
	RawNormalCost            sql.NullFloat64
	RawOriginalPromoCost     sql.NullFloat64
	RawClientTypePromoPrice  hstore.Hstore
	RawPopularity            sql.NullFloat64
	RawBoostingPoint         sql.NullFloat64
	RawPromotionID           sql.NullInt64
	RawStoreLocation         sql.NullString
	RawVariantID             int64
	RawProductID             int64
	RawTranslatedTaxon       hstore.Hstore
	RawTranslatedProductType hstore.Hstore
	RawProductTypePath       sql.NullString
	RawBrandID               sql.NullInt64
	RawBrandName             sql.NullString
	RawBrandNameLocal        sql.NullString
	RawProperties            hstore.Hstore
	RawCountryISO            sql.NullString
	RawUserIDs               pq.StringArray
	RawStockLocationID       sql.NullInt64
	RawProductSlug           sql.NullString
	RawDeletedAt             pq.NullTime
	RawMaxOrderQuantity      int64
	pq.Int64Array
}

func (s *RawStockItem) SKU() (sku string) {
	if s.RawSku.Valid {
		sku = s.RawSku.String
	}

	return
}

func (s *RawStockItem) Name() (name string, nameLocal string) {
	if s.RawName.Valid {
		name = s.RawName.String
	}

	if s.RawNameLocal.Valid {
		nameLocal = s.RawNameLocal.String
	}

	return
}

func (s *RawStockItem) Description() (desc string, descLocal string) {
	if s.RawDescription.Valid {
		desc = s.RawDescription.String
	}

	if s.RawDescriptionLocal.Valid {
		descLocal = s.RawDescriptionLocal.String
	}

	return
}

func (s *RawStockItem) StoreID() (storeID int64) {
	if s.RawStoreID.Valid {
		storeID = s.RawStoreID.Int64
	}

	return
}

func (s *RawStockItem) InStock() (inStock string) {
	if s.RawInStock.Valid {
		inStock = s.RawInStock.String
	}

	return
}

func (s *RawStockItem) Price() (price float64) {
	if s.RawPrice.Valid {
		price = s.RawPrice.Float64
	}

	return
}

func (s *RawStockItem) NormalPrice() (normalPrice float64) {
	if s.RawNormalPrice.Valid {
		normalPrice = s.RawNormalPrice.Float64
	}

	return
}

func (s *RawStockItem) Cost() (cost float64) {
	if s.RawCost.Valid {
		cost = s.RawCost.Float64
	}

	return
}

func (s *RawStockItem) NormalCost() (normalCost float64) {
	if s.RawNormalCost.Valid {
		normalCost = s.RawNormalCost.Float64
	}

	return
}

func (s *RawStockItem) OriginalPromoCost() (originalPromoCost float64) {
	if s.RawOriginalPromoCost.Valid {
		originalPromoCost = s.RawOriginalPromoCost.Float64
	}

	return
}

func (s *RawStockItem) PriceForChannel(channel string) float64 {
	price, ok := s.ClientTypePromoPrice()[channel]
	if !ok || price <= 0 {
		price = s.Price()
	}

	return price
}

func (s *RawStockItem) ClientTypePromoPrice() (clientTypePromoPrice map[string]float64) {
	clientTypePromoPrice = map[string]float64{}
	for k, v := range s.RawClientTypePromoPrice.Map {
		if !v.Valid {
			continue
		}

		if price, err := strconv.ParseFloat(v.String, 64); err == nil {
			clientTypePromoPrice[k] = price
		}
	}

	return
}

func (s *RawStockItem) UnboostedPopularity() float64 {
	if s.RawPopularity.Valid {
		return s.RawPopularity.Float64
	}

	return 0.0
}

func (s *RawStockItem) Popularity() float64 {
	popularity := 0.0
	if s.RawPopularity.Valid {
		popularity = popularity + s.RawPopularity.Float64
	}

	if s.RawBoostingPoint.Valid {
		popularity = popularity + s.RawBoostingPoint.Float64
	}

	return popularity
}

func (s *RawStockItem) BoostingPoint() (boostingPoint float64) {
	if s.RawBoostingPoint.Valid {
		boostingPoint = s.RawBoostingPoint.Float64
	}

	return
}

func (s *RawStockItem) PromotionID() (promotionID int64) {
	if s.RawPromotionID.Valid {
		promotionID = s.RawPromotionID.Int64
	}

	return
}

func (s *RawStockItem) StoreLocation() (storeLocation string) {
	if s.RawStoreLocation.Valid {
		storeLocation = s.RawStoreLocation.String
	}

	return
}

func (s *RawStockItem) Brand() (id int64, name string, nameLocal string) {
	if s.RawBrandID.Valid {
		id = s.RawBrandID.Int64
	}

	if s.RawBrandName.Valid {
		name = s.RawBrandName.String
	}

	if s.RawBrandNameLocal.Valid {
		nameLocal = s.RawBrandNameLocal.String
	}

	return
}

func (s *RawStockItem) Taxons() (IDs []int64, name []string, nameLocal []string) {
	taxonIDs := map[int]bool{}
	for k, v := range s.RawTranslatedTaxon.Map {
		var keys pq.StringArray
		err := keys.Scan(k)
		if err != nil {
			continue
		}

		taxonID, err := strconv.Atoi(keys[0])
		if err != nil {
			continue
		}

		taxonIDs[taxonID] = true

		if keys[1] == "en" {
			name = append(name, v.String)
		} else {
			nameLocal = append(nameLocal, v.String)
		}
	}

	for k := range taxonIDs {
		IDs = append(IDs, int64(k))
	}

	return
}

func (s *RawStockItem) ProductTypes() (IDs []int64, name []string, nameLocal []string, path string) {
	for k, v := range s.RawTranslatedProductType.Map {
		var keys pq.StringArray
		err := keys.Scan(k)
		if err != nil {
			continue
		}

		productTypeID, err := strconv.Atoi(v.String)
		if err != nil {
			continue
		}

		IDs = append(IDs, int64(productTypeID))

		if keys[0] != "" {
			name = append(name, keys[0])
		}

		if keys[1] != "" {
			nameLocal = append(nameLocal, keys[1])
		}
	}

	if s.RawProductTypePath.Valid {
		path = s.RawProductTypePath.String
	}

	return
}

func (s *RawStockItem) NaturalAverageWeight() (avgWeight float64) {
	properties := s.RawProperties.Map

	if v, ok := properties[propertiesAverageWeightTag]; ok && v.Valid {
		avg, err := strconv.ParseFloat(v.String, 64)
		if err == nil {
			avgWeight = avg
		}
	}

	if avgWeight <= 0 {
		return
	}

	size := s.Size()
	if size <= 0 {
		size = 1
	}

	avgWeight = avgWeight * (1 / size)

	return
}

func (s *RawStockItem) MaxOrderQuantity() (maxOrderQty int64) {
	properties := s.RawProperties.Map
	stockItemMoq := s.RawMaxOrderQuantity
	if stockItemMoq > 0 {
		maxOrderQty = stockItemMoq
		return
	}

	if v, ok := properties[propertiesMaxOrderQuantity]; ok && v.Valid {
		if moq, err := strconv.Atoi(v.String); err == nil {
			maxOrderQty = int64(moq)
		}
	}

	return
}

func (s *RawStockItem) Size() (size float64) {
	properties := s.RawProperties.Map

	if v, ok := properties[propertiesSizeTag]; ok && v.Valid {
		sz, err := strconv.ParseFloat(v.String, 64)
		if err == nil {
			size = sz
		}
	}

	return
}

func (s *RawStockItem) UnitPieces() (unitPieces float64) {
	properties := s.RawProperties.Map

	if v, ok := properties[propertiesUnitPiecesTag]; ok && v.Valid {
		up, err := strconv.ParseFloat(v.String, 64)
		if err == nil {
			unitPieces = up
		}
	}

	return
}

func (s *RawStockItem) NaturalUnit() (naturalUnit string) {
	properties := s.RawProperties.Map

	if v, ok := properties[propertiesNaturalUnitTag]; ok && v.Valid {
		naturalUnit = v.String
	}

	if naturalUnit == "" {
		buf := &bytes.Buffer{}

		if v, ok := properties[propertiesPackageTag]; ok && v.Valid {
			pkg := str.String(v.String).Int()
			if pkg > 1 {
				buf.WriteString(fmt.Sprintf("%d x ", pkg))
			}
		}

		if v, ok := properties[propertiesSizeTag]; ok && v.Valid {
			size := str.String(v.String).Float64()
			if size >= 1.0 {
				buf.WriteString(fmt.Sprintf("%g ", size))
			}
		}

		if v, ok := properties[propertiesUnitTag]; ok && v.Valid {
			buf.WriteString(v.String)
		}

		naturalUnit = buf.String()
	}

	return
}

func (s *RawStockItem) SupermarketUnit() (supermarketUnit string) {
	properties := s.RawProperties.Map

	if v, ok := properties[propertiesSupermarketUnitTag]; ok && v.Valid {
		supermarketUnit = v.String
	}

	return
}

func (s *RawStockItem) UnitPriceUnit() (unitPriceUnit string) {
	properties := s.RawProperties.Map

	if v, ok := properties[propertiesSupermarketUnitTag]; ok && v.Valid {
		supermarketUnit := v.String

		f := func(u string) bool {
			return strings.Contains(strings.ToLower(supermarketUnit), u)
		}
		switch {
		case f("bundle"):
			unitPriceUnit = "each"
		case f("l"), f("ml"):
			unitPriceUnit = "100 ml"
		case f("mg"):
			unitPriceUnit = "mg"
		case f("kg"), f("g"):
			unitPriceUnit = "100 g"
		default:
			unitPriceUnit = "each"
		}
	}

	return
}

func (s *RawStockItem) SellNatural() (sellNatural string) {
	properties := s.RawProperties.Map

	if v, ok := properties[propertiesSellNaturalTag]; ok && v.Valid {
		sellNatural = v.String
	}

	return
}

func (s *RawStockItem) ProductSlug() (slug string) {
	if s.RawProductSlug.Valid {
		slug = s.RawProductSlug.String
	}

	return
}

func (s *RawStockItem) StockLocationID() (stockLocationID int64) {
	if s.RawStockLocationID.Valid {
		stockLocationID = s.RawStockLocationID.Int64
	}

	return
}

func (s *RawStockItem) DeletedAt() (deletedAt string) {
	if s.RawDeletedAt.Valid {
		deletedAt = s.RawDeletedAt.Time.Format(time.RFC3339)
	}

	return
}

func (s *RawStockItem) CountryISO() (countryiso string) {
	if s.RawCountryISO.Valid {
		countryiso = s.RawCountryISO.String
	} else {
		countryiso = "en"
	}

	return
}
