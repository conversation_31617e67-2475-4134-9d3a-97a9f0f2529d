package datastore

import (
	"context"
	"fmt"
	"sort"
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

func TestTaxonByID(t *testing.T) {
	type want struct {
		id   int64
		name string
		err  string
	}

	assert := func(t *testing.T, w want, c *Taxon, err error) {
		if w.id != c.Id {
			t.<PERSON>("ID: Want %v Got %v", w.id, c.Id)
		}

		if w.name != c.Name {
			t.<PERSON>("ID: Want %v Got %v", w.name, c.Name)
		}
	}

	for _, tt := range []struct {
		name string
		id   int
		want want
	}{
		{"Taxon ID inputted", 100, want{100, "Taxon-01", ""}},
	} {
		t.Run(tt.name, func(t *testing.T) {
			u, err := TaxonByID(context.Background(), tt.id)
			if err != nil {
				t.Error(errors.Wrap(err, fmt.Sprintf("Value Scan Error: sql: no rows in result set")))
			}

			assert(t, tt.want, u, err)
		})
	}
}

func TestTaxonByProductID(t *testing.T) {
	for _, tc := range []struct {
		name                  string
		productID             int
		countryISO            string
		expectationHasErr     bool
		expectationIDs        []int
		expectationNamesEN    []string
		expectationNameLocals []string
	}{
		{
			"Returns all taxons of a product with their translation",
			94, "id",
			false,
			[]int{202, 200},
			[]string{"Taxon-02-02-EN", "Taxon-02-EN"},
			[]string{"Taxon-02-02-ID", "Taxon-02-ID"},
		},
		{
			"Returns all taxons of a product with their translation - uppercase country iso",
			94, "ID",
			false,
			[]int{202, 200},
			[]string{"Taxon-02-02-EN", "Taxon-02-EN"},
			[]string{"Taxon-02-02-ID", "Taxon-02-ID"},
		},
		{
			"Returns all taxons of a product with their translation - country iso th",
			94, "th",
			false,
			[]int{202, 200},
			[]string{"Taxon-02-02-EN", "Taxon-02-EN"},
			[]string{"Taxon-02-02-TH"},
		},
	} {

		taxons, err := TaxonsByProductID(context.Background(), tc.productID, tc.countryISO)
		if (err != nil) != tc.expectationHasErr {
			t.Errorf("expectation: %v; reality: %v", tc.expectationHasErr, err != nil)
		}

		if tc.expectationHasErr {
			return
		}

		resTaxonIDs := make([]int, 0, len(taxons))
		resTaxonNames := make([]string, 0, len(taxons))
		resTaxonNameLocals := make([]string, 0, len(taxons))
		for _, taxn := range taxons {
			if taxn.Id > 0 {
				resTaxonIDs = append(resTaxonIDs, int(taxn.Id))
			}

			if len(taxn.Name) > 0 {
				resTaxonNames = append(resTaxonNames, taxn.Name)
			}

			if len(taxn.NameLocale) > 0 {
				resTaxonNameLocals = append(resTaxonNameLocals, taxn.NameLocale)
			}
		}

		if !arrayIntEqual(tc.expectationIDs, resTaxonIDs) {
			t.Errorf("expectation: %v; reality: %v", tc.expectationIDs, resTaxonIDs)
		}

		if !arrayStringEqual(tc.expectationNamesEN, resTaxonNames) {
			t.Errorf("expectation: %v; reality: %v", tc.expectationNamesEN, resTaxonNames)
		}

		if !arrayStringEqual(tc.expectationNameLocals, resTaxonNameLocals) {
			t.Errorf("expectation: %v; reality: %v", tc.expectationNameLocals, resTaxonNameLocals)
		}
	}
}

func TestTaxonsByProductIDs(t *testing.T) {
	for _, tc := range []struct {
		name               string
		productIDsParam    []int64
		localeParam        string
		expectError        bool
		expectedTaxonIDs   map[int64][]int64
		expectedTaxonNames map[int64][]string
	}{
		{
			"Returns product taxon and its ancestors",
			[]int64{91, 93},
			"en",
			false,
			map[int64][]int64{
				91: {101, 100},
				93: {201, 200},
			},
			map[int64][]string{
				91: {"Taxon-01-EN", "Taxon-01-01-EN"},
				93: {"Taxon-02-EN", "Taxon-02-01-EN"},
			},
		},
		{
			"Returns product taxon and its ancestors, localized",
			[]int64{91, 93},
			"id",
			false,
			map[int64][]int64{
				91: {101, 100},
				93: {201, 200},
			},
			map[int64][]string{
				91: {"Taxon-01-ID", "Taxon-01-01-ID"},
				93: {"Taxon-02-ID", "Taxon-02-01-ID"},
			},
		},
		{
			"Returns product taxon and its ancestors, some not found",
			[]int64{91, 999},
			"id",
			false,
			map[int64][]int64{
				91:  {101, 100},
				999: {},
			},
			map[int64][]string{
				91:  {"Taxon-01-ID", "Taxon-01-01-ID"},
				999: {},
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := TaxonsByProductIDs(ctx, tc.productIDsParam, tc.localeParam)
			assert.Equal(t, tc.expectError, err != nil)
			if tc.expectError {
				return
			}

			for k, expectedTaxonIDs := range tc.expectedTaxonIDs {
				expectedTaxonNames := tc.expectedTaxonNames[k]

				resultTaxons, ok := result[k]
				if !ok {
					t.Error()
				}

				resultTaxonIDs := []int64{}
				resultTaxonNames := []string{}
				for _, t := range resultTaxons {
					resultTaxonIDs = append(resultTaxonIDs, t.Id)
					resultTaxonNames = append(resultTaxonNames, t.NameLocale)
				}

				assert.ElementsMatch(t, expectedTaxonIDs, resultTaxonIDs)
				assert.ElementsMatch(t, expectedTaxonNames, resultTaxonNames)
			}
		})
	}
}

func arrayIntEqual(a, b []int) bool {
	sort.Ints(a)
	sort.Ints(b)

	if (a == nil) != (b == nil) {
		return false
	}

	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}

	return true
}

func arrayInt64Equal(a, b []int64) bool {
	if (a == nil) != (b == nil) {
		return false
	}

	if len(a) != len(b) {
		return false
	}

	m := map[int64]interface{}{}
	for _, e := range a {
		m[e] = &struct{}{}
	}

	equal := true
	for _, e := range b {
		if _, ok := m[e]; !ok {
			equal = false
			break
		}
	}

	return equal
}

func arrayStringEqual(a, b []string) bool {
	sort.Strings(a)
	sort.Strings(b)

	if (a == nil) != (b == nil) {
		return false
	}

	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}

	return true
}
