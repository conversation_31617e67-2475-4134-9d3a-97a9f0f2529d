package datastore

import (
	"context"
	"database/sql"
	"time"

	proto "github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. user.proto

var (
	users chacha.Getter
)

// UserID get user id for particular spree api token
func UserID(ctx context.Context, spreeToken string) (*User, error) {
	if len(spreeToken) == 0 {
		return &User{}, errors.New("Invalid user token")
	}
	key, err := MarshalKey("user-id-by-spree-token", spreeToken)
	if err != nil {
		return nil, err
	}

	user := &User{}
	err = users.Get(ctx, key, user)
	if err != nil {
		return nil, err
	}

	return user, nil
}

func scanUser(r *sql.Row) (proto.Message, error) {
	user := &User{}
	err := r.<PERSON>(
		&user.Id,
	)
	if err != nil {
		return nil, err
	}

	return user, nil
}

func init() {
	queries, err := getQueries("/resources/sql/user.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		users = chacha.NewGetter("users",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanUser, 30*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
