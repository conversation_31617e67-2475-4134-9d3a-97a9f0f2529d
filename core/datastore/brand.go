package datastore // import "happyfresh.io/search/core/datastore"

import (
	"context"
	"database/sql"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. brand.proto

var (
	brand chacha.Getter
)

func scanBrand(r *sql.Row) (proto.Message, error) {
	brand := &Brand{}
	err := r.<PERSON>an(
		&brand.ID,
		&brand.Name,
	)
	if err != nil {
		return nil, err
	}

	return brand, nil
}

func getBrand(ctx context.Context, name string, args ...interface{}) (*Brand, error) {
	c := &Brand{}
	key, err := MarshalKey(name, args...)
	if err != nil {
		return nil, err
	}

	err = brand.Get(ctx, key, c)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// Brand get Brand ID by Name
func BrandByName(ctx context.Context, name string) (*Brand, error) {
	return getBrand(ctx, "brand-id-by-brand-name", name)
}

func BrandByNames(ctx context.Context, names ...string) (map[string]*Brand, error) {
	result := make([]proto.Message, len(names))
	keys := make([]string, len(names))

	for i, name := range names {
		k, err := MarshalKey("brand-id-by-brand-name", name)
		if err != nil {
			return nil, err
		}

		keys[i] = k
		result[i] = proto.Message(&Brand{})
	}

	err := brand.GetMany(ctx, keys, result)
	if err != nil {
		return nil, err
	}

	brands := map[string]*Brand{}
	for i, v := range result {
		brands[names[i]] = v.(*Brand)
	}

	return brands, nil
}

func init() {
	queries, err := getQueries("/resources/sql/brand.sql")
	if err != nil {

		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		brand = chacha.NewGetter("brand",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanBrand, 30*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
