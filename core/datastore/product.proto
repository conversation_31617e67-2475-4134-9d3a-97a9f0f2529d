syntax = "proto3";

package datastore;

message Product {
    int64 id = 1;
    string name = 2;
    string description = 3;
    int64 variant_id = 4;
    string sku = 5;
    bool is_master = 6;
    bool track_inventory = 7;
    bool in_stock = 8;
    string product_type = 9;
    int64 brand_id = 10;
    string brand_name = 11;
    int64 stock_item_id = 12;
    string sell_natural = 13;
    double average_weight = 14;
    double unit_pieces = 15;
    string supermarket_unit = 16;
    string natural_unit = 17;
    double size = 18;
    double popularity = 19;
    double boosting_point = 20;
    int64 promotion_id = 21;
    int64 max_order_quantity = 22;
    int64 shipping_category_id = 23;
    string available_on = 24;
    string locale = 25;
    repeated int64 taxon_ids = 26;
    repeated string categories = 27;
    ProductPricePromotion price_promotion = 28;
}

message ProductPricePromotion {
    int64 store_id = 1;
    string sku = 2;
    double price = 3;
    bool in_stock = 4;
    double normal_price = 5;
    double store_normal_price = 6;
    double original_promo_cost = 7;
    double cost = 8;
    int64 max_order_quantity = 9;
    int64 max_promo_quantity = 10;
    int64 promotion_id = 11;
    repeated string display_banner_text = 12;
    string display_promotion_actions_combination_text = 13;
    string promotion_action_type = 14;
    string display_promotion_actions_short_text = 15;
    string display_promotion_actions_long_text = 16;
    int64 buy_x_quantity = 17;
    int64 get_y_quantity = 18;
}
