package datastore

import (
	"context"
	"database/sql"
	"strings"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=.  product_type.proto

var (
	productTypes    chacha.Getter
	productTypeRows chacha.Getter
)

func ProductTypeBySKU(ctx context.Context, sku, locale string) (*ProductType, error) {
	p := &ProductType{}
	key, err := MarshalKey("product-type-by-sku", sku, strings.ToLower(locale))
	if err != nil {
		return nil, err
	}

	err = productTypes.Get(ctx, key, p)
	if err != nil {
		return nil, err
	}

	return p, nil
}

func ProductTypeByProductID(ctx context.Context, productID int, locale string) (*ProductType, error) {
	key, err := MarshalKey("product-type-by-product-id", productID, strings.ToLower(locale))
	if err != nil {
		return nil, err
	}

	p := &ProductType{}
	err = productTypes.Get(ctx, key, p)
	if err != nil {
		return nil, err
	}

	return p, nil
}

func ProductTypesByProductID(ctx context.Context, productID int, locale string) ([]*ProductType, error) {
	key, err := MarshalKey("product-types-by-product-id", productID, strings.ToLower(locale))
	if err != nil {
		return nil, err
	}

	p := &ProductTypes{}
	err = productTypeRows.Get(ctx, key, p)
	if err != nil {
		return nil, err
	}

	return p.ProductTypes, nil
}

func ProductTypesByProductIDs(ctx context.Context, productIDs []int64, locale string) (map[int64][]*ProductType, error) {
	keys := make([]string, len(productIDs))
	results := make([]proto.Message, len(productIDs))
	for i, id := range productIDs {
		k, _ := MarshalKey("product-types-by-product-id", id, strings.ToLower(locale))
		keys[i] = k
		results[i] = &ProductTypes{}
	}

	err := productTypeRows.GetMany(ctx, keys, results)
	if err != nil {
		return nil, err
	}

	out := make(map[int64][]*ProductType)
	for i, id := range productIDs {
		r, ok := results[i].(*ProductTypes)
		if !ok || r.ProductTypes == nil {
			out[id] = []*ProductType{}
		}

		out[id] = r.ProductTypes
	}

	return out, nil
}

func scanProductType(r *sql.Row) (proto.Message, error) {
	var parentID sql.NullInt64
	var name sql.NullString
	var nameLocal sql.NullString
	var path sql.NullString

	p := &ProductType{}
	err := r.Scan(
		&p.Id,
		&parentID,
		&name,
		&nameLocal,
		&path,
	)
	if err != nil {
		return nil, err
	}

	if parentID.Valid {
		p.ParentId = parentID.Int64
	}

	if name.Valid {
		p.Name = name.String
	}

	if nameLocal.Valid {
		p.NameLocal = nameLocal.String
	}

	if path.Valid {
		p.Path = path.String
	}

	return p, nil
}

func scanProductTypeRows(r *sql.Rows) (proto.Message, error) {
	productTypes := make([]*ProductType, 0)
	for r.Next() {
		var parentID sql.NullInt64
		var name sql.NullString
		var nameLocal sql.NullString
		var path sql.NullString

		p := &ProductType{}
		err := r.Scan(
			&p.Id,
			&parentID,
			&name,
			&nameLocal,
			&path,
		)
		if err != nil {
			return nil, err
		}

		if parentID.Valid {
			p.ParentId = parentID.Int64
		}

		if name.Valid {
			p.Name = name.String
		}

		if nameLocal.Valid {
			p.NameLocal = nameLocal.String
		}

		if path.Valid {
			p.Path = path.String
		}

		productTypes = append(productTypes, p)
	}

	return &ProductTypes{ProductTypes: productTypes}, nil
}

func init() {
	queries, err := getQueries("/resources/sql/product_type.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		productTypes = chacha.NewGetter("product:type",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanProductType, time.Hour),
			cacheBackend,
			cacheEncoding,
		)

		productTypeRows = chacha.NewGetter("product:types",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowsScanner(scanProductTypeRows, time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
