package datastore

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/pkg/errors"
)

func Test_CountryByStockLocation(t *testing.T) {
	type want struct {
		id       int64
		isoName  string
		name     string
		currency string
		err      string
	}

	assert := func(t *testing.T, w want, c *Country, err error) {
		if len(w.err) > 1 {
			if !strings.Contains(err.<PERSON><PERSON>(), w.err) {
				t.<PERSON>("Err: Want %s Got %s", w.err, err.<PERSON>())
			}
			return
		}

		if w.id != c.Id {
			t.<PERSON>("ID: Want %d Got %d", w.id, c.Id)
		}

		if w.isoName != c.IsoName {
			t.<PERSON>rf("IsoName: Want %s Got %s", w.isoName, c.IsoName)
		}

		if w.name != c.Name {
			t.<PERSON>rf("Name: Want %s Got %s", w.name, c.Name)
		}

		if w.currency != c.Currency {
			t.<PERSON>("Currency: Want %s Got %s", w.currency, c.Currency)
		}
	}

	for _, tt := range []struct {
		name string
		id   int
		want want
	}{
		{"ShouldReturnCorrectCountry", 3, want{1, "ID", "Indonesia", "IDR", ""}},
		{"ErrorWhenNotFound", 1, want{err: "no rows"}},
	} {
		t.Run(tt.name, func(t *testing.T) {
			c, err := CountryByStockLocationID(context.Background(), tt.id)
			if err != nil {
				if len(tt.want.err) < 1 {
					t.Error(errors.Wrap(err, fmt.Sprintf("Failed to get: %d", tt.id)))
				}
			}

			assert(t, tt.want, c, err)
		})
	}
}

func Test_CountryByISO(t *testing.T) {
	type want struct {
		id       int64
		isoName  string
		name     string
		currency string
		err      string
	}

	assert := func(t *testing.T, w want, c *Country, err error) {
		if len(w.err) > 1 {
			if !strings.Contains(err.Error(), w.err) {
				t.Errorf("Err: Want %s Got %s", w.err, err.Error())
			}
			return
		}

		if w.id != c.Id {
			t.Errorf("ID: Want %d Got %d", w.id, c.Id)
		}

		if w.isoName != c.IsoName {
			t.Errorf("IsoName: Want %s Got %s", w.isoName, c.IsoName)
		}

		if w.name != c.Name {
			t.Errorf("Name: Want %s Got %s", w.name, c.Name)
		}

		if w.currency != c.Currency {
			t.Errorf("Currency: Want %s Got %s", w.currency, c.Currency)
		}
	}

	for _, tt := range []struct {
		name string
		iso  string
		want want
	}{
		{"ShouldReturnCorrectCountry", "ID", want{1, "ID", "Indonesia", "IDR", ""}},
		{"ErrorWhenNotFound", "BKK", want{err: "no rows"}},
	} {
		t.Run(tt.name, func(t *testing.T) {
			c, err := CountryByISO(context.Background(), tt.iso)
			if err != nil {
				if len(tt.want.err) < 1 {
					t.Error(errors.Wrap(err, fmt.Sprintf("Failed to get: %s", tt.iso)))
				}
			}

			assert(t, tt.want, c, err)
		})
	}
}
