syntax = "proto3";

package datastore;

message StockItems {
    repeated StockItem items = 1;
}

message StockItem {
    int64 id = 1;
    string sku = 2;
    string name = 3;
    string name_local = 4;
    string description = 5;
    string description_local = 6;
    int64 store_id = 7;
    string in_stock = 8;
    double price = 9;
    double normal_price = 10;
    double cost = 11;
    double normal_cost = 12;
    double popularity = 13;
    double boosting_point = 14;
    int64 max_order_quantity = 15;
    int64 promotion_id = 16;
    string country_iso = 17;
    string store_location = 18;
    int64 variant_id = 19;
    repeated int64 taxon_ids = 20;
    repeated string taxon_name = 21;
    repeated string taxon_name_local = 22;
    repeated int64 product_type_ids = 23;
    repeated string product_type_name = 24;
    repeated string product_type_name_local = 25;
    string product_type_path = 26;
    int64 brand_id = 27;
    string brand_name = 28;
    string brand_name_local = 29;
    double original_promo_cost = 30;
    double avg_weight = 31;
    string display_unit = 32;
    string supermarket_unit = 33;
    double size = 34;
    string unit_price_unit = 35;
    double unit_pieces = 36;
    double price_android = 37;
    double price_ios = 38;
    double price_webapp = 39;
    double price_mobileweb = 40;
    double price_grabfresh = 41;
    int64 product_id = 42;
    repeated string user_ids = 43;
    string display_promotion_actions_combination_text = 44;
    repeated string display_banner_text = 45;
    string promotion_action_type = 46;
    string display_promotion_actions_long_text = 47;
    string display_promotion_actions_short_text = 48;
    double unit_price = 49;
    double unit_price_android = 50;
    double unit_price_ios = 51;
    double unit_price_webapp = 52;
    double unit_price_mobileweb = 53;
    double unit_price_grabfresh = 54;
    int64 buy_x_quantity = 55;
    int64 get_y_quantity = 56;
    string promotion_type = 57;
    bool stock_prediction = 58;
    string stock_prediction_updated_at = 59;
    string product_slug =60;
    string sell_natural=61;
    int64 stock_location_id =62;
    string deleted_at = 63;
    int64 max_promotion_quantity = 64;
    double raw_popularity = 65;
}
