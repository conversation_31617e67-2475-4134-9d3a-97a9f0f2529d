package datastore

import (
	"context"
	"database/sql"
	"time"

	proto "github.com/golang/protobuf/proto"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=. stock_location.proto

var stockLocations chacha.Getter

// StockLocationByID finds *StockLocation instance by its ID on Sprinkles DB
// The result is cached for 30 days
func StockLocationByID(ctx context.Context, ID int) (*StockLocation, error) {
	key, err := MarshalKey("stock-location-by-id", ID)
	if err != nil {
		return nil, err
	}

	stockLocation := &StockLocation{}
	err = stockLocations.Get(ctx, key, stockLocation)
	if err != nil {
		return nil, err
	}

	return stockLocation, nil
}

func StockLocationsByIDs(ctx context.Context, IDs []int64) ([]*StockLocation, error) {
	result := make([]proto.Message, len(IDs))
	keys := make([]string, len(IDs))

	for i, id := range IDs {
		k, err := MarshalKey("stock-location-by-id", id)
		if err != nil {
			return nil, err
		}

		keys[i] = k
		result[i] = proto.Message(&StockLocation{})
	}

	err := stockLocations.GetMany(ctx, keys, result)
	if err != nil {
		return nil, err
	}

	stockLocations := make([]*StockLocation, len(result))
	for i, r := range result {
		stockLocations[i] = r.(*StockLocation)
	}

	return stockLocations, nil
}

func scanStockLocation(r *sql.Row) (proto.Message, error) {
	var storeID sql.NullInt64
	var countryCode sql.NullString
	var supplierID sql.NullInt64

	stockLocation := &StockLocation{}
	err := r.Scan(
		&stockLocation.Id,
		&storeID,
		&countryCode,
		&supplierID,
	)
	if err != nil {
		return nil, err
	}

	if storeID.Valid {
		stockLocation.StoreId = storeID.Int64
	}

	if countryCode.Valid {
		stockLocation.CountryCode = countryCode.String
	}

	if supplierID.Valid {
		stockLocation.SupplierId = supplierID.Int64
	}

	return stockLocation, nil
}

func init() {
	queries, err := getQueries("/resources/sql/stock_location.sql")
	if err != nil {
		log.For("datastore", "init").Error(err)
	}

	configures = append(configures, func() {
		stockLocations = chacha.NewGetter("stock:locations",
			chacha.WithQueries(ro(), queries),
			chacha.WithRowScanner(scanStockLocation, 30*24*time.Hour),
			cacheBackend,
			cacheEncoding,
		)
	})
}
