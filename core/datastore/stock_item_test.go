package datastore

import (
	"context"
	"database/sql"
	"sort"
	"strings"
	"testing"

	"github.com/lib/pq/hstore"
	"github.com/stretchr/testify/assert"
)

func TestStockItemByID(t *testing.T) {
	for _, tc := range []struct {
		name                string
		args                []interface{}
		expectation         *StockItem
		errExistExpectation bool
	}{
		{
			"Stock item that in stock should exists",
			[]interface{}{"id", int64(11)},
			&StockItem{
				Id:                   11,
				Sku:                  "SKU01",
				Name:                 "P01",
				NameLocal:            "P01-ID",
				Description:          "Product 01 - apple",
				DescriptionLocal:     "P01-ID Desc",
				StoreId:              1003,
				InStock:              "true",
				Price:                12000,
				NormalPrice:          17000,
				Cost:                 10000,
				NormalCost:           15000,
				OriginalPromoCost:    13000,
				MaxOrderQuantity:     5,
				VariantId:            91,
				ProductId:            91,
				TaxonIds:             []int64{100, 101},
				TaxonName:            []string{"Taxon-01-EN", "Taxon-01-01-EN"},
				TaxonNameLocal:       []string{"Taxon-01-ID", "Taxon-01-01-ID"},
				ProductTypeIds:       []int64{9100, 9101},
				ProductTypeName:      []string{"PTY-01-EN", "PTY-01-01-EN"},
				ProductTypeNameLocal: []string{"PTY-01-ID", "PTY-01-01-ID"},
				ProductTypePath:      "pty-01-01",
				BrandId:              91,
				BrandName:            "Star",
				BrandNameLocal:       "Bintang",
				AvgWeight:            0.5,
				DisplayUnit:          "each",
				SupermarketUnit:      "kg",
				Size_:                1,
				UnitPriceUnit:        "100 g",
				UnitPieces:           1,
				PriceAndroid:         12000,
				PriceIos:             12000,
				PriceWebapp:          12000,
				PriceMobileweb:       12000,
				PriceGrabfresh:       12000,
				CountryIso:           "id",
				UserIds:              []string{},
			},
			false,
		},
		{
			"Stock item that out of stock should exists",
			[]interface{}{"id", int64(13)},
			&StockItem{
				Id:                   13,
				Sku:                  "SKU03",
				Name:                 "P03",
				NameLocal:            "P03-ID",
				Description:          "Product 03 - watermelon",
				DescriptionLocal:     "P03-ID Desc",
				StoreId:              1003,
				InStock:              "false",
				Price:                22000,
				NormalPrice:          27000,
				Cost:                 20000,
				NormalCost:           25000,
				OriginalPromoCost:    23000,
				MaxOrderQuantity:     5,
				VariantId:            93,
				ProductId:            93,
				TaxonIds:             []int64{200, 201},
				TaxonName:            []string{"Taxon-02-EN", "Taxon-02-01-EN"},
				TaxonNameLocal:       []string{"Taxon-02-ID", "Taxon-02-01-ID"},
				ProductTypeIds:       []int64{9200, 9201},
				ProductTypeName:      []string{"PTY-02-EN", "PTY-02-01-EN"},
				ProductTypeNameLocal: []string{"PTY-02-ID", "PTY-02-01-ID"},
				ProductTypePath:      "pty-03-03",
				BrandId:              91,
				BrandName:            "Star",
				BrandNameLocal:       "Bintang",
				AvgWeight:            0.5,
				DisplayUnit:          "each",
				SupermarketUnit:      "kg",
				Size_:                1,
				UnitPriceUnit:        "100 g",
				UnitPieces:           1,
				PriceAndroid:         22000,
				PriceIos:             22000,
				PriceWebapp:          22000,
				PriceMobileweb:       22000,
				PriceGrabfresh:       22000,
				CountryIso:           "id",
				UserIds:              []string{},
			},
			false,
		},
		{
			"Stock item doesnt exist",
			[]interface{}{"id", int64(55)},
			nil,
			true,
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			reality, err := StockItemByID(context.Background(), tc.args[0].(string), tc.args[1].(int64))
			errExistReality := err != nil

			if tc.errExistExpectation != errExistReality {
				t.Errorf("\nexpectation %v\nreality %v", tc.errExistExpectation, errExistReality)
				return
			}

			if !tc.errExistExpectation {
				if reality == nil {
					t.Errorf("\nexpectation %v\nreality %v", "not nill", reality)
				}
				if tc.expectation.Sku != reality.Sku {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.Sku, reality.Sku)
				}
				if tc.expectation.VariantId != reality.VariantId {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.VariantId, reality.VariantId)
				}
				if tc.expectation.ProductId != reality.ProductId {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.ProductId, reality.ProductId)
				}
				if tc.expectation.BrandId != reality.BrandId {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.BrandId, reality.BrandId)
				}
				if !arrayInt64Equal(tc.expectation.TaxonIds, reality.TaxonIds) {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.TaxonIds, reality.TaxonIds)
				}
				if !arrayInt64Equal(tc.expectation.ProductTypeIds, reality.ProductTypeIds) {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.ProductTypeIds, reality.ProductTypeIds)
				}
				if tc.expectation.StoreId != reality.StoreId {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.StoreId, reality.StoreId)
				}
				if tc.expectation.InStock != reality.InStock {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.InStock, reality.InStock)
				}
				if tc.expectation.Name != reality.Name {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.Name, reality.Name)
				}
				if tc.expectation.NameLocal != reality.NameLocal {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.NameLocal, reality.NameLocal)
				}
				if tc.expectation.Description != reality.Description {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.Description, reality.Description)
				}
				if tc.expectation.Price != reality.Price {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.Price, reality.Price)
				}
				if tc.expectation.NormalPrice != reality.NormalPrice {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.NormalPrice, reality.NormalPrice)
				}
				if tc.expectation.Cost != reality.Cost {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.Cost, reality.Cost)
				}
				if tc.expectation.NormalCost != reality.NormalCost {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.NormalCost, reality.NormalCost)
				}
				if tc.expectation.OriginalPromoCost != reality.OriginalPromoCost {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.OriginalPromoCost, reality.OriginalPromoCost)
				}

				sort.Strings(tc.expectation.TaxonName)
				sort.Strings(reality.TaxonName)
				if strings.Join(tc.expectation.TaxonName, ",") != strings.Join(reality.TaxonName, ",") {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.TaxonName, reality.TaxonName)
				}

				sort.Strings(tc.expectation.TaxonNameLocal)
				sort.Strings(reality.TaxonNameLocal)
				if strings.Join(tc.expectation.TaxonNameLocal, ",") != strings.Join(reality.TaxonNameLocal, ",") {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.TaxonNameLocal, reality.TaxonNameLocal)
				}

				sort.Strings(tc.expectation.ProductTypeName)
				sort.Strings(reality.ProductTypeName)
				if strings.Join(tc.expectation.ProductTypeName, ",") != strings.Join(reality.ProductTypeName, ",") {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.ProductTypeName, reality.ProductTypeName)
				}

				sort.Strings(tc.expectation.ProductTypeNameLocal)
				sort.Strings(reality.ProductTypeNameLocal)
				if strings.Join(tc.expectation.ProductTypeNameLocal, ",") != strings.Join(reality.ProductTypeNameLocal, ",") {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.ProductTypeNameLocal, reality.ProductTypeNameLocal)
				}

				if tc.expectation.AvgWeight != reality.AvgWeight {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.AvgWeight, reality.AvgWeight)
				}
				if tc.expectation.PriceAndroid != reality.PriceAndroid {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.PriceAndroid, reality.PriceAndroid)
				}
				if tc.expectation.PriceIos != reality.PriceIos {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.PriceIos, reality.PriceIos)
				}
				if tc.expectation.PriceWebapp != reality.PriceWebapp {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.PriceWebapp, reality.PriceWebapp)
				}
				if tc.expectation.PriceMobileweb != reality.PriceMobileweb {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.PriceMobileweb, reality.PriceMobileweb)
				}
				if tc.expectation.PriceGrabfresh != reality.PriceGrabfresh {
					t.Errorf("\nexpectation %v\nreality %v", tc.expectation.PriceGrabfresh, reality.PriceGrabfresh)
				}
				if len(tc.expectation.UserIds) > 0 && len(reality.UserIds) > 0 {
					sort.Strings(tc.expectation.UserIds)
					sort.Strings(reality.UserIds)

					if tc.expectation.UserIds[0] != reality.UserIds[0] {
						t.Errorf("\nexpectation %v\nreality %v", tc.expectation.UserIds[0], reality.UserIds[0])
					}
					if tc.expectation.UserIds[1] != reality.UserIds[1] {
						t.Errorf("\nexpectation %v\nreality %v", tc.expectation.UserIds[1], reality.UserIds[1])
					}
				} else {
					if len(tc.expectation.UserIds) != len(reality.UserIds) {
						t.Errorf("\nexpectation %v\nreality %v", len(tc.expectation.UserIds), len(reality.UserIds))
					}
				}
			}
		})
	}
}

func TestUnitPriceFactor(t *testing.T) {
	c := func(supermarketUnit, unitPieces, size, avgWeight string) hstore.Hstore {
		return hstore.Hstore{
			Map: map[string]sql.NullString{
				propertiesSupermarketUnitTag: {String: supermarketUnit, Valid: true},
				propertiesUnitPiecesTag:      {String: unitPieces, Valid: true},
				propertiesSizeTag:            {String: size, Valid: true},
				propertiesAverageWeightTag:   {String: avgWeight, Valid: true},
			},
		}
	}

	tenKTest := func(factor, p float64) bool {
		return p/factor == 10000.0
	}

	tests := []struct {
		name     string
		fields   hstore.Hstore
		want     float64
		tenKTest float64
	}{
		{"Valid Unit 10K Price => 1 500g", c("g", "1", "500", ""), 0.2, 2e3},
		{"Valid Unit 10K Price => 1 25g", c("g", "1", "25", ""), 4, 4e4},
		{"Valid Unit 10K Price => 10 40ml", c("ml", "10", "40", ""), 0.25, 2500},
		{"Valid Unit 10K Price => 10 5ml", c("ml", "10", "5", ""), 2, 2e4},
		{"Valid Unit 10K Price => 10 pack", c("pack", "1", "10", ""), 0.10, 1e3},
		{"Valid Unit 10K Price => 1 each", c("each", "1", "1", ""), 1, 1e4},
		{"Valid Unit 10K Price => 1 kg", c("kg", "1", "1", ""), 0.1, 1e3},
		{"Valid Unit 10K Price => 1 L", c("l", "1", "1", ""), 0.1, 1e3},
		{"Invalid Unit => 1 bundle", c("bundle", "", "", ""), 1, 1e4},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rsi := &RawStockItem{
				RawProperties: tt.fields,
			}
			si := &StockItem{
				SupermarketUnit: rsi.SupermarketUnit(),
				UnitPieces:      rsi.UnitPieces(),
				Size_:           rsi.Size(),
				AvgWeight:       rsi.NaturalAverageWeight(),
			}

			if got := si.UnitPriceFactor(); got != tt.want {
				t.Errorf("UnitPriceFactor:\nexpectation: %.2f\nreality: %.2f", tt.want, got)
			}

			if !tenKTest(si.UnitPriceFactor(), tt.tenKTest) {
				t.Errorf("With 10.000 Price it should display %.2f", tt.tenKTest)
			}
		})
	}
}

func TestStockItemsByIDs(t *testing.T) {
	for _, tc := range []struct {
		name                 string
		stockItemIDsParam    []int
		localeParam          string
		expectError          bool
		expectedStockItemIDs []int64
		expectedProductNames []string
	}{
		{
			"Returns relevant product",
			[]int{11, 12, 13, 14},
			"en",
			false,
			[]int64{11, 12, 13, 14},
			[]string{"P01-EN", "P02-EN", "P03-EN", "P04-EN"},
		},
		{
			"Returns relevant product, localized",
			[]int{11, 12, 13, 14},
			"id",
			false,
			[]int64{11, 12, 13, 14},
			[]string{"P01-ID", "P02-ID", "P03-ID", "P04-ID"},
		},
		{
			"Returns error when some not found",
			[]int{11, 12, 13, 140},
			"id",
			true,
			[]int64{},
			[]string{},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := StockItemsByIDs(ctx, tc.localeParam, tc.stockItemIDsParam)
			assert.Equal(t, tc.expectError, err != nil)
			if tc.expectError {
				return
			}

			resultIDs := []int64{}
			resultNames := []string{}
			for _, si := range result {
				resultIDs = append(resultIDs, si.Id)
				resultNames = append(resultNames, si.NameLocal)
			}

			assert.Equal(t, tc.expectedStockItemIDs, resultIDs)
			assert.Equal(t, tc.expectedProductNames, resultNames)
		})
	}
}

func TestGetStockItemsByIDs(t *testing.T) {
	for _, tc := range []struct {
		name                 string
		stockItemIDsParam    []int64
		localeParam          string
		expectError          bool
		expectedStockItemIDs []int64
		expectedProductNames []string
	}{
		{
			"Returns relevant product",
			[]int64{11, 12, 13, 14},
			"en",
			false,
			[]int64{11, 12, 13, 14},
			[]string{"P01-EN", "P02-EN", "P03-EN", "P04-EN"},
		},
		{
			"Returns relevant product, localized",
			[]int64{11, 12, 13, 14},
			"id",
			false,
			[]int64{11, 12, 13, 14},
			[]string{"P01-ID", "P02-ID", "P03-ID", "P04-ID"},
		},
		{
			"Returns relevant when some not found",
			[]int64{11, 12, 13, 140},
			"id",
			false,
			[]int64{11, 12, 13, 0},
			[]string{"P01-ID", "P02-ID", "P03-ID", ""},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := GetStockItemByIDs(ctx, "ID", tc.stockItemIDsParam, tc.localeParam)
			assert.Equal(t, tc.expectError, err != nil)
			if tc.expectError {
				return
			}

			resultIDs := []int64{}
			resultNames := []string{}
			for _, si := range result {
				resultIDs = append(resultIDs, si.Id)
				resultNames = append(resultNames, si.NameLocal)
			}

			assert.Equal(t, tc.expectedStockItemIDs, resultIDs)
			assert.Equal(t, tc.expectedProductNames, resultNames)
		})
	}
}

func TestDisplayAvgWeight(t *testing.T) {
	for _, tc := range []struct {
		name      string
		stockItem *StockItem
		expected  string
	}{
		{
			"Formats average weight",
			&StockItem{
				AvgWeight:       1.161,
				SupermarketUnit: "kg",
			},
			"~ 1.161 kg",
		},
	} {
		// return fmt.Sprintf("~ %s %s", strconv.FormatFloat(float64(s.AvgWeight), 'f', -1, 64), s.SupermarketUnit)
		t.Run(tc.name, func(t *testing.T) {
			s := tc.stockItem.DisplayAvgWeight()
			assert.Equal(t, tc.expected, s)
		})
	}
}
