package hubble_test

import (
	"context"
	"os"
	"testing"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"happyfresh.io/hubble/lib/rpc"
	"happyfresh.io/search/core/hubble"
	hfgrpc "happyfresh.io/search/lib/grpc"
)

func TestHubbleClient(t *testing.T) {
	tt := []struct {
		name      string
		f         func(context.Context, *rpc.TranslateIdRequest, ...grpc.CallOption) (*rpc.TranslateIdResponse, error)
		input     []int64
		result    []*rpc.TranslatedID
		isErrgRPC bool
	}{
		{
			"Returns translated IDs",
			func(ctx context.Context, r *rpc.TranslateIdRequest, opts ...grpc.CallOption) (*rpc.TranslateIdResponse, error) {
				return &rpc.TranslateIdResponse{
					TranslatedIds: []*rpc.TranslatedID{
						{
							GlobalProductId:    9,
							SpreeProductId:     8,
							SpreeVariantId:     7,
							SpreeProductTypeId: 6,
							SpreeBrandId:       5,
							DeliveredQuantity:  29,
						},
					},
				}, nil
			},
			[]int64{9},
			[]*rpc.TranslatedID{
				{
					GlobalProductId:    9,
					SpreeProductId:     8,
					SpreeVariantId:     7,
					SpreeProductTypeId: 6,
					SpreeBrandId:       5,
					DeliveredQuantity:  29,
				},
			},
			false,
		},
		{
			"Returns gRPC error",
			func(ctx context.Context, r *rpc.TranslateIdRequest, opts ...grpc.CallOption) (*rpc.TranslateIdResponse, error) {
				return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
			},
			[]int64{-1},
			nil,
			true,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			h, err := hubble.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				hubble.WithgRPCClient(&hubble.ClientMock{
					TranslateIDFunc: tc.f,
				}),
				hubble.WithSupplierCache(&cacheMock{}),
			)
			if err != nil {
				t.Fatal(err)
			}

			res, err := h.TranslateIDs(ctx, tc.input)
			if err != nil {
				_, ok := status.FromError(errors.Cause(err))
				assert := convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
				}
			}

			assert := convey.ShouldResemble(res, tc.result)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestHubbleClientNoRpc(t *testing.T) {
	h, _ := hubble.New(
		"dns:///localhost:443",
		os.Getenv("SSRV_REDIS_TEST_DSN"),
		hubble.WithSecure(true),
		hubble.WithSupplierCache(&cacheMock{}),
	)

	assert.NotNil(t, h)
	_, err := h.TranslateIDs(context.Background(), []int64{1, 2})

	assert.Error(t, err)
}

type cacheMock struct {
	GetFunc     func(ctx context.Context, key string, target proto.Message) error
	GetManyFunc func(ctx context.Context, key []string, targets []proto.Message) error
}

func (c *cacheMock) Get(ctx context.Context, key string, target proto.Message) error {
	if c.GetFunc != nil {
		return c.GetFunc(ctx, key, target)
	}

	return nil
}

func (c *cacheMock) GetMany(ctx context.Context, key []string, targets []proto.Message) error {
	if c.GetManyFunc != nil {
		return c.GetManyFunc(ctx, key, targets)
	}

	return nil
}

func TestGetSupplier(t *testing.T) {
	testCases := []struct {
		name                 string
		stockLocationIDInput int64
		cacheFunc            func(ctx context.Context, key string, target proto.Message) error
		supplier             *rpc.Supplier
	}{
		{
			"Return supplier detail",
			3,
			func(ctx context.Context, key string, target proto.Message) error {
				s := target.(*rpc.Supplier)
				s.SupplierId = 52
				s.PreferencesShowGlobalSearchPrice = false

				return nil
			},
			&rpc.Supplier{
				SupplierId:                       52,
				PreferencesShowGlobalSearchPrice: false,
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()
			h, err := hubble.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				hubble.WithSupplierCache(&cacheMock{
					GetFunc: testCase.cacheFunc,
				}),
				hubble.WithgRPCClient(&hubble.ClientMock{}),
			)
			if err != nil {
				t.Fatal(err)
			}

			res, err := h.GetSupplier(ctx, testCase.stockLocationIDInput)
			if err != nil {
				t.Error(err)
			}

			assert := assert.New(t)

			assert.Equal(testCase.supplier.SupplierId, res.SupplierId)
			assert.Equal(testCase.supplier.PreferencesShowGlobalSearchPrice, res.PreferencesShowGlobalSearchPrice)
		})
	}
}

func TestGetCountry(t *testing.T) {
	testCases := []struct {
		name            string
		countryIsoName  string
		stockLocationID int64
		cacheFunc       func(ctx context.Context, key string, target proto.Message) error
		country         *rpc.Country
	}{
		{
			"Return supplier detail",
			"ID",
			3,
			func(ctx context.Context, key string, target proto.Message) error {
				s := target.(*rpc.Country)
				s.CountryId = 1
				s.IsoName = "ID"

				return nil
			},
			&rpc.Country{
				CountryId: 1,
				IsoName:   "ID",
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()
			h, err := hubble.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				hubble.WithCountryCache(&cacheMock{
					GetFunc: testCase.cacheFunc,
				}),
				hubble.WithgRPCClient(&hubble.ClientMock{}),
			)
			if err != nil {
				t.Fatal(err)
			}

			res, err := h.GetCountryByIso(ctx, testCase.countryIsoName)
			if err != nil {
				t.Error(err)
			}

			assert := assert.New(t)

			assert.Equal(testCase.country.CountryId, res.CountryId)
			assert.Equal(testCase.country.IsoName, res.IsoName)
		})
	}
}
