package hubble

import (
	"context"
	"crypto/tls"
	"strings"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"github.com/sony/gobreaker"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/status"
	"happyfresh.io/hubble/lib/rpc"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/lib/chacha"
	r "happyfresh.io/search/lib/chacha/redis"
	"happyfresh.io/search/lib/trace/grpctrace"
)

var (
	inst *Client
)

type Client struct {
	c             rpc.ApiClient
	useSecure     bool
	cb            *gobreaker.CircuitBreaker
	supplierCache chacha.Getter
	countryCache  chacha.Getter
}

func (c *Client) isSuccessful(err error) bool {
	if err == nil {
		return true
	}

	s, ok := status.FromError(errors.Cause(err))
	if !ok {
		return false
	}

	if s.Code() == codes.NotFound || s.Code() == codes.InvalidArgument {
		return true
	}

	return false
}

type Option func(*Client)

func New(address string, redisDSN string, opts ...Option) (*Client, error) {
	if inst != nil {
		return inst, nil
	}

	h := &Client{}
	for _, apply := range opts {
		apply(h)
	}

	if h.c == nil {
		secureOpt := grpc.WithInsecure()
		if h.useSecure {
			secureOpt = grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{}))
		}

		conn, err := grpc.Dial(
			address,
			secureOpt,
			grpc.WithUnaryInterceptor(
				grpctrace.ChainUnaryClientInterceptor(true, true),
			),
			grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy":"round_robin"}`),
		)
		if err != nil {
			return nil, errors.Wrap(err, "[hubble] failed to connect")
		}

		h.c = rpc.NewApiClient(conn)
	}

	if h.supplierCache == nil {
		h.supplierCache = chacha.NewGetter(
			"hubble:supplier",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN)),
			chacha.WithKeyRoundTripper(h.SupplierRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
		)
	}

	if h.countryCache == nil {
		h.countryCache = chacha.NewGetter(
			"hubble:country",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN)),
			chacha.WithKeyRoundTripper(h.CountryRoundTripper(), 30*24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
		)
	}

	h.cb = gobreaker.NewCircuitBreaker(gobreaker.Settings{
		Name:         "SSRV-Spree-Proxy-Client",
		Interval:     time.Minute,
		Timeout:      time.Minute,
		IsSuccessful: h.isSuccessful,
	})

	return h, nil
}

func (c *Client) SupplierRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		supplierID := str.MaybeString(args[0]).Int64()

		sp, err := c.c.GetSupplier(ctx, &rpc.GetSupplierRequest{
			SupplierId: supplierID,
		})
		if err != nil {
			status, ok := status.FromError(err)
			if ok {
				if status.Code() == codes.InvalidArgument || status.Code() == codes.NotFound {
					log.Warnf("Use empty cache on supplierID %d", supplierID)
					return &rpc.Supplier{}, nil
				}
			}

			return nil, errors.Wrap(err, "[hubble] error fetching supplier data")
		}

		return sp, nil
	})
}

func (c *Client) GetSupplier(ctx context.Context, supplierID int64) (*rpc.Supplier, error) {
	key, err := cacheKeyEncoder(ctx, "supplier-id", supplierID)
	if err != nil {
		return nil, err
	}

	resp := &rpc.Supplier{}
	err = c.supplierCache.Get(ctx, key, resp)
	if err != nil {
		return nil, errors.Wrap(err, "[hubble] error fetching supplier data")
	}

	return resp, nil
}

func (c *Client) CountryRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		countryIsoName := str.MaybeString(args[0]).String()
		stockLocationID := str.MaybeString(args[1]).Int64()

		ct, err := c.c.GetCountry(ctx, &rpc.GetCountryRequest{
			IsoName:         countryIsoName,
			StockLocationId: stockLocationID,
		})
		if err != nil {
			return nil, errors.Wrap(err, "[hubble] error fetching country data")
		}

		return ct, nil
	})
}

func (c *Client) GetCountryByIso(ctx context.Context, countryIsoName string) (*rpc.Country, error) {
	countryIsoName = strings.ToUpper(countryIsoName)

	key, err := cacheKeyEncoder(ctx, "iso-name", countryIsoName, 0)
	if err != nil {
		return nil, err
	}

	resp := &rpc.Country{}
	err = c.countryCache.Get(ctx, key, resp)
	if err != nil {
		return nil, errors.Wrap(err, "[hubble] error fetching supplier data")
	}

	return resp, nil
}

func (c *Client) GetCountryByStockLocationID(ctx context.Context, stockLocationID int64) (*rpc.Country, error) {
	key, err := cacheKeyEncoder(ctx, "stock-location-id", "", stockLocationID)
	if err != nil {
		return nil, err
	}

	resp := &rpc.Country{}
	err = c.countryCache.Get(ctx, key, resp)
	if err != nil {
		return nil, errors.Wrap(err, "[hubble] error fetching country data")
	}

	return resp, nil
}

func (c *Client) TranslateIDs(ctx context.Context, productIDs []int64) ([]*rpc.TranslatedID, error) {
	res, err := c.cb.Execute(func() (interface{}, error) {
		r, err := c.c.TranslateIds(ctx, &rpc.TranslateIdRequest{ProductIds: productIDs})
		if err != nil {
			return nil, errors.Wrap(err, "[hubble] error fetching IDs")
		}

		return r, nil
	})
	if err != nil {
		return nil, err
	}

	r, _ := res.(*rpc.TranslateIdResponse)

	return r.TranslatedIds, nil
}

func WithgRPCClient(apiClient rpc.ApiClient) Option {
	return func(c *Client) {
		c.c = apiClient
	}
}

func WithSecure(useSecure bool) Option {
	return func(c *Client) {
		c.useSecure = useSecure
	}
}

func WithSupplierCache(cache chacha.Getter) Option {
	return func(c *Client) {
		c.supplierCache = cache
	}
}

func WithCountryCache(cache chacha.Getter) Option {
	return func(c *Client) {
		c.countryCache = cache
	}
}

func cacheKeyEncoder(ctx context.Context, s string, args ...interface{}) (string, error) {
	sb := strings.Builder{}
	sb.WriteString(s)
	for _, arg := range args {
		sb.WriteString(":")
		sb.WriteString(str.MaybeString(arg).String())
	}

	return sb.String(), nil
}

func cacheKeyDecoder(ctx context.Context, s string) (string, []interface{}, error) {
	keys := strings.Split(s, ":")

	var getterName string
	args := make([]interface{}, len(keys)-1)
	for i, key := range keys {
		if i == 0 {
			getterName = key
			continue
		}

		args[i-1] = key
	}

	return getterName, args, nil
}
