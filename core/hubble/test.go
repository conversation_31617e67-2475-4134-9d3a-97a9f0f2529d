package hubble

import (
	"context"

	"google.golang.org/grpc"
	"happyfresh.io/hubble/lib/rpc"
)

type ClientMock struct {
	TranslateIDFunc func(context.Context, *rpc.TranslateIdRequest, ...grpc.CallOption) (*rpc.TranslateIdResponse, error)
	GetCountryFunc  func(ctx context.Context, in *rpc.GetCountryRequest, opts ...grpc.CallOption) (*rpc.Country, error)
}

func (c *ClientMock) Ping(ctx context.Context, in *rpc.NullRequest, opts ...grpc.CallOption) (*rpc.PingResponse, error) {
	return &rpc.PingResponse{Pong: "Pong"}, nil
}

func (c *ClientMock) TranslateIds(ctx context.Context, in *rpc.TranslateIdRequest, opts ...grpc.CallOption) (*rpc.TranslateIdResponse, error) {
	if c.TranslateIDFunc != nil {
		return c.TranslateIDFunc(ctx, in, opts...)
	}

	return &rpc.TranslateIdResponse{}, nil
}

func (c *ClientMock) TranslateStockLocationId(ctx context.Context, in *rpc.TranslateStockLocationIdRequest, opts ...grpc.CallOption) (*rpc.TranslateStockLocationIdResponse, error) {
	return &rpc.TranslateStockLocationIdResponse{}, nil
}

func (c *ClientMock) GetSupplier(ctx context.Context, in *rpc.GetSupplierRequest, opts ...grpc.CallOption) (*rpc.Supplier, error) {
	return &rpc.Supplier{}, nil
}

func (c *ClientMock) GetStockItemsPopularity(ctx context.Context, in *rpc.GetStockItemsPopularityRequest, opts ...grpc.CallOption) (*rpc.GetStockItemsPopularityResponse, error) {
	return &rpc.GetStockItemsPopularityResponse{}, nil
}

func (c *ClientMock) GetStockItemPopularity(ctx context.Context, in *rpc.GetStockItemPopularityRequest, opts ...grpc.CallOption) (*rpc.StoreProduct, error) {
	return &rpc.StoreProduct{}, nil
}

func (c *ClientMock) GetStoreProductPromotion(ctx context.Context, in *rpc.StoreProduct, opts ...grpc.CallOption) (*rpc.StoreProductPromotion, error) {
	return &rpc.StoreProductPromotion{}, nil
}

func (c *ClientMock) GetCountry(ctx context.Context, in *rpc.GetCountryRequest, opts ...grpc.CallOption) (*rpc.Country, error) {
	if c.GetCountryFunc != nil {
		return c.GetCountryFunc(ctx, in, opts...)
	}

	return &rpc.Country{}, nil
}
