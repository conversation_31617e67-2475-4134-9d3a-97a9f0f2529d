package stemmer

import (
	"fmt"
	"strings"

	"github.com/kljensen/snowball"
)

// StemQuery is an experimental API to stem user query term
// currently only supports up to two words query term
// returns all combinations of original and stemmed words but the original
// if the stemmed query result is the same as the original query then empty result is returned
func StemQuery(query string) []string {
	result := []string{}
	original := strings.Split(query, " ")
	if len(original) > 2 {
		return result
	}

	stemmed := make([]string, len(original))
	for i, o := range original {
		s, err := snowball.Stem(o, "english", false)
		if err != nil {
			continue
		}

		stemmed[i] = s
	}

	if len(stemmed) == 1 {
		if stemmed[0] != query {
			return stemmed
		}

		return result
	}

	uniq := map[string]struct{}{}

	initial := fmt.Sprintf("%s %s", stemmed[0], stemmed[1])
	if initial == query {
		return result
	}
	result = append(result, initial)
	uniq[initial] = struct{}{}

	if stemmed[0] != original[0] {
		s := fmt.Sprintf("%s %s", stemmed[0], original[1])
		if _, ok := uniq[s]; !ok {
			result = append(result, s)
			uniq[s] = struct{}{}
		}
	}

	if stemmed[1] != original[1] {
		s := fmt.Sprintf("%s %s", original[0], stemmed[1])
		if _, ok := uniq[s]; !ok {
			result = append(result, s)
			uniq[s] = struct{}{}
		}
	}

	return result
}
