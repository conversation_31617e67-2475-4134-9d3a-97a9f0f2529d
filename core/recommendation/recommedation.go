package recommendation

import (
	"context"
	"encoding/json"
	fmt "fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=plugins=grpc:. recommendation.proto

var (
	std = &srv{
		client:     &http.Client{Timeout: 5 * time.Second},
		boostConst: 2,
		topN:       3,
	}

	keyEncoding = chacha.WithKeyEncoding(chacha.EncodeKeyFunc(
		func(ctx context.Context, s string, args ...interface{}) (string, error) {
			return s, nil
		}), chacha.DecodeKeyFunc(
		func(ctx context.Context, s string) (string, []interface{}, error) {
			return s, nil, nil
		}),
	)
)

type RecommendationService interface {
	Get(context.Context, string) (map[string]float64, error)
}

func Get(ctx context.Context, q string) (map[string]float64, error) {
	return std.Get(ctx, q)
}

type srv struct {
	client     *http.Client
	getter     chacha.Getter
	url        string
	boostConst float64
	topN       int

	onceGetter sync.Once
}

func (s *srv) Get(ctx context.Context, query string) (map[string]float64, error) {
	s.onceGetter.Do(s.initGetter)
	recommendation := &RecommendationResponse{}
	err := s.getter.Get(ctx, query, recommendation)
	if err != nil {
		return nil, errors.Wrapf(err, "[recommendation] Error getting rec for query: %s", query)
	}

	res := make(map[string]float64)
	if len(recommendation.Ranks) >= s.topN {
		recommendation.Ranks = recommendation.Ranks[:s.topN]
	}

	for _, i := range recommendation.Ranks {
		productTypeID := strconv.FormatInt(i.ProductTypeId, 10)
		res[productTypeID] = float64(i.Score) + s.boostConst
	}

	return res, nil
}

func (s *srv) RoundTrip(ctx context.Context, query string, args ...interface{}) (proto.Message, error) {
	request, err := s.newRequest("product_type_preferences", query)
	if err != nil {
		return nil, err
	}

	response, err := s.client.Do(request.WithContext(ctx))
	if err != nil {
		return nil, err
	}

	bod, err := ioutil.ReadAll(response.Body)
	defer response.Body.Close()
	if err != nil {
		return nil, errors.Wrap(err, "[recommendation] error searching recommendation")
	}

	if response.StatusCode != http.StatusOK {
		return nil, errors.Errorf("[recommendation] error searching recommendation: [%s]", string(bod))
	}

	recommendation := &RecommendationResponse{}
	err = json.Unmarshal(bod, recommendation)
	if err != nil {
		return nil, errors.Wrap(err, "[recommendation] Failed to decode response")
	}

	return recommendation, nil
}

func (s *srv) initGetter() {
	if s.getter == nil {
		s.getter = chacha.NewGetter("recommendation:product:type",
			chacha.WithKeyRoundTripper(s, 24*time.Hour),
			keyEncoding,
		)
	}
}

func (s *srv) newRequest(path, query string) (*http.Request, error) {
	request, err := http.NewRequest(http.MethodGet, fmt.Sprintf("%s%s", s.url, path), nil)
	if err != nil {
		return request, errors.Wrap(err, "[recommendation] failed to prepare request")
	}

	request.Header.Add("content-type", "application/json")

	q := request.URL.Query()
	q.Add("keyword", strings.ToLower(query))
	request.URL.RawQuery = q.Encode()

	return request, nil
}

type Option func(srv *srv)

func Dial(address string, opts ...Option) RecommendationService {
	srv := &srv{
		url:        address,
		client:     std.client,
		boostConst: std.boostConst,
		topN:       std.topN,
	}

	for _, opt := range opts {
		opt(srv)
	}

	return srv
}

func InitDefault(opts ...Option) {
	for _, apply := range opts {
		apply(std)
	}
}

func WithAddress(address string) Option {
	return func(s *srv) {
		s.url = address
	}
}

func WithTopN(n int) Option {
	return func(s *srv) {
		s.topN = n
	}
}

func WithHTTPClient(c *http.Client) Option {
	return func(s *srv) {
		s.client = c
	}
}

func WithBoostConst(c float64) Option {
	return func(s *srv) {
		s.boostConst = c
	}
}

func WithCacheOptions(name string, opts ...chacha.DialOption) Option {
	return func(s *srv) {
		s.getter = chacha.NewGetter("recommendation:product:type",
			chacha.WithCache(name, opts...),
			chacha.WithKeyRoundTripper(s, 24*time.Hour),
			keyEncoding,
		)
	}
}
