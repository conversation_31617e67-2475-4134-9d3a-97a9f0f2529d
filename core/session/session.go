package session

import (
	"context"
	"strings"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/pkg/errors"
	"github.com/rs/xid"
	"happyfresh.io/chacha"
	r "happyfresh.io/chacha/redis"
	"happyfresh.io/lib/str"
)

var (
	letters         = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
	sessionIDGetter chacha.Getter
	queryIDGetter   chacha.Getter
)

func GenerateSearchSessionID(ctx context.Context, identifier string) (string, error) {
	key, err := cacheKeyEncoder(ctx, "id", identifier)
	if err != nil {
		return "", errors.Wrap(err, "[session] error creating search ID")
	}

	resp := &wrappers.StringValue{}
	err = sessionIDGetter.Get(ctx, key, resp)
	if err != nil {
		return "", errors.Wrap(err, "[session] error creating search ID")
	}

	return resp.Value, nil
}

func GenerateQueryID(ctx context.Context, identifier, query, sort string, stockLocationID, taxonID, brandID int64) (string, error) {
	key, err := cacheKeyEncoder(ctx, "id", identifier, query, sort, stockLocationID, taxonID, brandID)
	if err != nil {
		return "", errors.Wrap(err, "[session] error creating query ID")
	}

	resp := &wrappers.StringValue{}
	err = queryIDGetter.Get(ctx, key, resp)
	if err != nil {
		return "", errors.Wrap(err, "[session] error creating query ID")
	}

	return resp.Value, nil
}

func sessionIDRT() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		id := xid.New()
		w := &wrappers.StringValue{Value: id.String()}

		return w, nil
	})
}

func cacheKeyEncoder(ctx context.Context, s string, args ...interface{}) (string, error) {
	sb := strings.Builder{}
	sb.WriteString(s)
	for _, arg := range args {
		sb.WriteString(":")
		sb.WriteString(str.MaybeString(arg).String())
	}

	return sb.String(), nil
}

func cacheKeyDecoder(ctx context.Context, s string) (string, []interface{}, error) {
	keys := strings.Split(s, ":")

	var getterName string
	args := make([]interface{}, len(keys)-1)
	for i, key := range keys {
		if i == 0 {
			getterName = key
			continue
		}

		args[i-1] = key
	}

	return getterName, args, nil
}

func Setup(redisDSN string, sessionDuration time.Duration) {
	sessionIDGetter = chacha.NewGetter(
		"session",
		chacha.WithCache("redis", r.WithRedisURL(redisDSN)),
		chacha.WithKeyRoundTripper(sessionIDRT(), sessionDuration),
		chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
	)

	queryIDGetter = chacha.NewGetter(
		"query",
		chacha.WithCache("redis", r.WithRedisURL(redisDSN)),
		chacha.WithKeyRoundTripper(sessionIDRT(), sessionDuration),
		chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
	)
}
