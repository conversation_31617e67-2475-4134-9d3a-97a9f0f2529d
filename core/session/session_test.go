package session_test

import (
	"context"
	"testing"
	"time"

	"github.com/alicebob/miniredis"
	"github.com/stretchr/testify/assert"
	"happyfresh.io/search/core/session"
)

func TestGenerateSessionID(t *testing.T) {
	mr, err := miniredis.Run()
	if err != nil {
		t.Error(err)
		return
	}

	session.Setup("redis://"+mr.<PERSON>dr(), 5*time.Second)

	for _, tc := range []struct {
		name        string
		identifier1 string
		identifier2 string
		sleepTime   time.Duration
		assertFunc  func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string)
	}{
		{
			"returns same session id when identifier is the same and within sleep time",
			"qweasd", "qweasd",
			3 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.Equal(sessionID1, sessionID2)
			},
		},
		{
			"returns different session id when identifier is the same and not within sleep time",
			"zxcasd", "zxcasd",
			6 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
		{
			"returns different session id when identifier is different and within sleep time",
			"qweqwe", "rtyrty",
			3 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
		{
			"returns different session id when identifier is different and not within sleep time",
			"zxczxc", "cvbcvb",
			6 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			mr.FastForward(10 * time.Second)

			ctx := context.Background()
			assertion := assert.New(t)
			sessionID1, err := session.GenerateSearchSessionID(ctx, tc.identifier1)
			if err != nil {
				t.Error(err)
			}

			mr.FastForward(tc.sleepTime)

			sessionID2, err := session.GenerateSearchSessionID(ctx, tc.identifier2)
			if err != nil {
				t.Error(err)
			}

			tc.assertFunc(t, assertion, sessionID1, sessionID2)
		})
	}
}

func TestGenerateQueryID(t *testing.T) {
	mr, err := miniredis.Run()
	if err != nil {
		t.Error(err)
		return
	}

	session.Setup("redis://"+mr.Addr(), 5*time.Second)

	for _, tc := range []struct {
		name                                 string
		identifier1, query1, sort1           string
		stockLocationID1, taxonID1, brandID1 int64
		identifier2, query2, sort2           string
		stockLocationID2, taxonID2, brandID2 int64
		sleepTime                            time.Duration
		assertFunc                           func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string)
	}{
		{
			"returns same query id when params are the same and within sleep time",
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			3 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.Equal(sessionID1, sessionID2)
			},
		},
		{
			"returns different query id when params are the same and not within sleep time",
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			6 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
		{
			"returns different query id when query is not the same and within sleep time",
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			"qweasd", "oreo thins", "relevance desc", 3, 0, 0,
			3 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
		{
			"returns different query id when sort is not the same and within sleep time",
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			"qweasd", "oreo", "name desc", 3, 0, 0,
			3 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
		{
			"returns different query id when store is not the same and within sleep time",
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			"qweasd", "oreo", "relevance desc", 4, 0, 0,
			3 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
		{
			"returns different query id when taxon filter is not the same and within sleep time",
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			"qweasd", "oreo", "relevance desc", 3, 1, 0,
			3 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
		{
			"returns different query id when brand filter is not the same and within sleep time",
			"qweasd", "oreo", "relevance desc", 3, 0, 0,
			"qweasd", "oreo", "relevance desc", 3, 0, 1,
			3 * time.Second,
			func(t *testing.T, assertion *assert.Assertions, sessionID1 string, sessionID2 string) {
				assertion.NotEqual(sessionID1, sessionID2)
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			mr.FastForward(10 * time.Second)

			ctx := context.Background()
			assertion := assert.New(t)
			sessionID1, err := session.GenerateQueryID(ctx, tc.identifier1, tc.query1, tc.sort1, tc.stockLocationID1, tc.taxonID1, tc.brandID1)
			if err != nil {
				t.Error(err)
			}

			mr.FastForward(tc.sleepTime)

			sessionID2, err := session.GenerateQueryID(ctx, tc.identifier2, tc.query2, tc.sort2, tc.stockLocationID2, tc.taxonID2, tc.brandID2)
			if err != nil {
				t.Error(err)
			}

			tc.assertFunc(t, assertion, sessionID1, sessionID2)
		})
	}
}
