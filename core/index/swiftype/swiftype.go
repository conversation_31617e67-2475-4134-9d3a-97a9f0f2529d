package swiftype

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"happyfresh.io/lib/str"

	"github.com/pkg/errors"
	"github.com/valyala/bytebufferpool"
	ddhttp "gopkg.in/DataDog/dd-trace-go.v1/contrib/net/http"
	iface "happyfresh.io/search/core/index"
)

type index struct {
	key     string
	baseURL *url.URL
	client  *http.Client
}

type dialer struct {
	rt http.RoundTripper
}

func (i *index) Store(ctx context.Context, data ...interface{}) error {
	b := bytebufferpool.Get()
	b.Reset()
	defer bytebufferpool.Put(b)

	err := json.NewEncoder(b).Encode(data)
	if err != nil {
		return errors.Wrap(err, "[swiftype] failed to encode data")
	}

	req, err := i.newRequest(http.MethodPost, "documents", bytes.NewReader(b.B))
	if err != nil {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	resp, err := i.sendRequest(req.WithContext(ctx))
	if err != nil {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	var docResp []*documentResponse
	body, err := i.readAll(resp)
	if err != nil {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	if resp.StatusCode != http.StatusOK {
		return iface.NewError(resp.StatusCode, errors.Errorf("[switype] error searching document: [%s]", string(body)))
	}

	err = json.Unmarshal(body, &docResp)
	if err != nil {
		return errors.Wrap(err, "[swiftype] error storing document")
	}

	var errs []string
	for _, doc := range docResp {
		if len(doc.Errs) > 0 {
			errs = append(errs, fmt.Sprintf("{%s : [%s]}", doc.ID, strings.Join(doc.Errs, ", ")))
		}
	}

	if len(errs) > 0 {
		return errors.Errorf("[switype] error storing document: [%s]", strings.Join(errs, ", "))
	}

	return nil
}

func (i *index) Search(ctx context.Context, options ...iface.SearchOption) (*iface.SearchResponse, error) {
	q := &Q{}

	for _, apply := range options {
		apply(q)
	}

	b := bytebufferpool.Get()
	b.Reset()
	defer bytebufferpool.Put(b)

	err := json.NewEncoder(b).Encode(q)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] failed to encode data")
	}

	req, err := i.newRequest(http.MethodPost, "search", bytes.NewReader(b.B))
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] error searching document")
	}

	resp, err := i.sendRequest(req.WithContext(ctx))
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] error searching document")
	}

	body, err := i.readAll(resp)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] error searching document")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, iface.NewError(resp.StatusCode, errors.Errorf("[switype] error searching document: [%s]", string(body)))
	}

	t := &searchResponse{}
	err = json.Unmarshal(body, t)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] Failed to decode response")
	}

	searchResponse := &iface.SearchResponse{
		Meta: &iface.Meta{
			Page:       t.Meta.Page.Current,
			PageSize:   t.Meta.Page.Size,
			TotalPages: t.Meta.Page.TotalPages,
			Count:      int64(len(t.Results)),
			TotalCount: t.Meta.Page.TotalResults,
			SearchID:   t.Meta.RequestID,
		},
		StockItems: make([]*iface.StockItem, len(t.Results)),
		Filters:    make(map[string][]interface{}),
	}

	for i, r := range t.Results {
		stockItem := &iface.StockItem{
			ID:       str.MaybeString(r.ID.Raw).Int64(),
			Score:    str.MaybeString(r.Meta.Score).Float64(),
			GroupKey: r.GroupKey,
		}
		if r.ProductID != nil {
			stockItem.ProductID = str.MaybeString(r.ProductID.Raw).Int64()
		}
		if r.VariantID != nil {
			stockItem.VariantID = str.MaybeString(r.VariantID.Raw).Int64()
		}
		if r.InStock != nil {
			stockItem.InStock = str.MaybeString(r.InStock.Raw).String()
		}
		if r.RawPopularity != nil {
			stockItem.RawPopularity = str.MaybeString(r.RawPopularity.Raw).Float64()
		}

		searchResponse.StockItems[i] = stockItem
	}

	for k, v := range t.Facets {
		datas := make([]interface{}, len(v[0].Data))
		for i, data := range v[0].Data {
			datas[i] = data.Value
		}

		searchResponse.Filters[k] = datas
	}

	return searchResponse, nil
}

func (i *index) SuggestSearch(ctx context.Context, options ...iface.SearchOption) ([]string, error) {
	q := &Q{}

	for _, apply := range options {
		apply(q)
	}

	fields := []string{"name_local", "taxon_name"}
	if strings.ToLower(q.locale) == "en" || q.locale == "" {
		fields = []string{"name", "taxon_name"}
	}

	qs := &QS{
		QueryString: q.QueryString,
		Size:        5,
		Types: Types{
			Documents: Documents{
				fields,
			},
		},
	}
	b := bytebufferpool.Get()
	b.Reset()
	defer bytebufferpool.Put(b)

	err := json.NewEncoder(b).Encode(qs)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] failed to encode data")
	}

	req, err := i.newRequest(http.MethodPost, "query_suggestion", bytes.NewReader(b.B))
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] error searching suggestion")
	}

	resp, err := i.sendRequest(req.WithContext(ctx))
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] error searching suggestion")
	}

	body, err := i.readAll(resp)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] error searching suggestion")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, iface.NewError(resp.StatusCode, errors.Errorf("[switype] error searching suggestion: [%s]", string(body)))
	}

	ssr := &searchSuggestionResponse{}
	err = json.Unmarshal(body, ssr)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] Failed to decode response")
	}

	querySuggestion := make([]string, len(ssr.Results.Documents))
	for i, s := range ssr.Results.Documents {
		querySuggestion[i] = str.MaybeString(s.Suggestion).String()
	}

	return querySuggestion, nil
}

func (i *index) Click(ctx context.Context, data interface{}) error {
	b := bytebufferpool.Get()
	b.Reset()
	defer bytebufferpool.Put(b)

	err := json.NewEncoder(b).Encode(data)
	if err != nil {
		return errors.Wrap(err, "[swiftype] failed to encode data")
	}

	req, err := i.newRequest(http.MethodPost, "click", bytes.NewReader(b.B))
	if err != nil {
		return errors.Wrap(err, "[swiftype] error track query")
	}

	resp, err := i.sendRequest(req.WithContext(ctx))
	if err != nil {
		return errors.Wrap(err, "[swiftype] error track query")
	}

	if resp.StatusCode != http.StatusOK {
		return iface.NewError(resp.StatusCode, errors.New("[swiftype] error track query "+resp.Status))
	}

	return nil
}

func (i *index) readAll(r *http.Response) (res []byte, err error) {
	body := r.Body
	if r.Header.Get("content-encoding") == "gzip" {
		body, err = gzip.NewReader(r.Body)
		if err != nil {
			return nil, err
		}
	}
	defer body.Close()

	return ioutil.ReadAll(body)
}

func (i *index) sendRequest(r *http.Request) (*http.Response, error) {
	return i.client.Do(r)
}

func (i *index) newRequest(method, path string, body io.Reader) (*http.Request, error) {
	path = filepath.Join(i.baseURL.Path, path)
	uri, err := i.baseURL.Parse(path)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] invalid base uri")
	}

	req, err := http.NewRequest(method, uri.String(), body)
	if err != nil {
		return req, errors.Wrap(err, "[swiftype] failed to prepare request")
	}
	req.Header.Add("content-type", "application/json")
	req.Header.Add("authorization", "Bearer "+i.key)
	req.Header.Add("accept-encoding", "gzip")

	return req, nil
}

func (d *dialer) Dial(u string) (iface.Index, error) {
	uri, err := url.Parse(u)
	if err != nil {
		return nil, errors.Wrap(err, "[swiftype] Failed to parse uri")
	}

	if uri.User == nil {
		return nil, errors.New("[swiftype] backend need auth")
	}

	key, ok := uri.User.Password()
	if !ok {
		return nil, errors.New("[swiftype] backend need auth")
	}
	uri.User = nil

	client := &http.Client{
		Timeout:   5 * time.Second,
		Transport: d.rt,
	}

	return &index{
		key:     key,
		baseURL: uri,
		client:  client,
	}, nil
}

func init() {
	iface.Register("swiftype", &dialer{rt: ddhttp.WrapRoundTripper(http.DefaultTransport)})
}
