package swiftype

type searchResponse struct {
	Meta struct {
		Alerts   []interface{} `json:"alerts"`
		Warnings []interface{} `json:"warnings"`
		Page     struct {
			Current      int64 `json:"current"`
			TotalPages   int64 `json:"total_pages"`
			TotalResults int64 `json:"total_results"`
			Size         int64 `json:"size"`
		} `json:"page"`
		RequestID string `json:"request_id"`
	} `json:"meta"`
	Results []*result           `json:"results"`
	Facets  map[string][]*facet `json:"facets"`
}

type result struct {
	ID            *resultField `json:"id"`
	ProductID     *resultField `json:"product_id"`
	VariantID     *resultField `json:"variant_id"`
	InStock       *resultField `json:"in_stock"`
	RawPopularity *resultField `json:"raw_popularity"`
	Meta          *meta        `json:"_meta"`
	GroupKey      interface{}  `json:"_group_key"`
}

type resultField struct {
	Raw     interface{} `json:"raw"`
	Snippet interface{} `json:"snippet"`
}

type meta struct {
	Score float64 `json:"score"`
}

type facet struct {
	Type string `json:"type"`
	Name string `json:"name"`
	Data []struct {
		Value interface{} `json:"value"`
		Count int         `json:"count"`
	} `json:"data"`
}

type searchSuggestionResponse struct {
	Results struct {
		Documents []*document `json:"documents"`
	} `json:"results"`
	Meta struct {
		RequestID string `json:"request_id"`
	} `json:"meta"`
}

type document struct {
	Suggestion interface{} `json:"suggestion"`
}

type documentResponse struct {
	ID   string   `json:"id"`
	Errs []string `json:"errors"`
}
