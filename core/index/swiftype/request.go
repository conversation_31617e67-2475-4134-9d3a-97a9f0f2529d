package swiftype

import (
	"fmt"
	"time"

	"happyfresh.io/lib/str"
)

type Q struct {
	QueryString  string                              `json:"query"`
	Filters      Filter                              `json:"filters,omitempty"`
	Grouping     Grouping                            `json:"group,omitempty"`
	Pagination   Pagination                          `json:"page,omitempty"`
	Sorting      []Sorting                           `json:"sort,omitempty"`
	SearchFields map[string]map[string]int           `json:"search_fields,omitempty"`
	ResultFields map[string]map[string]interface{}   `json:"result_fields,omitempty"`
	Boosts       map[string][]map[string]interface{} `json:"boosts,omitempty"`
	Facets       map[string][]*Facet                 `json:"facets,omitempty"`
	Analytics    map[string][]string                 `json:"analytics,omitempty"`

	locale string
}

type Grouping map[string]interface{}

type Filter map[string]interface{}

type Pagination map[string]int

type Sorting map[string]string

type Facet struct {
	Type string            `json:"type"`
	Name string            `json:"name"`
	Size int               `json:"size"`
	Sort map[string]string `json:"sort,omitempty"`
}

type QS struct {
	QueryString string `json:"query"`
	Size        int64  `json:"size,omitempty"`
	Types       Types  `json:"types"`
}

type Types struct {
	Documents Documents `json:"documents"`
}

type Documents struct {
	Fields []string `json:"fields"`
}

func (s *Q) SetQueryString(qs string) {
	s.QueryString = qs
}

func (s *Q) SetFuzzyQueries(fqs []string) {
}

func (s *Q) SetGrouping(f string) {
	s.Grouping = Grouping{
		"field": f,
		"size":  1,
	}
}

func (s *Q) SetLocale(l string) {
	s.locale = l
	s.AddAnalyticTags("locale", l)
}

func (s *Q) SetChannel(channel string) {
}

func (s *Q) AddTermFilter(field string, value ...interface{}) {
	if len(value) <= 0 {
		return
	}

	s.appendFilter(field, value)
}

func (s *Q) AddTermFilterInt64(field string, value ...int64) {
	if len(value) <= 0 {
		return
	}

	s.appendFilter(field, value)
}

func (s *Q) AddTermFilterString(field string, value ...string) {
	if len(value) <= 0 {
		return
	}

	s.appendFilter(field, value)
}

func (s *Q) AddNoneTermFilterString(field string, value ...string) {
	if len(value) <= 0 {
		return
	}

	s.appendNoneFilter(field, value)
}

func (s *Q) AddGeoFilter(field string, lat, long, distance float64, unit string) {
	center := fmt.Sprintf("%.7f,%.7f", lat, long)
	s.appendFilter(field, map[string]interface{}{
		"center":   center,
		"distance": distance,
		"unit":     unit,
	})
}

func (s *Q) AddRangeFilter(field string, start, end float64) {
	s.appendFilter(field, map[string]float64{
		"from": start,
		"to":   end,
	})
}

func (s *Q) AddDateRangeFilter(field string, start, end time.Time) {
	from := start.Format(time.RFC3339)
	to := end.Format(time.RFC3339)
	s.appendFilter(field, map[string]string{
		"from": from,
		"to":   to,
	})
}

func (s *Q) AddNoneTermFilterInt64(field string, value ...int64) {
	if len(value) <= 0 {
		return
	}

	s.appendNoneFilter(field, value)
}

func (s *Q) AddBoosts(field string, value ...interface{}) {
	if s.Boosts == nil {
		s.Boosts = map[string][]map[string]interface{}{}
	}

	for _, v := range value {
		b := str.MaybeString(v)
		if b.Int() <= 0 {
			continue
		}

		s.Boosts[field] = []map[string]interface{}{
			{
				"type":      "value",
				"value":     b.String(),
				"operation": "multiply",
				"factor":    1.5,
			},
		}
	}
}

func (s *Q) AddFunctionalBoosts(fields ...string) {
	if s.Boosts == nil {
		s.Boosts = map[string][]map[string]interface{}{}
	}

	for _, f := range fields {
		if f == "" {
			continue
		}

		s.Boosts[f] = []map[string]interface{}{
			{
				"type":      "functional",
				"function":  "logarithmic",
				"operation": "multiply",
				"factor":    2,
			},
		}
	}
}

func (s *Q) AddFunctionalBoostsScore(fieldsScore map[string]float64) {
	if s.Boosts == nil {
		s.Boosts = map[string][]map[string]interface{}{}
	}

	for field, score := range fieldsScore {
		if field == "" {
			continue
		}

		s.Boosts[field] = []map[string]interface{}{
			{
				"type":      "functional",
				"function":  "logarithmic",
				"operation": "multiply",
				"factor":    score,
			},
		}
	}
}

func (s *Q) AddFacet(field string, params ...interface{}) {
	if s.Facets == nil {
		s.Facets = make(map[string][]*Facet)
	}

	s.Facets[field] = []*Facet{
		{
			Type: "value",
			Name: "filter",
			Size: 250, //max size for facets @appsearch
			Sort: map[string]string{
				"value": "asc",
			},
		},
	}
}

func (s *Q) SetPagination(size, current int) {
	s.Pagination = map[string]int{
		"size":    size,
		"current": current,
	}
}

func (s *Q) SortBy(field, direction string) {
	s.Sorting = append(s.Sorting, map[string]string{
		field: direction,
	})
}

func (s *Q) AddAnalyticTags(field string, value interface{}) {
	if s.Analytics == nil {
		s.Analytics = map[string][]string{"tags": {}}
	}

	s.Analytics["tags"] = append(s.Analytics["tags"], fmt.Sprintf("%s_%v", field, value))
}

func (s *Q) SetResultFields(fields ...string) {
	s.ResultFields = map[string]map[string]interface{}{}

	for _, f := range fields {
		s.ResultFields[f] = map[string]interface{}{
			"raw": map[string]interface{}{},
		}
	}
}

func (s *Q) AddConversionBoosts(field string, value interface{}) {
	val := (value).(map[string]float64)

	if s.Boosts == nil {
		s.Boosts = make(map[string][]map[string]interface{})
	}

	for k, v := range val {
		if len(k) == 0 || k == "0" {
			continue
		}

		s.Boosts[field] = append(s.Boosts[field], []map[string]interface{}{
			{
				"type":      "value",
				"value":     k,
				"operation": "multiply",
				"factor":    v,
			},
		}...)
	}
}

func (s *Q) AddTrendingBoost(weight float64, activePeriod int64) {
}

func (s *Q) AddResultPinning(pinnedVariantIDs []int64) {
	return
}

func (s *Q) AddSearchIntentionAttributes(taxonIDs, productTypeIDs, brandIDs []int64, score float64) {}

func (s *Q) appendFilter(field string, value interface{}) {
	if s.Filters == nil {
		s.Filters = make(map[string]interface{})
	}

	if len(s.Filters) == 0 {
		s.Filters[field] = value
		return
	}

	_, allOk := s.Filters["all"]
	_, noneOk := s.Filters["none"]
	if len(s.Filters) == 1 && !allOk && !noneOk {
		all := []map[string]interface{}{}

		for k, v := range s.Filters {
			all = append(all, map[string]interface{}{k: v})
		}

		s.Filters = map[string]interface{}{"all": all}
	}

	if _, ok := s.Filters["all"]; !ok {
		all := []map[string]interface{}{}
		all = append(all, map[string]interface{}{field: value})
		s.Filters["all"] = all

		return
	}

	all := s.Filters["all"]
	if v, ok := all.([]map[string]interface{}); ok {
		v = append(v, map[string]interface{}{field: value})
		s.Filters["all"] = v
	}
}

func (s *Q) appendNoneFilter(field string, value interface{}) {
	if v, ok := value.([]interface{}); ok && len(v) == 0 {
		return
	}

	if s.Filters == nil {
		s.Filters = make(map[string]interface{})
	}

	_, allOk := s.Filters["all"]
	_, noneOk := s.Filters["none"]
	if len(s.Filters) == 1 && !allOk && !noneOk {
		all := []map[string]interface{}{}

		for k, v := range s.Filters {
			all = append(all, map[string]interface{}{k: v})
		}

		s.Filters = map[string]interface{}{"all": all}
	}

	if _, ok := s.Filters["none"]; !ok {
		none := []map[string]interface{}{}
		none = append(none, map[string]interface{}{field: value})

		s.Filters["none"] = none

		return
	}

	none := s.Filters["none"]
	if v, ok := none.([]map[string]interface{}); ok {
		v = append(v, map[string]interface{}{field: value})
		s.Filters["none"] = v
	}
}

func (s *Q) SetCountry(country string) {
	return
}
