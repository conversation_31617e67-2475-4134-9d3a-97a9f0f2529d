package pq

import (
	"strconv"
	"strings"
	"time"
)

type Q struct {
	locale           string
	variantIDs       []int
	filteredTaxonIDs []int64
	storeID          int
	stockLocationID  int
	page             int
	perPage          int
	sort             string
	direction        string
	nonSellableItems []string
	pinnedVariantIDs []int64
}

func (s *Q) SetQueryString(q string) {
	return
}

func (s *Q) SetFuzzyQueries(fqs []string) {
}

func (s *Q) SetGrouping(field string) {
	return
}

func (s *Q) SetLocale(locale string) {
	s.locale = locale
}

func (s *Q) SetChannel(channel string) {
}

func (s *Q) AddGeoFilter(field string, lat, long, distance float64, unit string) {
	return
}

func (s *Q) AddTermFilter(field string, value ...interface{}) {
	switch field {
	case "variant_id":
		for _, v := range value {
			s.variantIDs = append(s.variantIDs, v.(int))
		}
	case "store_id":
		if len(value) <= 0 {
			s.storeID = -1
		}

		s.storeID = value[0].(int)
	case "stock_location_id":
		if len(value) <= 0 {
			s.stockLocationID = -1
		}

		s.stockLocationID = value[0].(int)
	}
}

func (s *Q) AddTermFilterInt64(field string, value ...int64) {
	switch field {
	case "variant_id":
		for _, v := range value {
			s.variantIDs = append(s.variantIDs, int(v))
		}
	case "store_id":
		if len(value) <= 0 {
			s.storeID = -1
		}

		s.storeID = int(value[0])
	case "stock_location_id":
		if len(value) <= 0 {
			s.stockLocationID = -1
		}

		s.stockLocationID = int(value[0])
	}
}

func (s *Q) AddTermFilterString(field string, value ...string) {
	switch field {
	case "variant_id":
		for _, v := range value {
			vInt, err := strconv.Atoi(v)
			if err != nil {
				vInt = -1
			}

			s.variantIDs = append(s.variantIDs, vInt)
		}
	case "store_id":
		vInt, err := strconv.Atoi(value[0])
		if err != nil {
			vInt = -1
		}

		s.storeID = vInt
	case "stock_location_id":
		vInt, err := strconv.Atoi(value[0])
		if err != nil {
			vInt = -1
		}

		s.stockLocationID = vInt
	}
}

func (s *Q) AddRangeFilter(field string, start, end float64) {
	return
}

func (s *Q) AddDateRangeFilter(field string, start, end time.Time) {
	return
}

func (s *Q) AddNoneTermFilterString(field string, value ...string) {
	if field == "sellable_items" {
		s.nonSellableItems = value
	}

	return
}

func (s *Q) AddNoneTermFilterInt64(field string, value ...int64) {
	if field == "taxon_ids" {
		s.filteredTaxonIDs = append(s.filteredTaxonIDs, value...)
	}

	return
}

func (s *Q) AddBoosts(field string, value ...interface{}) {
	return
}

func (s *Q) AddFunctionalBoosts(fields ...string) {
	return
}

func (s *Q) AddFunctionalBoostsScore(fieldsScore map[string]float64) {
}

func (s *Q) AddConversionBoosts(field string, value interface{}) {
}

func (s *Q) AddTrendingBoost(weight float64, activePeriod int64) {
}

func (s *Q) AddFacet(field string, params ...interface{}) {
}

func (s *Q) AddResultPinning(pinnedVariantIDs []int64) {
	if len(pinnedVariantIDs) == 0 {
		return
	}

	if s.pinnedVariantIDs == nil {
		s.pinnedVariantIDs = []int64{}
	}

	s.pinnedVariantIDs = append(s.pinnedVariantIDs, pinnedVariantIDs...)
}

func (s *Q) SetPagination(size, current int) {
	s.page = current
	s.perPage = size
}

func (s *Q) SortBy(field, direction string) {
	if field == "_score" {
		s.sort = "popularity"
		direction = "desc"
	} else if strings.HasPrefix(field, "price") {
		s.sort = "price"
	} else if strings.HasPrefix(field, "unit_price") {
		s.sort = "unit-price"
	} else {
		s.sort = field
	}

	s.direction = direction
}

func (s *Q) SetResultFields(fields ...string) {
	return
}

func (s *Q) AddAnalyticTags(field string, value interface{}) {
	return
}

func (s *Q) SetCountry(country string) {
	return
}

func (s *Q) AddSearchIntentionAttributes(taxonIDs, productTypeIDs, brandIDs []int64, score float64) {}
