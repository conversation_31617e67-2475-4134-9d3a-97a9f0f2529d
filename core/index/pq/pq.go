package pq

import (
	"context"
	"math"

	"happyfresh.io/search/core/datastore"
	iface "happyfresh.io/search/core/index"
)

type index struct {
}

func (i *index) Store(ctx context.Context, data ...interface{}) error {
	return nil
}

func (i *index) Update(ctx context.Context, data ...interface{}) error {
	return nil
}

func (i *index) Search(ctx context.Context, options ...iface.SearchOption) (*iface.SearchResponse, error) {
	q := &Q{}
	for _, opt := range options {
		opt(q)
	}

	if len(q.variantIDs) <= 0 {
		return &iface.SearchResponse{
			Meta: &iface.Meta{
				Page:       int64(q.page),
				PageSize:   int64(q.perPage),
				TotalPages: 0,
				Count:      0,
				TotalCount: 0,
			},
			StockItems: make([]*iface.StockItem, 0),
			Filters:    make(map[string][]interface{}),
		}, nil
	}

	if len(q.locale) <= 0 {
		q.locale = "EN"
	}

	stockItemIDs, err := datastore.StockItemIDsByVariantStore(ctx, q.variantIDs, q.filteredTaxonIDs, q.storeID, q.sort, q.direction, q.nonSellableItems, q.pinnedVariantIDs)
	if err != nil {
		return nil, err
	}

	from := q.perPage * (q.page - 1)
	to := from + (q.perPage)
	if len(stockItemIDs) <= to {
		to = len(stockItemIDs)
	}

	stockItems := make([]*datastore.StockItem, 0)
	if from < len(stockItemIDs) {
		stockItems, err = datastore.StockItemsByIDs(ctx, q.locale, stockItemIDs[from:to])
		if err != nil {
			return nil, err
		}
	}

	totalCount := len(stockItemIDs)
	searchResponse := &iface.SearchResponse{
		Meta: &iface.Meta{
			Page:       int64(q.page),
			PageSize:   int64(q.perPage),
			TotalPages: int64(math.Ceil(float64(totalCount) / float64(q.perPage))),
			Count:      int64(len(stockItems)),
			TotalCount: int64(totalCount),
		},
		StockItems: make([]*iface.StockItem, len(stockItems)),
		Filters:    make(map[string][]interface{}),
	}

	for i, stockItem := range stockItems {
		searchResponse.StockItems[i] = &iface.StockItem{
			ID:        stockItem.Id,
			ProductID: stockItem.ProductId,
			VariantID: stockItem.VariantId,
			InStock:   stockItem.InStock,
		}
	}

	return searchResponse, nil
}

func (i *index) SuggestSearch(ctx context.Context, options ...iface.SearchOption) ([]string, error) {
	return []string{}, nil
}

func (i *index) Click(ctx context.Context, data interface{}) error {
	return nil
}

func New() iface.Client {
	return &client{
		index: &index{},
	}
}

type client struct {
	index *index
}

func (c *client) Index(key string) (iface.Index, error) {
	return c.index, nil
}

func (c *client) Indices() map[string]iface.Index {
	return map[string]iface.Index{
		"ID": c.index,
		"MY": c.index,
		"TH": c.index,
	}
}
