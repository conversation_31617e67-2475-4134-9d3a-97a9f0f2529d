package suggester

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"

	"github.com/pkg/errors"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"

	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/index/es"
)

func New(config elasticsearch.Config) (*esClient, error) {
	es, err := elasticsearch.NewClient(config)
	if err != nil {
		return nil, err
	}

	return &esClient{
		client: es,
	}, nil
}

func NewWithOriginal(es *elasticsearch.Client) (*esClient, error) {
	return &esClient{
		client: es,
	}, nil
}

type esClient struct {
	client         *elasticsearch.Client
	termIndexName  string
	taxonIndexName string
	brandIndexName string
}

func (c *esClient) Suggest(ctx context.Context, options ...index.SuggestOption) (*index.SuggestResponse, error) {
	suggestRequest := &Q{}
	for _, apply := range options {
		apply(suggestRequest)
	}

	var keywords []string
	var keywordInTaxons []*index.KeywordInTaxon
	var taxons []*index.IDNameSuggestion
	var brands []*index.IDNameSuggestion
	var err error

	keywords, keywordInTaxons, taxons, brands, err = c.suggest(ctx, suggestRequest)
	if err != nil {
		log.Error(err)
	}

	return &index.SuggestResponse{
		Keywords:        keywords,
		KeywordInTaxons: keywordInTaxons,
		Taxons:          taxons,
		Brands:          brands,
	}, nil
}

func (c *esClient) suggest(ctx context.Context, suggestRequest *Q) ([]string, []*index.KeywordInTaxon, []*index.IDNameSuggestion, []*index.IDNameSuggestion, error) {
	taxonField := "taxon_name"
	brandField := "brand_name"

	locale := strings.ToLower(suggestRequest.Locale)
	if locale != "en" {
		taxonField += "_locale"
		brandField += "_locale"
	}

	buf := &bytes.Buffer{}
	if _, err := buf.WriteString(
		fmt.Sprintf(
			MultiSuggestionQuery,
			suggestRequest.Query, suggestRequest.Fuzziness, strings.ToLower(suggestRequest.Locale), suggestRequest.StockLocationIDsString, suggestRequest.SupplierIDsString, suggestRequest.StoreCategoryIDsString, suggestRequest.Size,
			c.taxonIndexName,
			taxonField, suggestRequest.Query, suggestRequest.Fuzziness, suggestRequest.StockLocationIDsString, suggestRequest.SupplierIDsString, suggestRequest.StoreCategoryIDsString,
			c.brandIndexName,
			brandField, suggestRequest.Query, suggestRequest.Fuzziness, suggestRequest.StockLocationIDsString, suggestRequest.SupplierIDsString, suggestRequest.StoreCategoryIDsString,
		),
	); err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "[elasticsearch] error encoding request")
	}

	response, err := c.client.Msearch(
		buf,
		c.client.Msearch.WithContext(ctx),
		c.client.Msearch.WithIndex(c.termIndexName),
		c.client.Msearch.WithHeader(map[string]string{"Accept-Encoding": "gzip"}),
	)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "[elasticsearch] error on search")
	}

	b, err := c.readAll(response)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "[elasticsearch] error decoding response")
	}

	if response.StatusCode >= http.StatusBadRequest || response.IsError() {
		errB := &es.ESError{}
		err = json.Unmarshal(b, errB)
		if err != nil {
			return nil, nil, nil, nil, errors.Wrap(err, "[elasticsearch] error decoding response")
		}

		return nil, nil, nil, nil, errors.Errorf("[elasticsearch] error: status: %v; message: %v", response.Status(), errB.Error.Reason)
	}

	emr := &esMultiResponse{}
	err = json.Unmarshal(b, emr)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "[elasticsearch] error decoding response")
	}

	// Populate terms
	erTerms := emr.Responses[0]
	keywords := make([]string, len(erTerms.Aggregations.Terms.Buckets))
	keywordInTaxonsMap := map[int]*index.KeywordInTaxon{}
	for i, bucket := range erTerms.Aggregations.Terms.Buckets {
		if i == 0 {
			topDoc := bucket.TopHit.Hits.Hits[0]
			for j, taxon := range topDoc.Source.Taxons {
				if j >= 3 {
					break
				}

				taxonName := taxon.NameEN
				switch locale {
				case "id":
					taxonName = taxon.NameID
				case "my":
					taxonName = taxon.NameMY
				case "th":
					taxonName = taxon.NameTH
				}

				if locale != "en" && taxonName == "" {
					taxonName = taxon.NameEN
				}

				keywordInTaxonsMap[taxon.ID] = &index.KeywordInTaxon{
					Keyword:   bucket.Key,
					TaxonID:   taxon.ID,
					TaxonName: taxonName,
				}
			}
		}

		keywords[i] = bucket.Key
	}

	keywordInTaxons := []*index.KeywordInTaxon{}
	for _, v := range keywordInTaxonsMap {
		keywordInTaxons = append(keywordInTaxons, v)
	}

	// Populate taxons
	erTaxons := emr.Responses[1]
	txResponse := make([]*index.IDNameSuggestion, len(erTaxons.Aggregations.Taxon.Buckets))
	for i, bucket := range erTaxons.Aggregations.Taxon.Buckets {
		taxonName := bucket.TopHit.Hits.Hits[0].Source.TaxonNameLocale
		if locale == "en" || taxonName == "" {
			taxonName = bucket.TopHit.Hits.Hits[0].Source.TaxonName
		}

		if taxonName == "" {
			return nil, nil, nil, nil, errors.New("[elasticsearch] empty taxon name")
		}

		txResponse[i] = &index.IDNameSuggestion{
			ID:   bucket.Key,
			Name: str.MaybeString(taxonName).String(),
		}
	}

	// Populate brands
	erBrands := emr.Responses[2]
	brResponse := make([]*index.IDNameSuggestion, len(erBrands.Aggregations.Brand.Buckets))
	for i, bucket := range erBrands.Aggregations.Brand.Buckets {
		brandName := bucket.TopHit.Hits.Hits[0].Source.BrandNameLocale
		if locale == "en" || brandName == "" {
			brandName = bucket.TopHit.Hits.Hits[0].Source.BrandName
		}

		if brandName == "" {
			return nil, nil, nil, nil, errors.New("[elasticsearch] empty brand name")
		}

		brResponse[i] = &index.IDNameSuggestion{
			ID:   bucket.Key,
			Name: str.MaybeString(brandName).String(),
		}
	}

	return keywords, keywordInTaxons, txResponse, brResponse, nil
}

func (c *esClient) readAll(r *esapi.Response) (res []byte, err error) {
	body := r.Body
	if r.Header.Get("content-encoding") == "gzip" {
		body, err = gzip.NewReader(r.Body)
		if err != nil {
			return nil, err
		}
	}
	defer body.Close()

	return ioutil.ReadAll(body)
}

func (c *esClient) SetElasticsearchClient(client *elasticsearch.Client) {
	c.client = client
}

func (c *esClient) SetIndexNames(termIndexName, taxonIndexName, brandIndexName string) {
	c.termIndexName = termIndexName
	c.taxonIndexName = taxonIndexName
	c.brandIndexName = brandIndexName
}
