package suggester

import (
	"strconv"
	"strings"
)

type Q struct {
	Query                  string
	StockLocationIDsString string
	SupplierIDsString      string
	StoreCategoryIDsString string
	Locale                 string
	Fuzziness              int64
	Size                   int64
}

func (q *Q) SetQuery(query string) {
	q.Query = query
}

func (q *Q) SetStockLocationIDs(stockLocationIDs ...int64) {
	q.StockLocationIDsString = q.buildSliceString(stockLocationIDs)
}

func (q *Q) SetSupplierIDs(supplierIDs ...int64) {
	q.SupplierIDsString = q.buildSliceString(supplierIDs)
}

func (q *Q) SetSupplierStoreCategories(storeCategoryIDs ...int64) {
	q.StoreCategoryIDsString = q.buildSliceString(storeCategoryIDs)
}

func (q *Q) SetLocale(locale string) {
	q.Locale = locale
}

func (q *Q) SetFuzziness(fuzziness int64) {
	q.Fuzziness = fuzziness
}

func (q *Q) SetSize(size int64) {
	q.Size = size
}

func (q *Q) buildSliceString(a []int64) string {
	sb := &strings.Builder{}
	sb.WriteRune('[')
	for i, id := range a {
		if i > 0 {
			sb.WriteRune(',')
		}
		sb.WriteString(strconv.FormatInt(id, 10))
	}
	sb.WriteRune(']')

	return sb.String()
}
