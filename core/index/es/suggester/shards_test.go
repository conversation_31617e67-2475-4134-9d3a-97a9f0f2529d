package suggester_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"happyfresh.io/search/core/index/es/suggester"
)

func TestRegisterShards(t *testing.T) {
	for _, tc := range []struct {
		name                string
		registerParam       map[string]string
		expectRegisterError bool
		getParamToBeNil     []string
	}{
		{
			"Valid URLs",
			map[string]string{
				"id": "https://localhost:9200/idx1/idx2/idx3",
				"my": "https://localhost:9201/idx1/idx2/idx3",
				"th": "https://localhost:9202/idx1/idx2/idx3",
			},
			false,
			[]string{},
		},
		{
			"Invalid URL",
			map[string]string{
				"id": "https://localhost:9200/idx1/idx2/idx3",
				"my": "invalid url",
				"th": "https://localhost:9202/idx1/idx2/idx3",
			},
			true,
			[]string{"my"},
		},
		{
			"Invalid URL 02",
			map[string]string{
				"id": "https://localhost:9200/idx1/idx2/idx3",
				"my": "https://localhost:9201/idx1/idx2",
				"th": "https://localhost:9202/idx1/idx2/idx3",
			},
			true,
			[]string{"my"},
		},
		{
			"Partially valid URLs",
			map[string]string{
				"id": "https://localhost:9200/idx1/idx2/idx3",
				"my": "https://localhost:9201/idx1",
				"th": "https://localhost:9202/idx1/idx2/idx3",
			},
			false,
			[]string{},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			sg, err := suggester.NewShards()
			if err != nil {
				t.Error(err)
			}

			err = sg.Register(tc.registerParam)
			assert.Equal(t, tc.expectRegisterError, err != nil)

			for _, s := range tc.getParamToBeNil {
				c, err := sg.Get(s)
				assert.Nil(t, c)
				assert.NotNil(t, err)
			}
		})
	}
}
