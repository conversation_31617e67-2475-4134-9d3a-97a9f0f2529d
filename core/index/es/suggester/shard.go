package suggester

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/pkg/errors"
	"happyfresh.io/search/core/index"
)

//go:generate esc -o client.json.go -pkg suggester  ../../../../resources/elasticsearch/search

var (
	MultiSuggestionQuery string
)

func NewShards(opts ...shardOption) (*esShards, error) {
	q, err := FSString(false, "/resources/elasticsearch/search/multi-suggest")
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] error initializing query")
	}

	MultiSuggestionQuery = q

	s := &esShards{
		shards: make(map[string]*esClient),
		m:      &sync.RWMutex{},
	}

	for _, apply := range opts {
		apply(s)
	}

	return s, nil
}

type esShards struct {
	shards map[string]*esClient
	m      *sync.RWMutex
	rt     http.RoundTripper
}

func (c *esShards) Get(key string) (index.Suggester, error) {
	k := strings.ToLower(key)

	c.m.Lock()
	s, ok := c.shards[k]
	c.m.Unlock()
	if !ok {
		return nil, errors.New("[elasticsearch] suggester shard not found")
	}

	return s, nil
}

func (c *esShards) Register(shardsURL map[string]string) error {
	for k, v := range shardsURL {
		u, err := url.Parse(v)
		if err != nil {
			return errors.Wrap(err, "[elasticsearch] invalid url")
		}

		pw, _ := u.User.Password()
		s, err := New(elasticsearch.Config{
			Addresses: []string{fmt.Sprintf("%s://%s", u.Scheme, u.Host)},
			Username:  u.User.Username(),
			Password:  pw,
			Transport: c.rt,
		})
		if err != nil {
			return err
		}

		paths := strings.Split(u.Path[1:], "/")
		if len(paths) == 1 {
			s.termIndexName = paths[0]
		} else if len(paths) == 3 {
			s.termIndexName = paths[0]
			s.taxonIndexName = paths[1]
			s.brandIndexName = paths[2]
		} else {
			return errors.New("[elasticsearch] invalid url")
		}

		if c.shards == nil {
			c.shards = make(map[string]*esClient)
		}

		c.shards[k] = s
	}

	return nil
}

type shardOption func(*esShards)

func WithShardHTTPRoundTripper(rt http.RoundTripper) shardOption {
	return func(s *esShards) {
		s.rt = rt
	}
}
