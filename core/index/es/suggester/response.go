package suggester

type esMultiResponse struct {
	Responses []struct {
		Aggregations struct {
			Terms *struct {
				Buckets []*struct {
					Key    string `json:"key"`
					TopHit *struct {
						Hits *struct {
							Hits []*struct {
								Source *struct {
									Taxons []*struct {
										ID     int    `json:"id"`
										NameEN string `json:"name_en"`
										NameID string `json:"name_id"`
										NameMY string `json:"name_ms"`
										NameTH string `json:"name_th"`
									} `json:"taxons"`
								} `json:"_source"`
							} `json:"hits"`
						} `json:"hits"`
					} `json:"top_doc"`
				} `json:"buckets"`
			} `json:"terms"`
			Taxon *struct {
				Buckets []*struct {
					Key    int64 `json:"key"`
					TopHit *struct {
						Hits *struct {
							Hits []*struct {
								Source *struct {
									TaxonName       string `json:"taxon_name"`
									TaxonNameLocale string `json:"taxon_name_locale"`
								} `json:"_source"`
							} `json:"hits"`
						} `json:"hits"`
					} `json:"top_doc"`
				} `json:"buckets"`
			} `json:"taxons"`
			Brand *struct {
				Buckets []*struct {
					Key    int64 `json:"key"`
					TopHit *struct {
						Hits *struct {
							Hits []*struct {
								Source *struct {
									BrandName       string `json:"brand_name"`
									BrandNameLocale string `json:"brand_name_locale"`
								} `json:"_source"`
							} `json:"hits"`
						} `json:"hits"`
					} `json:"top_doc"`
				} `json:"buckets"`
			} `json:"brands"`
		} `json:"aggregations"`
	} `json:"responses"`
}

type esMResponse struct {
	Responses []interface{} `json:"responses"`
}

type esResponse struct {
	Hits hits `json:"hits"`
}

type hits struct {
	Hits []*innerHits `json:"hits"`
}

type innerHits struct {
	ID     string  `json:"_id"`
	Source *source `json:"_source"`
}

type source struct {
	Query  string   `json:"query"`
	Taxons []*taxon `json:"taxons"`
}

type taxon struct {
	ID     int    `json:"id"`
	NameEN string `json:"name_en"`
	NameID string `json:"name_id"`
	NameMY string `json:"name_ms"`
	NameTH string `json:"name_th"`
}

type taxonResponse struct {
	Aggregations taxonAggregations `json:"aggregations"`
}

type taxonAggregations struct {
	Taxon *bucketAggregation `json:"taxons"`
}

type brandResponse struct {
	Aggregations brandAggregations `json:"aggregations"`
}

type brandAggregations struct {
	Brand *bucketAggregation `json:"brands"`
}

type bucketAggregation struct {
	Buckets []*bucket `json:"buckets"`
}

type bucket struct {
	Key    int64             `json:"key"`
	TopHit *innerAggregation `json:"top_hit_doc"`
}

type innerAggregation struct {
	Hits *aggregationHits `json:"hits"`
}

type aggregationHits struct {
	Hits []*aggregationInnerHits `json:"hits"`
}

type aggregationInnerHits struct {
	ID     string            `json:"_id"`
	Source map[string]string `json:"_source"`
}
