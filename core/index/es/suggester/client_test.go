package suggester_test

import (
	"bytes"
	"compress/gzip"
	"context"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"testing"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	"github.com/glycerine/goconvey/convey"
	"github.com/pkg/errors"

	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/index/es/suggester"
)

type esMock struct {
	multiSearchFunc msearchFunc
}

type msearchFunc func(body io.Reader, r *esapi.MsearchRequest) (*esapi.Response, error)

func (es *esMock) Msearch(body io.Reader, o ...func(*esapi.MsearchRequest)) (*esapi.Response, error) {
	request := &esapi.MsearchRequest{}
	for _, apply := range o {
		apply(request)
	}

	if es.multiSearchFunc == nil {
		return nil, errors.New("[elasticsearch-test] multiSearchFunc is nil")
	}

	return es.multiSearchFunc(body, request)
}

func defaultMock(compressed bool) *esMock {
	multiSearchResult := `{
	  	"responses": [
		  {
			"aggregations" : {
			  "terms" : {
				"buckets" : [
				  {
					"key" : "telur",
					"top_doc" : {
					  "hits" : {
						"hits" : [
						  {
							"_id" : "fmG3OXcBptsveUSxBehr",
							"_source" : {
							  "query" : "telur",
							  "taxons" : [
							  	{
								  "name_ms" : "Telur dan Produk Susu",
								  "name_th" : "",
								  "id" : 63,
								  "name_id" : "Telur dan Produk Susu",
								  "name_en" : "Eggs and Dairy"
							  	}
							  ]
							}
						  }
						]
					  }
					}
				  }
				]
		      }
		    }
		  },
		  {
			"aggregations" : {
			  "taxons" : {
				"buckets" : [
				  {
					"key" : 8,
					"top_doc" : {
					  "hits" : {
						"hits" : [
						  {
							"_id" : "fmG3OXcBptsveUSxBehr",
							"_source" : {
							  "taxon_name": "Meat Counter",
							  "taxon_name_locale" : "Konter Daging"
							}
						  }
						]
					  }
					}
				  }
				]
		      }
		    }
		  },
		  {
			"aggregations" : {
			  "brands" : {
				"buckets" : [
				  {
					"key" : 217,
					"top_doc" : {
					  "hits" : {
						"hits" : [
						  {
							"_id" : "llVAOXcBptsveUSxn8b-",
							"_source" : {
							  "brand_name": "Oreo",
							  "brand_name_locale" : "Oreo"
							}
						  }
						]
					  }
					}
				  }
				]
			  }
		    }
		  }
		]
	  }
	`

	toESResponse := func(s string, compressed bool) (*esapi.Response, error) {
		header := http.Header{}
		header.Set("Content-Type", "application/json")

		var body io.Reader = strings.NewReader(s)

		if compressed {
			buf := &bytes.Buffer{}
			gz := gzip.NewWriter(buf)
			if _, err := gz.Write([]byte(s)); err != nil {
				return nil, err
			}

			if err := gz.Close(); err != nil {
				return nil, err
			}

			header.Set("content-encoding", "gzip")
			body = buf
		}

		return &esapi.Response{
			StatusCode: 200,
			Header:     header,
			Body:       ioutil.NopCloser(body),
		}, nil
	}

	mock := &esMock{
		multiSearchFunc: func(body io.Reader, r *esapi.MsearchRequest) (*esapi.Response, error) {
			return toESResponse(multiSearchResult, compressed)
		},
	}

	return mock
}

func TestSearch(t *testing.T) {
	q, err := suggester.FSString(false, "/resources/elasticsearch/search/multi-suggest")
	if err != nil {
		t.Error(err)
	}

	suggester.MultiSuggestionQuery = q

	tt := []struct {
		name                 string
		suggestRequestOpts   []index.SuggestOption
		multiSearchFunc      msearchFunc
		expectedResult       *index.SuggestResponse
		expectedIsError      bool
		compressed           bool
		instantiateSuggester func(esapi.Msearch) index.Suggester
	}{
		{
			"Returns all suggestion EN",
			[]index.SuggestOption{
				index.WithSuggesterLocale("EN"),
			},
			nil,
			&index.SuggestResponse{
				Keywords:        []string{"telur"},
				KeywordInTaxons: []*index.KeywordInTaxon{{Keyword: "telur", TaxonID: 63, TaxonName: "Eggs and Dairy"}},
				Taxons:          []*index.IDNameSuggestion{{ID: 8, Name: "Meat Counter"}},
				Brands:          []*index.IDNameSuggestion{{ID: 217, Name: "Oreo"}},
			},
			false,
			false,
			func(e esapi.Msearch) index.Suggester {
				i, _ := suggester.NewWithOriginal(&elasticsearch.Client{
					API: &esapi.API{
						Msearch: e,
					},
				})
				i.SetIndexNames("term", "taxon", "brand")

				return i
			},
		},
		{
			"Returns all suggestion locale",
			[]index.SuggestOption{
				index.WithSuggesterLocale("id"),
			},
			nil,
			&index.SuggestResponse{
				Keywords:        []string{"telur"},
				KeywordInTaxons: []*index.KeywordInTaxon{{Keyword: "telur", TaxonID: 63, TaxonName: "Telur dan Produk Susu"}},
				Taxons:          []*index.IDNameSuggestion{{ID: 8, Name: "Konter Daging"}},
				Brands:          []*index.IDNameSuggestion{{ID: 217, Name: "Oreo"}},
			},
			false,
			true,
			func(e esapi.Msearch) index.Suggester {
				i, _ := suggester.New(elasticsearch.Config{})
				i.SetIndexNames("term", "taxon", "brand")
				i.SetElasticsearchClient(&elasticsearch.Client{
					API: &esapi.API{
						Msearch: e,
					},
				})

				return i
			},
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			mock := defaultMock(tc.compressed)
			if tc.multiSearchFunc != nil {
				mock.multiSearchFunc = tc.multiSearchFunc
			}

			i := tc.instantiateSuggester(mock.Msearch)

			ctx := context.Background()
			suggestOpts := append(tc.suggestRequestOpts, index.WithSuggesterStoreCategory([]int64{0}...), index.WithSuggesterSize(3))
			response, err := i.Suggest(ctx, suggestOpts...)
			if err != nil {
				assert := convey.ShouldResemble(err != nil, tc.expectedIsError)
				if assert != "" {
					t.Error(err)
				}
			}

			if tc.expectedIsError {
				return
			}

			assert := convey.ShouldResemble(response, tc.expectedResult)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}
