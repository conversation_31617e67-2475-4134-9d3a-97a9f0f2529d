package search

import (
	"reflect"
	"testing"
	"time"
)

func TestSetQueryString(t *testing.T) {
	testCases := []struct {
		name          string
		query         string
		startingQuery []string
		expectedQuery []string
	}{
		{
			name:          "With query string",
			query:         "jeruk",
			expectedQuery: []string{"jeruk"},
		},
		{
			name:          "Without query string",
			query:         "jeruk",
			expectedQuery: []string{"jeruk"},
		},
		{
			name:          "With existing query",
			query:         "jeruk",
			startingQuery: []string{"jruk"},
			expectedQuery: []string{"jruk", "jeruk"},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			if len(testCase.startingQuery) > 0 {
				q.Queries = testCase.startingQuery
			}

			q.SetQueryString(testCase.query)

			if !reflect.DeepEqual(q.Queries, testCase.expectedQuery) {
				t.<PERSON>("\nexpectation: %v\nreality: %v", testCase.expectedQuery, q.Queries)
			}
		})
	}
}

func TestSetGrouping(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		q := Q{}
		q.SetGrouping("")

		if !reflect.DeepEqual(q, Q{}) {
			t.Errorf("\nexpectation: %v\nreality: %v", "SetGrouping has no implementation", q)
		}
	})
}

func TestSetLocale(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		q := Q{}
		q.SetLocale("")

		if !reflect.DeepEqual(q, Q{}) {
			t.Errorf("\nexpectation: %v\nreality: %v", "SetGrouping has no implementation", q)
		}
	})
}

func TestSetChannel(t *testing.T) {
	testCases := []struct {
		name            string
		channel         string
		expectedChannel string
	}{
		{
			name:            "With channel android",
			channel:         "android",
			expectedChannel: "android",
		},
		{
			name:            "With channel ios",
			channel:         "ios",
			expectedChannel: "ios",
		},
		{
			name:            "With invalid channel",
			channel:         "iOS",
			expectedChannel: "",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			q.SetChannel(testCase.channel)

			if !reflect.DeepEqual(q.Channel, testCase.expectedChannel) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedChannel, q.Channel)
			}
		})
	}
}

func TestAddTermFilter(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		q := Q{}
		q.AddTermFilter("")

		if !reflect.DeepEqual(q, Q{}) {
			t.Errorf("\nexpectation: %v\nreality: %v", "SetGrouping has no implementation", q)
		}
	})
}

func TestAddTermFilterInt64(t *testing.T) {
	testCases := []struct {
		name           string
		field          string
		value          []int64
		startingFilter map[string][]interface{}
		expectedFilter map[string][]interface{}
	}{
		{
			name:  "With filter",
			field: "store_id",
			value: []int64{1101},
			expectedFilter: map[string][]interface{}{
				"must": {
					map[string]interface{}{
						"terms": map[string][]int64{
							"store_id.float": {1101},
						},
					},
				},
			},
		},
		{
			name:  "With starting filter",
			field: "store_id",
			value: []int64{1101},
			startingFilter: map[string][]interface{}{
				"must": {
					map[string]interface{}{
						"terms": map[string][]int64{
							"brand_id.float": {5},
						},
					},
				},
			},
			expectedFilter: map[string][]interface{}{
				"must": {
					map[string]interface{}{
						"terms": map[string][]int64{
							"brand_id.float": {5},
						},
					},
					map[string]interface{}{
						"terms": map[string][]int64{
							"store_id.float": {1101},
						},
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			if len(testCase.startingFilter) > 0 {
				q.Filter = testCase.startingFilter
			}

			q.AddTermFilterInt64(testCase.field, testCase.value...)

			if !reflect.DeepEqual(q.Filter, testCase.expectedFilter) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedFilter, q.Filter)
			}
		})
	}
}

func TestAddTermFilterString(t *testing.T) {
	testCases := []struct {
		name           string
		field          string
		value          []string
		startingFilter map[string][]interface{}
		expectedFilter map[string][]interface{}
	}{
		{
			name:  "With filter",
			field: "product_type_ids",
			value: []string{"3268"},
			expectedFilter: map[string][]interface{}{
				"must": {
					map[string]interface{}{
						"terms": map[string][]string{
							"product_type_ids.enum": {"3268"},
						},
					},
				},
			},
		},
		{
			name:  "With starting filter",
			field: "product_type_ids",
			value: []string{"3268"},
			startingFilter: map[string][]interface{}{
				"must": {
					map[string]interface{}{
						"terms": map[string][]string{
							"product_type_ids.enum": {"3267"},
						},
					},
				},
			},
			expectedFilter: map[string][]interface{}{
				"must": {
					map[string]interface{}{
						"terms": map[string][]string{
							"product_type_ids.enum": {"3267"},
						},
					},
					map[string]interface{}{
						"terms": map[string][]string{
							"product_type_ids.enum": {"3268"},
						},
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			if len(testCase.startingFilter) > 0 {
				q.Filter = testCase.startingFilter
			}

			q.AddTermFilterString(testCase.field, testCase.value...)

			if !reflect.DeepEqual(q.Filter, testCase.expectedFilter) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedFilter, q.Filter)
			}
		})
	}
}

func TestAddGeoFilter(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		q := Q{}
		q.AddGeoFilter("", 0, 0, 0, "")

		if !reflect.DeepEqual(q, Q{}) {
			t.Errorf("\nexpectation: %v\nreality: %v", "AddGeoFilter has no implementation", q)
		}
	})
}

func TestAddRangeFilter(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		q := Q{}
		q.AddRangeFilter("", 0, 0)

		if !reflect.DeepEqual(q, Q{}) {
			t.Errorf("\nexpectation: %v\nreality: %v", "AddRangeFilter has no implementation", q)
		}
	})
}

func TestAddDateRangeFilter(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		q := Q{}
		q.AddDateRangeFilter("", time.Now(), time.Now())

		if !reflect.DeepEqual(q, Q{}) {
			t.Errorf("\nexpectation: %v\nreality: %v", "SetGrouping has no implementation", q)
		}
	})
}

func TestAddNoneTermFilterString(t *testing.T) {
	testCases := []struct {
		name           string
		field          string
		value          []string
		startingFilter map[string][]interface{}
		expectedFilter map[string][]interface{}
	}{
		{
			name:  "With filter",
			field: "product_type_ids",
			value: []string{"3268"},
			expectedFilter: map[string][]interface{}{
				"must_not": {
					map[string]interface{}{
						"terms": map[string][]string{
							"product_type_ids": {"3268"},
						},
					},
				},
			},
		},
		{
			name:  "With starting filter",
			field: "product_type_ids",
			value: []string{"3268"},
			startingFilter: map[string][]interface{}{
				"must_not": {
					map[string]interface{}{
						"terms": map[string][]string{
							"product_type_ids": {"3267"},
						},
					},
				},
			},
			expectedFilter: map[string][]interface{}{
				"must_not": {
					map[string]interface{}{
						"terms": map[string][]string{
							"product_type_ids": {"3267"},
						},
					},
					map[string]interface{}{
						"terms": map[string][]string{
							"product_type_ids": {"3268"},
						},
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			if len(testCase.startingFilter) > 0 {
				q.Filter = testCase.startingFilter
			}

			q.AddNoneTermFilterString(testCase.field, testCase.value...)

			if !reflect.DeepEqual(q.Filter, testCase.expectedFilter) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedFilter, q.Filter)
			}
		})
	}
}

func TestAddNoneTermFilterInt64(t *testing.T) {
	testCases := []struct {
		name           string
		field          string
		value          []int64
		startingFilter map[string][]interface{}
		expectedFilter map[string][]interface{}
	}{
		{
			name:  "With filter",
			field: "store_id",
			value: []int64{1101},
			expectedFilter: map[string][]interface{}{
				"must_not": {
					map[string]interface{}{
						"terms": map[string][]int64{
							"store_id.float": {1101},
						},
					},
				},
			},
		},
		{
			name:  "With starting filter",
			field: "store_id",
			value: []int64{1101},
			startingFilter: map[string][]interface{}{
				"must_not": {
					map[string]interface{}{
						"terms": map[string][]int64{
							"brand_id.float": {5},
						},
					},
				},
			},
			expectedFilter: map[string][]interface{}{
				"must_not": {
					map[string]interface{}{
						"terms": map[string][]int64{
							"brand_id.float": {5},
						},
					},
					map[string]interface{}{
						"terms": map[string][]int64{
							"store_id.float": {1101},
						},
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			if len(testCase.startingFilter) > 0 {
				q.Filter = testCase.startingFilter
			}

			q.AddNoneTermFilterInt64(testCase.field, testCase.value...)

			if !reflect.DeepEqual(q.Filter, testCase.expectedFilter) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedFilter, q.Filter)
			}
		})
	}
}

func TestAddFacet(t *testing.T) {
	testCases := []struct {
		name         string
		facetName    string
		facetParams  []interface{}
		expectedAggs map[string]interface{}
	}{
		{
			name:         "Apply no facet name",
			facetName:    "",
			expectedAggs: make(map[string]interface{}),
		},
		{
			name:      "Apply brand facet",
			facetName: "brands",
			expectedAggs: map[string]interface{}{
				"brands": map[string]interface{}{
					"terms": map[string]interface{}{
						"field": "brand_id.float",
						"size":  int64(100),
					},
				},
			},
		},
		{
			name:      "Apply taxon facet",
			facetName: "taxons",
			expectedAggs: map[string]interface{}{
				"taxons": map[string]interface{}{
					"terms": map[string]interface{}{
						"field": "taxon_ids.float",
						"size":  int64(100),
					},
				},
			},
		},
		{
			name:      "Apply stock location id facet",
			facetName: "stock_location_ids",
			expectedAggs: map[string]interface{}{
				"stock_location_ids": map[string]interface{}{
					"terms": map[string]interface{}{
						"field": "stock_location_id.float",
						"size":  int64(10),
					},
					"aggs": map[string]interface{}{
						"top_hit_doc": map[string]interface{}{
							"top_hits": map[string]interface{}{
								"size":    int64(5),
								"_source": false,
								"fields":  []string{"product_id.float", "variant_id.float", "in_stock", "raw_popularity.float", "stock_location_id.float"},
								"sort": []map[string]interface{}{
									{
										"id": "asc",
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name:        "Apply stock location id facet with custom sizes",
			facetName:   "stock_location_ids",
			facetParams: []interface{}{3, 3},
			expectedAggs: map[string]interface{}{
				"stock_location_ids": map[string]interface{}{
					"terms": map[string]interface{}{
						"field": "stock_location_id.float",
						"size":  int64(3),
					},
					"aggs": map[string]interface{}{
						"top_hit_doc": map[string]interface{}{
							"top_hits": map[string]interface{}{
								"size":    int64(3),
								"_source": false,
								"fields":  []string{"product_id.float", "variant_id.float", "in_stock", "raw_popularity.float", "stock_location_id.float"},
								"sort": []map[string]interface{}{
									{
										"id": "asc",
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}

			q.AddFacet(testCase.facetName, testCase.facetParams...)

			if !reflect.DeepEqual(q.Aggs, testCase.expectedAggs) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedAggs, q.Aggs)
			}
		})
	}
}

func TestSetPagination(t *testing.T) {
	size := 20
	page := 1

	q := Q{}
	q.SetPagination(size, page)

	expectedQuery := Q{
		Size:        size,
		From:        size * (page - 1),
		CurrentPage: page,
	}

	if !reflect.DeepEqual(q, expectedQuery) {
		t.Errorf("\nexpectation: %v\nreality: %v", expectedQuery, q)
	}
}

func TestSortBy(t *testing.T) {
	testCases := []struct {
		name              string
		field             string
		direction         string
		predefinedChannel string
		expectedSort      []map[string]interface{}
	}{
		{
			name:      "Sort by name",
			field:     "name",
			direction: "asc",
			expectedSort: []map[string]interface{}{
				{
					"name.enum": "asc",
				},
			},
		},
		{
			name:      "Sort by price",
			field:     "price",
			direction: "asc",
			expectedSort: []map[string]interface{}{
				{
					"price.float": "asc",
				},
			},
		},
		{
			name:      "Use default sort",
			direction: "asc",
			expectedSort: []map[string]interface{}{
				{
					"_score": "asc",
				},
			},
		},
		{
			name:              "Sort by discount",
			field:             "discount",
			direction:         "desc",
			predefinedChannel: "android",
			expectedSort: []map[string]interface{}{
				{
					"_script": map[string]interface{}{
						"type": "number",
						"script": map[string]string{
							"lang":   "painless",
							"source": "(doc['price_android.float'].empty||doc['normal_price.float'].empty)?0:1-(doc['price_android.float'].value/doc['normal_price.float'].value)",
						},
						"order": "desc",
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			if testCase.predefinedChannel != "" {
				q.Channel = testCase.predefinedChannel
			}

			q.SortBy(testCase.field, testCase.direction)

			if !reflect.DeepEqual(q.Sort, testCase.expectedSort) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedSort, q.Sort)
			}
		})
	}
}

func TestAddAnalyticTags(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		q := Q{}
		q.AddAnalyticTags("", "")

		if !reflect.DeepEqual(q, Q{}) {
			t.Errorf("\nexpectation: %v\nreality: %v", "AddAnalyticTags has no implementation", q)
		}
	})
}

func TestSetResultFields(t *testing.T) {
	testCases := []struct {
		name           string
		fields         []string
		expectedFields []string
	}{
		{
			name:           "With fields",
			fields:         []string{"name", "name_local", "price", "variant_id"},
			expectedFields: []string{"name", "name_local", "price.float", "variant_id.float"},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			q.SetResultFields(testCase.fields...)

			if !reflect.DeepEqual(q.Source, testCase.expectedFields) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedFields, q.Source)
			}
		})
	}
}

func TestAddFunctionalBoosts(t *testing.T) {
	testCases := []struct {
		name                   string
		fields                 []string
		expectedFunctionScores []map[string]interface{}
	}{
		{
			name:   "With fields",
			fields: []string{"raw_popularity"},
			expectedFunctionScores: []map[string]interface{}{
				{
					"field_value_factor": map[string]interface{}{
						"field":    "raw_popularity.float",
						"factor":   2.0,
						"modifier": "log2p",
						"missing":  1.0,
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			q.AddFunctionalBoosts(testCase.fields...)

			if !reflect.DeepEqual(q.FunctionScores, testCase.expectedFunctionScores) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedFunctionScores, q.FunctionScores)
			}
		})
	}
}

func TestAddFunctionalBoostsScore(t *testing.T) {
	testCases := []struct {
		name                   string
		fieldsScore            map[string]float64
		expectedFunctionScores []map[string]interface{}
	}{
		{
			name:        "With fields",
			fieldsScore: map[string]float64{"raw_popularity": 20},
			expectedFunctionScores: []map[string]interface{}{
				{
					"field_value_factor": map[string]interface{}{
						"field":    "raw_popularity.float",
						"factor":   20.0,
						"modifier": "log2p",
						"missing":  1.0,
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			q.AddFunctionalBoostsScore(testCase.fieldsScore)

			if !reflect.DeepEqual(q.FunctionScores, testCase.expectedFunctionScores) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedFunctionScores, q.FunctionScores)
			}
		})
	}
}

func TestAddBoosts(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		q := Q{}
		q.AddBoosts("", "")

		if !reflect.DeepEqual(q, Q{}) {
			t.Errorf("\nexpectation: %v\nreality: %v", "AddBoosts has no implementation", q)
		}
	})
}

func TestAddConversionBoosts(t *testing.T) {
	testCases := []struct {
		name                   string
		field                  string
		value                  map[string]float64
		expectedFunctionScores []map[string]interface{}
	}{
		{
			name:  "With fields",
			field: "product_type_ids",
			value: map[string]float64{
				"3268": 1.905084745762712,
			},
			expectedFunctionScores: []map[string]interface{}{
				{
					"weight": 1.905084745762712,
					"filter": map[string]interface{}{
						"term": map[string]interface{}{
							"product_type_ids.enum": "3268",
						},
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			q := Q{}
			q.AddConversionBoosts(testCase.field, testCase.value)

			if !reflect.DeepEqual(q.FunctionScores, testCase.expectedFunctionScores) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedFunctionScores, q.FunctionScores)
			}
		})
	}
}

func TestToJson(t *testing.T) {
	testCases := []struct {
		name         string
		facets       []string
		q            Q
		expectedJSON string
		expectedErr  bool
	}{
		{
			name:         "Empty Query",
			expectedJSON: `{"_source":false,"fields":["product_id.float","variant_id.float","in_stock","raw_popularity.float","promotion_type","trending_boost_score.float","trending_boost_updated_date"],"from":0,"query":{"function_score":{"boost_mode":"multiply","functions":null,"query":{"bool":{"filter":{"bool":null}}},"score_mode":"sum"}},"size":0,"sort":[{"_score":"desc"},{"id":"asc"}]}`,
			expectedErr:  false,
		},
		{
			name:         "Query with Brand and Taxon Facet",
			facets:       []string{"brands", "taxons"},
			expectedJSON: `{"_source":false,"aggs":{"brands":{"terms":{"field":"brand_id.float","size":100}},"taxons":{"terms":{"field":"taxon_ids.float","size":100}}},"fields":["product_id.float","variant_id.float","in_stock","raw_popularity.float","promotion_type","trending_boost_score.float","trending_boost_updated_date"],"from":0,"query":{"function_score":{"boost_mode":"multiply","functions":null,"query":{"bool":{"filter":{"bool":null}}},"score_mode":"sum"}},"size":0,"sort":[{"_score":"desc"},{"id":"asc"}]}`,
			expectedErr:  false,
		},
		{
			name: "Request exceed max data limit",
			q: Q{
				From:    0,
				Size:    20,
				MaxData: 10,
			},
			expectedJSON: "",
			expectedErr:  true,
		},
		{
			name: "Query with script scores",
			q: Q{
				ScriptScores: []string{"_score", "_score"},
			},
			expectedJSON: `{"_source":false,"fields":["product_id.float","variant_id.float","in_stock","raw_popularity.float","promotion_type","trending_boost_score.float","trending_boost_updated_date"],"from":0,"query":{"function_score":{"boost_mode":"multiply","functions":[{"script_score":{"script":{"source":"Math.max(_score * (_score * _score) - _score, 0)"}}}],"query":{"bool":{"filter":{"bool":null}}},"score_mode":"sum"}},"size":0,"sort":[{"_score":"desc"},{"id":"asc"}]}`,
			expectedErr:  false,
		},
		{
			name: "Query with channel",
			q: Q{
				Channel: "webapp",
			},
			expectedJSON: `{"_source":false,"fields":["product_id.float","variant_id.float","in_stock","raw_popularity.float","promotion_type","trending_boost_score.float","trending_boost_updated_date","price_webapp.float"],"from":0,"query":{"function_score":{"boost_mode":"multiply","functions":null,"query":{"bool":{"filter":{"bool":null}}},"score_mode":"sum"}},"size":0,"sort":[{"_score":"desc"},{"id":"asc"}]}`,
			expectedErr:  false,
		},
		{
			name: "Multiple queries with country TH and fuzzy queries",
			q: Q{
				Country:      "TH",
				Queries:      []string{"life", "lifes"},
				FuzzyQueries: []string{"life"},
			},
			expectedJSON: `{"_source":false,"fields":["product_id.float","variant_id.float","in_stock","raw_popularity.float","promotion_type","trending_boost_score.float","trending_boost_updated_date"],"from":0,"query":{"function_score":{"boost_mode":"multiply","functions":null,"query":{"bool":{"filter":{"bool":null},"must":{"bool":{"should":[{"bool":{"should":[{"multi_match":{"boost":1,"fields":["sku^10.0","sku.stem^9.5","sku.prefix^1.0","sku.joined^7.5","sku.delimiter^4.0","brand_name^5.0","brand_name.stem^4.75","brand_name.joined^3.75","brand_name.delimiter^2.0","name^10.0","name.stem^9.5","name.joined^7.5","name.delimiter^4.0","name_local^10.0","name_local.stem^9.5","name_local.joined^7.5","name_local.delimiter^4.0","brand_name_local^5.0","brand_name_local.stem^4.75","brand_name_local.joined^3.75","brand_name_local.delimiter^2.0","brand_name.prefix^0.5","name.prefix^1.0","name_local.prefix^1.0","brand_name_local.prefix^0.5"],"operator":"AND","query":"life","type":"cross_fields"}},{"multi_match":{"boost":0.5,"fields":["sku^10.0","sku.stem^9.5","sku.prefix^1.0","sku.joined^7.5","sku.delimiter^4.0","brand_name^5.0","brand_name.stem^4.75","brand_name.joined^3.75","brand_name.delimiter^2.0","name^10.0","name.stem^9.5","name.joined^7.5","name.delimiter^4.0","name_local^10.0","name_local.stem^9.5","name_local.joined^7.5","name_local.delimiter^4.0","brand_name_local^5.0","brand_name_local.stem^4.75","brand_name_local.joined^3.75","brand_name_local.delimiter^2.0","brand_name.prefix^0.5","name.prefix^1.0","name_local.prefix^1.0","brand_name_local.prefix^0.5"],"operator":"AND","query":"lifes","type":"cross_fields"}}]}},{"constant_score":{"boost":0.5,"filter":{"bool":{"should":[{"multi_match":{"fields":["name^1.0","name_local^1.0","brand_name^0.5","brand_name_local^0.5"],"fuzziness":"AUTO:4,11","fuzzy_transpositions":false,"max_expansions":5,"operator":"AND","prefix_length":2,"query":"life"}}]}}}}]}}}},"score_mode":"sum"}},"size":0,"sort":[{"_score":"desc"},{"id":"asc"}]}`,
			expectedErr:  false,
		},
		{
			name: "Error encoding elasticsearch request",
			q: Q{
				FunctionScores: []map[string]interface{}{
					{
						"filter": make(chan error),
					},
				},
			},
			expectedJSON: "",
			expectedErr:  true,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			for _, facet := range testCase.facets {
				testCase.q.AddFacet(facet)
			}

			jsonized, err := testCase.q.ToJson()
			if (err != nil) != testCase.expectedErr {
				t.Errorf("\nexpectation: %t\nreality: %v", testCase.expectedErr, err)
			}

			if string(jsonized) != testCase.expectedJSON {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedJSON, string(jsonized))
			}
		})
	}
}
