package search

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"happyfresh.io/lib/str"
)

type Q struct {
	Queries                   []string
	FuzzyQueries              []string
	Filter                    map[string][]interface{}
	Size                      int
	From                      int
	CurrentPage               int
	Sort                      []map[string]interface{}
	Source                    []string
	ScriptScores              []string
	FunctionScores            []map[string]interface{}
	Aggs                      map[string]interface{}
	MaxData                   int
	Channel                   string
	Country                   string
	SearchIntentionAttributes []map[string]interface{}
	ResultPinning             []int64
}

func (s *Q) SetQueryString(qs string) {
	if len(qs) == 0 {
		return
	}

	if s.Queries == nil {
		s.Queries = []string{qs}
		return
	}

	s.Queries = append(s.Queries, qs)
}

func (s *Q) SetFuzzyQueries(fqs []string) {
	s.FuzzyQueries = fqs
}

func (s *Q) SetGrouping(f string) {
	return
}

func (s *Q) SetLocale(l string) {
	return
}

func (s *Q) SetChannel(channel string) {
	switch channel {
	case "android", "ios", "webapp", "mobileapp", "grabfresh":
		s.Channel = channel
	}
}

func (s *Q) AddTermFilter(field string, value ...interface{}) {
	return
}

func (s *Q) AddTermFilterInt64(field string, value ...int64) {
	if len(value) == 0 {
		return
	}

	if s.Filter == nil {
		s.Filter = map[string][]interface{}{}
	}

	if s.Filter["must"] == nil {
		s.Filter["must"] = []interface{}{}
	}

	field = fmt.Sprintf("%s.float", field)
	filter := map[string]interface{}{
		"terms": map[string][]int64{
			field: value,
		},
	}

	s.Filter["must"] = append(s.Filter["must"], filter)
}

func (s *Q) AddTermFilterString(field string, value ...string) {
	if len(value) == 0 {
		return
	}

	if s.Filter == nil {
		s.Filter = map[string][]interface{}{}
	}

	if s.Filter["must"] == nil {
		s.Filter["must"] = []interface{}{}
	}

	field = fmt.Sprintf("%s.enum", field)
	filter := map[string]interface{}{
		"terms": map[string][]string{
			field: value,
		},
	}

	s.Filter["must"] = append(s.Filter["must"], filter)
}

func (s *Q) AddGeoFilter(field string, lat, long, distance float64, unit string) {
	return
}

func (s *Q) AddRangeFilter(field string, start, end float64) {
	return
}

func (s *Q) AddDateRangeFilter(field string, start, end time.Time) {
	return
}

func (s *Q) AddNoneTermFilterString(field string, value ...string) {
	if len(value) == 0 {
		return
	}

	if s.Filter == nil {
		s.Filter = map[string][]interface{}{}
	}

	if s.Filter["must_not"] == nil {
		s.Filter["must_not"] = []interface{}{}
	}

	field = fmt.Sprintf("%s", field)
	filter := map[string]interface{}{
		"terms": map[string][]string{
			field: value,
		},
	}

	s.Filter["must_not"] = append(s.Filter["must_not"], filter)
}

func (s *Q) AddNoneTermFilterInt64(field string, value ...int64) {
	if len(value) == 0 {
		return
	}

	if s.Filter == nil {
		s.Filter = map[string][]interface{}{}
	}

	if s.Filter["must_not"] == nil {
		s.Filter["must_not"] = []interface{}{}
	}

	field = fmt.Sprintf("%s.float", field)
	filter := map[string]interface{}{
		"terms": map[string][]int64{
			field: value,
		},
	}

	s.Filter["must_not"] = append(s.Filter["must_not"], filter)
}

func (s *Q) AddFacet(name string, params ...interface{}) {
	if s.Aggs == nil {
		s.Aggs = make(map[string]interface{})
	}

	var field string
	var termsSize int64
	var extraAggs map[string]interface{}

	switch name {
	case "brands":
		field = "brand_id.float"
		termsSize = 100
	case "taxons":
		field = "taxon_ids.float"
		termsSize = 100
	case "stock_location_ids":
		field = "stock_location_id.float"

		termsSize = 10
		if len(params) > 0 {
			termsSizeCandidate := str.MaybeString(params[0]).Int64()
			if termsSizeCandidate != -1 {
				termsSize = termsSizeCandidate
			}
		}

		tophitsSize := int64(5)
		if len(params) > 1 {
			tophitsSizeCandidate := str.MaybeString(params[1]).Int64()
			if tophitsSizeCandidate != -1 {
				tophitsSize = tophitsSizeCandidate
			}
		}

		facetSort := append(s.Sort, map[string]interface{}{"id": "asc"})
		extraAggs = map[string]interface{}{
			"top_hit_doc": map[string]interface{}{
				"top_hits": map[string]interface{}{
					"_source": false,
					"size":    tophitsSize,
					"fields":  []string{"product_id.float", "variant_id.float", "in_stock", "raw_popularity.float", "stock_location_id.float"},
					"sort":    facetSort,
				},
			},
		}
	default:
		return
	}

	aggs := map[string]interface{}{
		"terms": map[string]interface{}{
			"field": field,
			"size":  termsSize,
		},
	}

	if len(extraAggs) > 0 {
		aggs["aggs"] = extraAggs
	}

	s.Aggs[name] = aggs
}

func (s *Q) SetPagination(size, current int) {
	s.Size = size
	s.From = size * (current - 1)
	s.CurrentPage = current
}

func (s *Q) SortBy(field, direction string) {
	if s.Sort == nil {
		s.Sort = []map[string]interface{}{}
	}

	switch field {
	case "discount", "promotion_type":
		if s.Channel == "" {
			return
		}

		script := fmt.Sprintf("(doc['price_%s.float'].empty||doc['normal_price.float'].empty)?0:1-(doc['price_%s.float'].value/doc['normal_price.float'].value)", s.Channel, s.Channel)

		s.Sort = append(s.Sort, map[string]interface{}{
			"_script": map[string]interface{}{
				"type": "number",
				"script": map[string]string{
					"lang":   "painless",
					"source": script,
				},
				"order": direction,
			},
		})

		return
	case "name", "name_local", "in_stock":
		field = fmt.Sprintf("%s.enum", field)
	case "popularity",
		"price", "price_ios", "price_android", "price_webapp", "price_mobileweb", "price_grabfresh",
		"unit_price", "unit_price_ios", "unit_price_android", "unit_price_webapp", "unit_price_mobileweb", "unit_price_grabfresh":
		field = fmt.Sprintf("%s.float", field)
	default:
		field = "_score"
	}

	s.Sort = append(s.Sort, map[string]interface{}{field: direction})
}

func (s *Q) AddAnalyticTags(field string, value interface{}) {
	return
}

func (s *Q) SetResultFields(fields ...string) {
	source := []string{}

	for _, field := range fields {
		switch field {
		case "name", "name_local", "in_stock", "promotion_type":
			source = append(source, field)
		case "product_id", "variant_id", "raw_popularity", "popularity",
			"price", "price_ios", "price_android", "price_webapp", "price_mobileweb", "price_grabfresh":
			source = append(source, fmt.Sprintf("%s.float", field))
		}
	}

	s.Source = source
}

func (s *Q) AddFunctionalBoosts(fields ...string) {
	if s.FunctionScores == nil {
		s.FunctionScores = []map[string]interface{}{}
	}

	for _, field := range fields {
		s.FunctionScores = append(s.FunctionScores, map[string]interface{}{
			"field_value_factor": map[string]interface{}{
				"field":    fmt.Sprintf("%s.float", field),
				"factor":   2.0,
				"modifier": "log2p",
				"missing":  1.0,
			},
		})
	}
}

func (s *Q) AddFunctionalBoostsScore(fieldsScore map[string]float64) {
	if s.FunctionScores == nil {
		s.FunctionScores = []map[string]interface{}{}
	}

	for field, score := range fieldsScore {
		s.FunctionScores = append(s.FunctionScores, map[string]interface{}{
			"field_value_factor": map[string]interface{}{
				"field":    fmt.Sprintf("%s.float", field),
				"factor":   score,
				"modifier": "log2p",
				"missing":  1.0,
			},
		})
	}
}

func (s *Q) AddBoosts(field string, value ...interface{}) {
	return
}

func (s *Q) AddConversionBoosts(field string, value interface{}) {
	if field == "" {
		return
	}

	boostings, ok := value.(map[string]float64)
	if !ok {
		return
	}

	if s.FunctionScores == nil {
		s.FunctionScores = []map[string]interface{}{}
	}

	for k, v := range boostings {
		s.FunctionScores = append(s.FunctionScores, map[string]interface{}{
			"weight": v,
			"filter": map[string]interface{}{
				"term": map[string]interface{}{
					fmt.Sprintf("%s.enum", field): k,
				},
			},
		})
	}
}

func (s *Q) AddTrendingBoost(weight float64, activePeriod int64) {
	if s.FunctionScores == nil {
		s.FunctionScores = []map[string]interface{}{}
	}

	var gtePeriod string
	if activePeriod < 1 {
		gtePeriod = "now/d"
	} else {
		if activePeriod > 6 {
			activePeriod = 6
		}

		gtePeriod = fmt.Sprintf("now/d-%dd", activePeriod)
	}

	s.FunctionScores = append(s.FunctionScores, map[string]interface{}{
		"weight": weight,
		"filter": map[string]interface{}{
			"bool": map[string][]interface{}{
				"must": {
					map[string]interface{}{
						"range": map[string]interface{}{
							"trending_boost_updated_date.date": map[string]interface{}{
								"gte": gtePeriod,
							},
						},
					},
					map[string]interface{}{
						"range": map[string]interface{}{
							"trending_boost_score.float": map[string]interface{}{
								"gt": 0,
							},
						},
					},
				},
			},
		},
	})
}

func (s *Q) AddResultPinning(pinnedVariantIDs []int64) {
	if len(pinnedVariantIDs) == 0 {
		return
	}

	if s.ResultPinning == nil {
		s.ResultPinning = pinnedVariantIDs
	} else {
		s.ResultPinning = append(s.ResultPinning, pinnedVariantIDs...)
	}
}

func (s *Q) SetCountry(country string) {
	s.Country = country
}

func (s *Q) AddSearchIntentionAttributes(taxonIDs, productTypeIDs, brandIDs []int64, score float64) {
	mustTerms := []map[string]interface{}{}
	if len(taxonIDs) > 0 {
		if len(taxonIDs) == 1 {
			mustTerms = append(mustTerms, map[string]interface{}{
				"terms": map[string][]int64{
					"taxon_ids.float": taxonIDs,
				},
			})
		} else {
			mustTerms = append(mustTerms, map[string]interface{}{
				"terms_set": map[string]interface{}{
					"taxon_ids.float": map[string]interface{}{
						"terms":                      taxonIDs,
						"minimum_should_match_field": 2,
					},
				},
			})
		}
	}
	if len(productTypeIDs) > 0 {
		mustTerms = append(mustTerms, map[string]interface{}{
			"terms": map[string][]int64{
				"product_type_ids.float": productTypeIDs,
			},
		})
	}
	if len(brandIDs) > 0 {
		mustTerms = append(mustTerms, map[string]interface{}{
			"terms": map[string][]int64{
				"brand_id.float": brandIDs,
			},
		})
	}

	if len(mustTerms) == 0 {
		return
	}

	s.SearchIntentionAttributes = append(s.SearchIntentionAttributes, map[string]interface{}{
		"constant_score": map[string]interface{}{
			"boost": score,
			"filter": map[string]interface{}{
				"bool": map[string]interface{}{
					"must": mustTerms,
				},
			},
		},
	})
}

func (s *Q) ToJson() ([]byte, error) {
	if s.MaxData == 0 {
		s.MaxData = 10000
	}
	if (s.Size + s.From) > s.MaxData {
		return nil, fmt.Errorf("Request exceed max data limit")
	}

	multiplierString := &bytes.Buffer{}
	for i, script := range s.ScriptScores {
		if i > 0 {
			multiplierString.WriteString(" * ")
		}

		multiplierString.WriteString(script)
	}
	if multiplierString.Len() != 0 {
		scriptScore := map[string]interface{}{
			"script_score": map[string]interface{}{
				"script": map[string]interface{}{
					"source": fmt.Sprintf("Math.max(_score * (%s) - _score, 0)", multiplierString.String()),
				},
			},
		}

		s.FunctionScores = append(s.FunctionScores, scriptScore)
	}

	if len(s.Source) == 0 {
		s.Source = []string{"product_id.float", "variant_id.float", "in_stock", "raw_popularity.float", "promotion_type", "trending_boost_score.float", "trending_boost_updated_date", "sku"}
		if s.Channel != "" {
			s.Source = append(s.Source, fmt.Sprintf("price_%s.float", s.Channel))
		}
	}

	if len(s.Sort) == 0 {
		s.Sort = []map[string]interface{}{
			{
				"_score": "desc",
			},
		}
	}
	s.Sort = append(s.Sort, map[string]interface{}{"id": "asc"})

	mainQuery := []map[string]interface{}{}
	mainQueryFields := []string{
		"sku^10.0",
		"sku.stem^9.5",
		"sku.prefix^1.0",
		"sku.joined^7.5",
		"sku.delimiter^4.0",
		"brand_name^5.0",
		"brand_name.stem^4.75",
		"brand_name.joined^3.75",
		"brand_name.delimiter^2.0",
		"name^10.0",
		"name.stem^9.5",
		"name.joined^7.5",
		"name.delimiter^4.0",
		"name_local^10.0",
		"name_local.stem^9.5",
		"name_local.joined^7.5",
		"name_local.delimiter^4.0",
		"brand_name_local^5.0",
		"brand_name_local.stem^4.75",
		"brand_name_local.joined^3.75",
		"brand_name_local.delimiter^2.0",
	}
	if s.Country == "TH" {
		mainQueryFields = append(mainQueryFields, []string{
			"brand_name.prefix^0.5",
			"name.prefix^1.0",
			"name_local.prefix^1.0",
			"brand_name_local.prefix^0.5",
		}...)
	}
	for i, q := range s.Queries {
		queryBoost := 1.0
		if i > 0 {
			queryBoost = 0.5
		}

		mainQuery = append(mainQuery, map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query":    q,
				"boost":    queryBoost,
				"type":     "cross_fields",
				"operator": "AND",
				"fields":   mainQueryFields,
			},
		})
	}

	fuzzyQuery := []map[string]interface{}{}
	for _, q := range s.FuzzyQueries {
		fuzzyQuery = append(fuzzyQuery, map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query":                q,
				"fuzziness":            "AUTO:4,11",
				"max_expansions":       5,
				"fuzzy_transpositions": false,
				"prefix_length":        2,
				"operator":             "AND",
				"fields": []string{
					"name^1.0",
					"name_local^1.0",
					"brand_name^0.5",
					"brand_name_local^0.5",
				},
			},
		})
	}

	boolQuery := map[string]interface{}{
		"filter": map[string]interface{}{
			"bool": s.Filter,
		},
	}
	if len(s.Queries) > 0 {
		matchQueries := []map[string]interface{}{
			{
				"bool": map[string]interface{}{
					"should": mainQuery,
				},
			},
		}

		if s.Country == "TH" {
			matchQueries = append(matchQueries, map[string]interface{}{
				"constant_score": map[string]interface{}{
					"boost": 0.5,
					"filter": map[string]interface{}{
						"bool": map[string]interface{}{
							"should": fuzzyQuery,
						},
					},
				},
			})
		}

		matchQueries = append(matchQueries, s.SearchIntentionAttributes...)

		boolQuery["must"] = map[string]interface{}{
			"bool": map[string]interface{}{
				"should": matchQueries,
			},
		}
	}

	resultPinningIDs := []string{}
	if s.ResultPinning != nil {
		for _, p := range s.ResultPinning {
			resultPinningIDs = append(resultPinningIDs, strconv.Itoa(int((p))))
		}
	}

	esQuery := map[string]interface{}{
		"query": map[string]interface{}{
			"pinned": map[string]interface{}{
				"ids": resultPinningIDs,
				"organic": map[string]interface{}{
					"function_score": map[string]interface{}{
						"boost_mode": "multiply",
						"score_mode": "sum",
						"query": map[string]interface{}{
							"bool": boolQuery,
						},
						"functions": s.FunctionScores,
					},
				},
			},
		},
		"fields":  s.Source,
		"sort":    s.Sort,
		"size":    s.Size,
		"from":    s.From,
		"_source": false,
	}

	if s.Aggs != nil {
		esQuery["aggs"] = s.Aggs
	}

	b, err := json.Marshal(esQuery)
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] error encoding request")
	}

	return b, nil
}
