package search

type esResponse struct {
	Hits *struct {
		Total *struct {
			Value int64 `json:"value"`
		} `json:"total"`
		Hits []*struct {
			Score  float64 `json:"_score"`
			EsID   string  `json:"_id"`
			Source *struct {
				ProductID                []float64 `json:"product_id.float"`
				VariantID                []float64 `json:"variant_id.float"`
				InStock                  []string  `json:"in_stock"`
				RawPopularity            []float64 `json:"raw_popularity.float"`
				TaxonIDs                 []float64 `json:"taxon_ids.float"`
				BrandID                  []float64 `json:"brand_id.float"`
				BrandName                []string  `json:"brand_name"`
				BrandNameLocal           []string  `json:"brand_name_local"`
				PriceAndroid             []float64 `json:"price_android.float"`
				PriceIos                 []float64 `json:"price_ios.float"`
				PriceWebapp              []float64 `json:"price_webapp.float"`
				PriceMobileweb           []float64 `json:"price_mobileweb.float"`
				PriceGrabfresh           []float64 `json:"price_grabfresh.float"`
				PromotionType            []string  `json:"promotion_type"`
				TrendingBoostScore       []float64 `json:"trending_boost_score.float"`
				TrendingBoostUpdatedDate []string  `json:"trending_boost_updated_date"`
				SKU                      []string  `json:"sku"`
			} `json:"fields"`
		} `json:"hits"`
	} `json:"hits"`
	Aggregations *struct {
		Brands *struct {
			Buckets []*struct {
				Key float64 `json:"key"`
			} `json:"buckets"`
		} `json:"brands"`
		Taxons *struct {
			Buckets []*struct {
				Key float64 `json:"key"`
			} `json:"buckets"`
		} `json:"taxons"`
		StockLocationIDs *struct {
			Buckets []*struct {
				Key       float64 `json:"key"`
				TopHitDoc *struct {
					Hits *struct {
						Hits []*struct {
							Score  float64 `json:"_score"`
							Source *struct {
								ProductID     []float64 `json:"product_id.float"`
								VariantID     []float64 `json:"variant_id.float"`
								InStock       []string  `json:"in_stock"`
								RawPopularity []float64 `json:"raw_popularity.float"`
							} `json:"fields"`
						} `json:"hits"`
					} `json:"hits"`
				} `json:"top_hit_doc"`
			} `json:"buckets"`
		} `json:"stock_location_ids"`
	} `json:"aggregations"`
}
