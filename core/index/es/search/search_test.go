package search

import (
	"context"
	"encoding/json"
	"testing"

	iface "happyfresh.io/search/core/index"
)

func TestStore(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		idx := index{}

		ctx := context.Background()
		data := struct{}{}

		err := idx.Store(ctx, data)

		if err != nil {
			t.Errorf("\nexpectation: nil\nreality: %v", err)
		}
	})
}

func TestMapAggToFilter(t *testing.T) {
	testCases := []struct {
		testName                string
		responseStr             string
		hasBrandFilter          bool
		brandFilterValue        []interface{}
		hasTaxonFilter          bool
		taxonFilterValue        []float64
		hasStockLocationIDAgg   bool
		stockLocationIDAggValue []interface{}
	}{
		{
			testName:    "Response has no aggs",
			responseStr: `{"took":100,"timed_out":false,"_shards":{"total":2,"successful":2,"skipped":0,"failed":0},"hits":{"total":{"value":122,"relation":"eq"},"max_score":null,"hits":[]}}`,
		},
		{
			testName:       "Response has brand and taxon aggs",
			responseStr:    `{"took":100,"timed_out":false,"_shards":{"total":2,"successful":2,"skipped":0,"failed":0},"hits":{"total":{"value":122,"relation":"eq"},"max_score":null,"hits":[]},"aggregations":{"brands":{"doc_count_error_upper_bound":-1,"sum_other_doc_count":117,"buckets":[{"key":7941,"doc_count":3,"top_hit":{"value":72.6276626586914},"top_hit_doc":{"hits":{"total":{"value":3,"relation":"eq"},"max_score":72.62766,"hits":[{"_index":".ent-search-engine-5e5c76b937342055495d78d8","_type":"_doc","_id":"5e69ae093734204e4bd273f6","_score":72.62766,"fields":{"brand_id.float":[7941]}}]}}},{"key":12728,"doc_count":1,"top_hit":{"value":72.46150207519531},"top_hit_doc":{"hits":{"total":{"value":1,"relation":"eq"},"max_score":72.4615,"hits":[{"_index":".ent-search-engine-5e5c76b937342055495d78d8","_type":"_doc","_id":"5e69a969373420956ad1b42e","_score":72.4615,"fields":{"brand_id.float":[12728],"brand_name":["telur prima"],"brand_name_local":["Telur Prima"]}}]}}}]},"taxons":{"doc_count_error_upper_bound":-1,"sum_other_doc_count":233,"buckets":[{"key":70,"doc_count":2,"top_hit":{"value":72.6276626586914},"top_hit_doc":{"hits":{"total":{"value":2,"relation":"eq"},"max_score":72.62766,"hits":[{"_index":".ent-search-engine-5e5c76b937342055495d78d8","_type":"_doc","_id":"5e69ae093734204e4bd273f6","_score":72.62766,"fields":{"taxon_ids.float":[73,70]}}]}}},{"key":73,"doc_count":1,"top_hit":{"value":72.6276626586914},"top_hit_doc":{"hits":{"total":{"value":1,"relation":"eq"},"max_score":72.62766,"hits":[{"_index":".ent-search-engine-5e5c76b937342055495d78d8","_type":"_doc","_id":"5e69ae093734204e4bd273f6","_score":72.62766,"fields":{"taxon_ids.float":[73,70]}}]}}}]}}}`,
			hasBrandFilter: true,
			brandFilterValue: []interface{}{
				&iface.Brand{
					BrandID: int64(7941),
				},
				&iface.Brand{
					BrandID: int64(12728),
				},
			},
			hasTaxonFilter:   true,
			taxonFilterValue: []float64{73, 70},
		},
		{
			testName:              "Response has stock location aggs",
			responseStr:           `{"took":100,"timed_out":false,"_shards":{"total":2,"successful":2,"skipped":0,"failed":0},"hits":{"total":{"value":330,"relation":"eq"},"max_score":null,"hits":[]},"aggregations":{"stock_location_ids":{"doc_count_error_upper_bound":0,"sum_other_doc_count":0,"buckets":[{"key":115,"doc_count":186,"top_hit_doc":{"hits":{"total":{"value":186,"relation":"eq"},"max_score":450.11914,"hits":[{"_index":".ent-search-engine-5e5c76b937342055495d78d8","_type":"_doc","_id":"5e74a3a137342040512fcfa4","_score":450.11914,"fields":{"in_stock":["true"],"raw_popularity.float":[0.55],"stock_location_id.float":[115],"product_id.float":[108009],"variant_id.float":[107954],"external_id":["853090"]}},{"_index":".ent-search-engine-5e5c76b937342055495d78d8","_type":"_doc","_id":"5e6a14a037342006210372b3","_score":440.14682,"fields":{"in_stock":["true"],"raw_popularity.float":[11.47],"stock_location_id.float":[115],"product_id.float":[37963],"variant_id.float":[37945],"external_id":["650274"]}}]}}},{"key":3,"doc_count":144,"top_hit_doc":{"hits":{"total":{"value":144,"relation":"eq"},"max_score":407.69366,"hits":[{"_index":".ent-search-engine-5e5c76b937342055495d78d8","_type":"_doc","_id":"5e69ab4b373420d37dd2018d","_score":407.69366,"fields":{"in_stock":["true"],"raw_popularity.float":[0.61],"stock_location_id.float":[3],"product_id.float":[15465],"variant_id.float":[15447],"external_id":["38791"]}},{"_index":".ent-search-engine-5e5c76b937342055495d78d8","_type":"_doc","_id":"5e69ce67373420121e9c6085","_score":395.2458,"fields":{"in_stock":["true"],"stock_location_id.float":[3],"product_id.float":[15501],"variant_id.float":[15483],"external_id":["192143"]}}]}}}]}}}`,
			hasStockLocationIDAgg: true,
			stockLocationIDAggValue: []interface{}{
				iface.StockItem{
					ID:              853090,
					ProductID:       108009,
					VariantID:       107954,
					InStock:         "true",
					RawPopularity:   0.55,
					Score:           450.11914,
					StockLocationID: 115,
				},
				iface.StockItem{
					ID:              650274,
					ProductID:       37963,
					VariantID:       37945,
					InStock:         "true",
					RawPopularity:   11.47,
					Score:           440.14682,
					StockLocationID: 115,
				},
				iface.StockItem{
					ID:              38791,
					ProductID:       15465,
					VariantID:       15447,
					InStock:         "true",
					RawPopularity:   0.61,
					Score:           407.69366,
					StockLocationID: 3,
				},
				iface.StockItem{
					ID:              192143,
					ProductID:       15501,
					VariantID:       15483,
					InStock:         "true",
					RawPopularity:   0,
					Score:           395.2458,
					StockLocationID: 3,
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.testName, func(t *testing.T) {
			response := &esResponse{}

			err := json.Unmarshal([]byte(testCase.responseStr), response)
			if err != nil {
				t.Errorf("\nexpectation: <nil>\nreality: %v", err)
			}

			filters := mapAggToFilters(response)

			brandFilter, brandOK := filters["brands"]
			if brandOK {
				if brandOK != testCase.hasBrandFilter {
					t.Errorf("\nexpectation: %v\nreality: %v", testCase.hasBrandFilter, brandOK)
				}

				for i, f := range brandFilter {
					bf := f.(*iface.Brand)
					ebf := testCase.brandFilterValue[i].(*iface.Brand)

					if ebf.BrandID != bf.BrandID {
						t.Errorf("\nexpectation: %v\nreality: %v", ebf.BrandID, bf.BrandID)
					}
				}
			}

			taxonFilter, taxonOK := filters["taxons"]

			if taxonOK {
				if taxonOK != testCase.hasTaxonFilter {
					t.Errorf("\nexpectation: %v\nreality: %v", testCase.hasTaxonFilter, taxonOK)
				}

				taxonFilterMap := make(map[float64]struct{})
				for _, tf := range taxonFilter {
					taxon, ok := tf.(float64)
					if !ok {
						continue
					}

					taxonFilterMap[taxon] = struct{}{}
				}

				for _, taxon := range testCase.taxonFilterValue {
					_, ok := taxonFilterMap[taxon]
					if !ok {
						t.Errorf("\nexpectation: %v\nreality: %v", testCase.taxonFilterValue, taxonFilter)
					}
				}
			}

			stockLocationIDAgg, sliOK := filters["stock_location_ids"]

			if sliOK {
				if sliOK != testCase.hasStockLocationIDAgg {
					t.Errorf("\nexpectation: %v\nreality: %v", testCase.hasStockLocationIDAgg, sliOK)
				}

				for i, f := range stockLocationIDAgg {
					bf := f.(iface.StockItem)
					ebf := testCase.stockLocationIDAggValue[i].(iface.StockItem)

					if ebf.ProductID != bf.ProductID {
						t.Errorf("\nexpectation: %v\nreality: %v", ebf.ProductID, bf.ProductID)
					}
					if ebf.VariantID != bf.VariantID {
						t.Errorf("\nexpectation: %v\nreality: %v", ebf.VariantID, bf.VariantID)
					}
					if ebf.InStock != bf.InStock {
						t.Errorf("\nexpectation: %v\nreality: %v", ebf.InStock, bf.InStock)
					}
					if ebf.RawPopularity != bf.RawPopularity {
						t.Errorf("\nexpectation: %v\nreality: %v", ebf.RawPopularity, bf.RawPopularity)
					}
				}
			}
		})
	}
}

func TestSuggestSearch(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		idx := index{}

		ctx := context.Background()
		opts := []iface.SearchOption{}

		_, err := idx.SuggestSearch(ctx, opts...)

		if err != nil {
			t.Errorf("\nexpectation: nil\nreality: %v", err)
		}
	})
}

func TestClick(t *testing.T) {
	t.Run("No implementation", func(t *testing.T) {
		idx := index{}

		ctx := context.Background()
		data := struct{}{}

		err := idx.Click(ctx, data)

		if err != nil {
			t.Errorf("\nexpectation: nil\nreality: %v", err)
		}
	})
}
