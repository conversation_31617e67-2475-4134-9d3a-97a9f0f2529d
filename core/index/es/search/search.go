package search

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"time"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	"github.com/pkg/errors"
	"happyfresh.io/lib/str"
	iface "happyfresh.io/search/core/index"
	"happyfresh.io/search/core/index/es"
)

type index struct {
	client    *elasticsearch.Client
	indexName string
}

func (i *index) Store(ctx context.Context, data ...interface{}) error {
	return nil
}

func (i *index) Search(ctx context.Context, options ...iface.SearchOption) (*iface.SearchResponse, error) {
	q := &Q{}
	for _, apply := range options {
		apply(q)
	}

	buf, err := q.ToJson()
	if err != nil {
		return nil, iface.NewError(http.StatusBadRequest, err)
	}

	response, err := i.client.Search(
		i.client.Search.WithContext(ctx),
		i.client.Search.WithIndex(i.indexName),
		i.client.Search.WithBody(bytes.NewReader(buf)),
		i.client.Search.WithHeader(map[string]string{"Accept-Encoding": "gzip"}),
		i.client.Search.WithFilterPath("-**._ignored"),
	)
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] search error")
	}

	b, err := i.readAll(response)
	if err != nil {
		return nil, err
	}

	if response.StatusCode >= http.StatusBadRequest || response.IsError() {
		errB := &es.ESError{}
		err = json.Unmarshal(b, errB)
		if err != nil {
			return nil, err
		}

		var rcType, rcReason string
		if len(errB.Error.RootCause) > 0 {
			rcType = errB.Error.RootCause[0].ErrType
			rcReason = errB.Error.RootCause[0].Reason
		}

		return nil, iface.NewError(
			str.MaybeString(errB.Status).Int(),
			errors.Errorf("[elasticsearch] error: %s, %s; %s, %s", response.Status(), errB.Error.Reason, rcType, rcReason),
		)
	}

	t := &esResponse{}
	err = json.Unmarshal(b, t)
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] failed to decode response")
	}

	searchResponse := &iface.SearchResponse{
		Meta: &iface.Meta{
			Page:       int64(q.CurrentPage),
			PageSize:   int64(q.Size),
			TotalPages: int64(math.Ceil(float64(t.Hits.Total.Value) / float64(q.Size))),
			Count:      int64(len(t.Hits.Hits)),
			TotalCount: t.Hits.Total.Value,
		},
		StockItems: make([]*iface.StockItem, len(t.Hits.Hits)),
		Filters:    make(map[string][]interface{}),
	}

	todayDate := time.Now().Format("2006-01-02")
	for i, r := range t.Hits.Hits {
		stockItem := &iface.StockItem{
			ProductID: str.MaybeString(r.Source.ProductID[0]).Int64(),
			VariantID: str.MaybeString(r.Source.VariantID[0]).Int64(),
			InStock:   r.Source.InStock[0],
			Score:     r.Score,
			EsID:      r.EsID,
			SKU:       r.Source.SKU[0],
		}

		if len(r.Source.RawPopularity) > 0 {
			stockItem.RawPopularity = r.Source.RawPopularity[0]
		}
		if len(r.Source.PromotionType) > 0 {
			stockItem.PromotionType = r.Source.PromotionType[0]
		}
		if len(r.Source.PriceAndroid) > 0 {
			stockItem.PriceAndroid = str.MaybeString(r.Source.PriceAndroid[0]).Float64()
		}
		if len(r.Source.PriceIos) > 0 {
			stockItem.PriceIos = str.MaybeString(r.Source.PriceIos[0]).Float64()
		}
		if len(r.Source.PriceWebapp) > 0 {
			stockItem.PriceWebapp = str.MaybeString(r.Source.PriceWebapp[0]).Float64()
		}
		if len(r.Source.PriceMobileweb) > 0 {
			stockItem.PriceMobileweb = str.MaybeString(r.Source.PriceMobileweb[0]).Float64()
		}
		if len(r.Source.PriceGrabfresh) > 0 {
			stockItem.PriceGrabfresh = str.MaybeString(r.Source.PriceGrabfresh[0]).Float64()
		}
		if len(r.Source.TrendingBoostUpdatedDate) > 0 && len(r.Source.TrendingBoostScore) > 0 {
			if r.Source.TrendingBoostUpdatedDate[0] == todayDate && str.MaybeString(r.Source.TrendingBoostScore[0]).Float64() > 0 {
				stockItem.Trending = true
			}
		}

		searchResponse.StockItems[i] = stockItem
	}

	filters := mapAggToFilters(t)
	if filters != nil {
		searchResponse.Filters = filters
	}

	return searchResponse, nil
}

func mapAggToFilters(t *esResponse) map[string][]interface{} {
	if t.Aggregations == nil {
		return nil
	}

	filter := make(map[string][]interface{})

	if t.Aggregations.Brands != nil {
		brandFilter := []interface{}{}
		for _, brand := range t.Aggregations.Brands.Buckets {
			f := &iface.Brand{
				BrandID: int64(brand.Key),
			}

			brandFilter = append(brandFilter, f)
		}

		filter["brands"] = brandFilter
	}

	if t.Aggregations.Taxons != nil {
		taxonFilter := []interface{}{}
		for _, taxonIDs := range t.Aggregations.Taxons.Buckets {
			taxonFilter = append(taxonFilter, taxonIDs.Key)
		}

		filter["taxons"] = taxonFilter
	}

	if t.Aggregations.StockLocationIDs != nil {
		stockItems := []interface{}{}
		for _, stockLocationID := range t.Aggregations.StockLocationIDs.Buckets {
			for _, r := range stockLocationID.TopHitDoc.Hits.Hits {
				stockItem := iface.StockItem{
					ProductID:       str.MaybeString(r.Source.ProductID[0]).Int64(),
					VariantID:       str.MaybeString(r.Source.VariantID[0]).Int64(),
					InStock:         r.Source.InStock[0],
					Score:           r.Score,
					StockLocationID: str.MaybeString(stockLocationID.Key).Int64(),
				}

				if len(r.Source.RawPopularity) > 0 {
					stockItem.RawPopularity = r.Source.RawPopularity[0]
				}

				stockItems = append(stockItems, stockItem)
			}
		}

		filter["stock_location_ids"] = stockItems
	}

	return filter
}

func (i *index) SuggestSearch(ctx context.Context, options ...iface.SearchOption) ([]string, error) {
	return nil, nil
}

func (i *index) Click(ctx context.Context, data interface{}) error {
	return nil
}

func (i *index) readAll(r *esapi.Response) (res []byte, err error) {
	body := r.Body
	if r.Header.Get("content-encoding") == "gzip" {
		body, err = gzip.NewReader(r.Body)
		if err != nil {
			return nil, err
		}
	}
	defer body.Close()

	return ioutil.ReadAll(body)
}

type dialer struct {
	rt http.RoundTripper
}

func (d *dialer) Dial(u string) (iface.Index, error) {
	uri, err := url.Parse(u)
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] error parsing index URL")
	}

	pw, _ := uri.User.Password()

	es, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: []string{fmt.Sprintf("%s://%s", uri.Scheme, uri.Host)},
		Username:  uri.User.Username(),
		Password:  pw,
		Transport: d.rt,
	})
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] error initializing client")
	}

	return &index{client: es, indexName: uri.Path[1:]}, nil
}

func NewDialer(opts ...dialerOption) iface.Dialer {
	d := &dialer{}
	for _, apply := range opts {
		apply(d)
	}

	return d
}

type dialerOption func(*dialer)

func WithIndexHTTPRoundTripper(rt http.RoundTripper) dialerOption {
	return func(d *dialer) {
		d.rt = rt
	}
}
