package index

import (
	"strings"
	"time"
)

type SearchRequest interface {
	SetQueryString(q string)
	SetFuzzyQueries(fqs []string)
	SetGrouping(field string)
	SetLocale(locale string)
	SetChannel(channel string)
	AddGeoFilter(field string, lat, long, distance float64, unit string)
	AddTermFilter(field string, value ...interface{})
	AddTermFilterInt64(field string, value ...int64)
	AddTermFilterString(field string, value ...string)
	AddNoneTermFilterString(field string, value ...string)
	AddNoneTermFilterInt64(field string, value ...int64)
	AddRangeFilter(field string, start, end float64)
	AddDateRangeFilter(field string, start, end time.Time)
	AddBoosts(field string, value ...interface{})
	AddFunctionalBoosts(fields ...string)
	AddFunctionalBoostsScore(map[string]float64)
	AddFacet(field string, params ...interface{})
	SetPagination(size, current int)
	SortBy(field, direction string)
	SetResultFields(fields ...string)
	AddAnalyticTags(field string, value interface{})
	AddConversionBoosts(field string, value interface{})
	AddTrendingBoost(weight float64, activePeriod int64)
	AddResultPinning(pinnedVariantIDs []int64)
	SetCountry(country string)
	AddSearchIntentionAttributes(taxonIDs, productTypeIDs, brandIDs []int64, score float64)
}

type SearchResponse struct {
	Meta       *Meta
	StockItems []*StockItem
	Filters    map[string][]interface{}
}

type Meta struct {
	SearchID   string
	Page       int64
	PageSize   int64
	TotalPages int64
	Count      int64
	TotalCount int64
}

type StockItem struct {
	EsID                 string   `json:"es_id,omitempty"`
	ID                   int64    `json:"id,omitempty"`
	ProductID            int64    `json:"product_id,omitempty"`
	VariantID            int64    `json:"variant_id,omitempty"`
	InStock              string   `json:"in_stock,omitempty"`
	RawPopularity        float64  `json:"raw_popularity,omitempty"`
	SKU                  string   `json:"sku,omitempty"`
	TaxonIDs             []int64  `json:"taxon_ids,omitempty"`
	TaxonName            []string `json:"taxon_name,omitempty"`
	TaxonNameLocal       []string `json:"taxon_name_local,omitempty"`
	ProductTypeIDs       []string `json:"product_type_ids,omitempty"`
	ProductTypeName      []string `json:"product_type_name,omitempty"`
	ProductTypeNameLocal []string `json:"product_type_name_local,omitempty"`
	BrandID              int64    `json:"brand_id,omitempty"`
	BrandName            string   `json:"brand_name,omitempty"`
	BrandNameLocal       string   `json:"brand_name_local,omitempty"`
	VendorID             int64    `json:"vendor_id,omitempty"`
	Name                 string   `json:"name,omitempty"`
	NameLocal            string   `json:"name_local,omitempty"`
	Description          string   `json:"description,omitempty"`
	DescriptionLocal     string   `json:"description_local,omitempty"`
	StoreID              int64    `json:"store_id,omitempty"`
	StockLocationID      int64    `json:"stock_location_id,omitempty"`
	SellableItems        string   `json:"sellable_items,omitempty"`

	// Sirius Boosting = log ( boosting point from aloha * max raw popularity ) + max raw popularity
	// Popularity = Sirius Boosting + raw popularity
	Popularity    float64 `json:"popularity,omitempty"`
	MaxPopularity float64 `json:"-"`

	// Trending Boost information
	TrendingBoostScore       float64 `json:"trending_boost_score,omitempty"`
	TrendingBoostUpdatedDate string  `json:"trending_boost_updated_date,omitempty"`
	Trending                 bool    `json:"-"`

	// Price will be stored even if its empty to filter products with no price
	Price       float64 `json:"price"`
	NormalPrice float64 `json:"normal_price"`

	PriceAndroid       float64 `json:"price_android,omitempty"`
	PriceIos           float64 `json:"price_ios,omitempty"`
	PriceWebapp        float64 `json:"price_webapp,omitempty"`
	PriceMobileweb     float64 `json:"price_mobileweb,omitempty"`
	PriceGrabfresh     float64 `json:"price_grabfresh,omitempty"`
	UnitPrice          float64 `json:"unit_price,omitempty"`
	UnitPriceAndroid   float64 `json:"unit_price_android,omitempty"`
	UnitPriceIos       float64 `json:"unit_price_ios,omitempty"`
	UnitPriceWebapp    float64 `json:"unit_price_webapp,omitempty"`
	UnitPriceMobileweb float64 `json:"unit_price_mobileweb,omitempty"`
	UnitPriceGrabfresh float64 `json:"unit_price_grabfresh,omitempty"`
	PromotionType      string  `json:"promotion_type"`
	CountryIso         string  `json:"country_iso,omitempty"`
	LastUpdated        string  `json:"last_updated,omitempty"`
	CreatedAt          string  `json:"-"`

	Score    float64     `json:"-"`
	GroupKey interface{} `json:"-"`
}

type Brand struct {
	BrandID int64
}

type SearchOption func(SearchRequest)

func WithTermQueries(ks ...string) SearchOption {
	return func(r SearchRequest) {
		for _, k := range ks {
			r.SetQueryString(k)
		}
	}
}

func WithTermQuery(k string) SearchOption {
	return func(r SearchRequest) {
		r.SetQueryString(k)
	}
}

func WithFuzzyQueries(fqs ...string) SearchOption {
	return func(r SearchRequest) {
		r.SetFuzzyQueries(fqs)
	}
}

func WithTermFilter(k string, v ...interface{}) SearchOption {
	return func(r SearchRequest) {
		r.AddTermFilter(k, v...)
	}
}

func WithTermFilterInt64(k string, v ...int64) SearchOption {
	return func(r SearchRequest) {
		r.AddTermFilterInt64(k, v...)
	}
}

func WithTermFilterString(k string, v ...string) SearchOption {
	return func(r SearchRequest) {
		r.AddTermFilterString(k, v...)
	}
}

func WithNoneTermFilterString(k string, v ...string) SearchOption {
	return func(r SearchRequest) {
		r.AddNoneTermFilterString(k, v...)
	}
}

func WithGeoFilter(k string, lat, long, distance float64, unit string) SearchOption {
	return func(r SearchRequest) {
		r.AddGeoFilter(k, lat, long, distance, unit)
	}
}

func WithRangeFilter(k string, s, e float64) SearchOption {
	return func(r SearchRequest) {
		r.AddRangeFilter(k, s, e)
	}
}

func WithDateRangeFilter(k string, s, e time.Time) SearchOption {
	return func(r SearchRequest) {
		r.AddDateRangeFilter(k, s, e)
	}
}

func WithNoneTermFilterInt64(k string, v ...int64) SearchOption {
	return func(r SearchRequest) {
		r.AddNoneTermFilterInt64(k, v...)
	}
}

func WithGrouping(k string) SearchOption {
	return func(r SearchRequest) {
		r.SetGrouping(k)
	}
}

func WithPagination(size, current int) SearchOption {
	return func(r SearchRequest) {
		r.SetPagination(size, current)
	}
}

func WithSorting(sorting string) SearchOption {
	return func(r SearchRequest) {
		sort := strings.Split(sorting, " ")
		if len(sort) < 2 {
			return
		}

		r.SortBy(sort[0], sort[1])
	}
}

func WithBoost(k string, v interface{}) SearchOption {
	return func(r SearchRequest) {
		r.AddBoosts(k, v)
	}
}

func WithConversionBoost(k string, v interface{}) SearchOption {
	return func(r SearchRequest) {
		r.AddConversionBoosts(k, v)
	}
}

func WithFunctionalBoosts(field ...string) SearchOption {
	return func(r SearchRequest) {
		r.AddFunctionalBoosts(field...)
	}
}

func WithFunctionalBoostsScore(fieldsScore map[string]float64) SearchOption {
	return func(r SearchRequest) {
		r.AddFunctionalBoostsScore(fieldsScore)
	}
}

func WithTrendingBoost(weight float64, activePeriod int64) SearchOption {
	return func(r SearchRequest) {
		r.AddTrendingBoost(weight, activePeriod)
	}
}

func WithLocale(l string) SearchOption {
	return func(r SearchRequest) {
		r.SetLocale(l)
	}
}

func WithChannel(channel string) SearchOption {
	return func(r SearchRequest) {
		r.SetChannel(channel)
	}
}

func WithAnalyticTags(field string, value interface{}) SearchOption {
	return func(r SearchRequest) {
		r.AddAnalyticTags(field, value)
	}
}

func WithResultFields(fields ...string) SearchOption {
	return func(r SearchRequest) {
		r.SetResultFields(fields...)
	}
}

func WithFacet(field string, params ...interface{}) SearchOption {
	return func(r SearchRequest) {
		r.AddFacet(field, params...)
	}
}

func WithResultPinning(pinnedVariantIDs []int64) SearchOption {
	return func(r SearchRequest) {
		r.AddResultPinning(pinnedVariantIDs)
	}
}

func WithCountry(c string) SearchOption {
	return func(r SearchRequest) {
		r.SetCountry(c)
	}
}

func WithSearchIntentionAttributes(taxonIDs, productTypeIDs, brandIDs []int64, score float64) SearchOption {
	return func(r SearchRequest) {
		r.AddSearchIntentionAttributes(taxonIDs, productTypeIDs, brandIDs, score)
	}
}
