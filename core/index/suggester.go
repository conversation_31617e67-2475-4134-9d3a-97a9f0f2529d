package index

import (
	"context"
)

type SuggestRequest interface {
	SetQuery(string)
	SetStockLocationIDs(...int64)
	SetSupplierIDs(...int64)
	SetSupplierStoreCategories(...int64)
	SetLocale(string)
	SetFuzziness(int64)
	SetSize(int64)
}

type SuggestResponse struct {
	Keywords        []string
	KeywordInTaxons []*KeywordInTaxon
	Taxons          []*IDNameSuggestion
	Brands          []*IDNameSuggestion
}

type KeywordInTaxon struct {
	Keyword   string
	TaxonID   int
	TaxonName string
}

type IDNameSuggestion struct {
	ID   int64
	Name string
}

type SuggestOption func(SuggestRequest)

type Suggester interface {
	Suggest(ctx context.Context, options ...SuggestOption) (*SuggestResponse, error)
}

type SuggesterShards interface {
	Get(key string) (Suggester, error)
	Register(shardURLs map[string]string) error
}

func WithSuggesterQuery(q string) SuggestOption {
	return func(r SuggestRequest) {
		r.SetQuery(q)
	}
}

func WithSuggesterStockLocationID(stockLocationIDs ...int64) SuggestOption {
	return func(r SuggestRequest) {
		r.SetStockLocationIDs(stockLocationIDs...)
	}
}

func WithSuggesterSupplierID(supplierIDs ...int64) SuggestOption {
	return func(r SuggestRequest) {
		r.SetSupplierIDs(supplierIDs...)
	}
}

func WithSuggesterStoreCategory(storeCategories ...int64) SuggestOption {
	return func(r SuggestRequest) {
		r.SetSupplierStoreCategories()
	}
}

func WithSuggesterLocale(locale string) SuggestOption {
	return func(r SuggestRequest) {
		r.SetLocale(locale)
	}
}

func WithSuggesterFuzziness(ed int64) SuggestOption {
	return func(r SuggestRequest) {
		r.SetFuzziness(ed)
	}
}

func WithSuggesterSize(size int64) SuggestOption {
	return func(r SuggestRequest) {
		r.SetSize(size)
	}
}
