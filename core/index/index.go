package index

import (
	"context"
	"net/http"
	"strings"
	"sync"

	"github.com/pkg/errors"
)

var (
	UseTaxonSuggestion bool

	bLock sync.RWMutex
	bMap  = map[string]Dialer{}
)

type Index interface {
	Store(ctx context.Context, data ...interface{}) error
	Search(ctx context.Context, options ...SearchOption) (*SearchResponse, error)
	SuggestSearch(ctx context.Context, options ...SearchOption) ([]string, error)
	Click(ctx context.Context, data interface{}) error
}

type Client interface {
	Index(key string) (Index, error)
}

type Dialer interface {
	Dial(string) (Index, error)
}

type client struct {
	lock   sync.RWMutex
	dialer Dialer
	shards map[string]Index
}

func (c *client) Index(key string) (Index, error) {
	upperCaseKey := strings.ToUpper(key)
	c.lock.RLock()
	i, ok := c.shards[upperCaseKey]
	c.lock.RUnlock()

	if !ok {
		return nil, errors.Errorf("Index shard not found: [%s]", upperCaseKey)
	}

	return i, nil
}

func (c *client) Indices() map[string]Index {
	return c.shards
}

func SetUseTaxonSuggestion(tf bool) {
	UseTaxonSuggestion = tf
}

type Option func(*client)

func WithShard(k, v string) Option {
	return func(c *client) {
		k = strings.ToUpper(k)
		i, err := c.dialer.Dial(v)
		if err == nil {
			c.lock.Lock()
			defer c.lock.Unlock()
			c.shards[k] = i
		}
	}
}

func WithHTTPClient(h *http.Client) Option {
	return func(c *client) {}
}

func New(backend string, opts ...Option) (Client, error) {
	bLock.RLock()
	dialer, ok := bMap[backend]
	bLock.RUnlock()

	if !ok {
		return nil, errors.Errorf("Unknown backend: [%s]", backend)
	}

	c := &client{
		dialer: dialer,
		shards: map[string]Index{},
	}

	for _, apply := range opts {
		apply(c)
	}

	return c, nil
}

func Register(name string, dial Dialer) {
	bLock.Lock()
	defer bLock.Unlock()
	if _, ok := bMap[name]; ok {
		panic(errors.New("Broker already registered"))
	}

	bMap[name] = dial
}
