package es

import (
	"context"
	"errors"
	"io/ioutil"
	"reflect"
	"strings"
	"testing"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	"happyfresh.io/search/core/synonym"
)

type esMock struct {
	searchSynonymFunc searchFunc
}

type searchFunc func(r *esapi.SearchRequest) (*esapi.Response, error)

func (es *esMock) Search(o ...func(*esapi.SearchRequest)) (*esapi.Response, error) {
	request := &esapi.SearchRequest{}
	for _, apply := range o {
		apply(request)
	}

	switch request.Index[0] {
	case "synonym":
		return es.searchSynonymFunc(request)
	}

	return nil, errors.New("[elasticsearch-test] unrecognized index")
}

func defaultMock() *esMock {
	searchSynonymResult := `
	{
		"hits" : {
		  "hits" : [
			{
			  "_source" : {
				"id" : "6081362f0c9b3fd47bcabc24",
				"created_at" : "2021-04-22T08:39:11Z",
				"updated_at" : "2021-05-18T01:58:55Z",
				"engine_id" : "5e5c76b937342055495d78d8",
				"synonyms" : [
				  "jahe",
				  "rempah",
				  "wedang"
				]
			  }
			}
		  ]
		}
	  }
	`

	toESResponse := func(s string) *esapi.Response {
		return &esapi.Response{
			StatusCode: 200,
			Header: map[string][]string{
				"Content-Type": {"application/json"},
			},
			Body: ioutil.NopCloser(strings.NewReader(s)),
		}
	}

	return &esMock{
		searchSynonymFunc: func(r *esapi.SearchRequest) (*esapi.Response, error) { return toESResponse(searchSynonymResult), nil },
	}
}

func TestSearch(t *testing.T) {
	query, err := FSString(false, "/resources/elasticsearch/synonym/synonym")
	if err != nil {
		t.Error(err)
	}

	SynonymQuery = query

	testCases := []struct {
		name              string
		searchSynonymFunc searchFunc
		expectedResult    *synonym.Synonym
	}{
		{
			"Find and return synonym",
			nil,
			&synonym.Synonym{
				Terms: []string{"jahe", "rempah", "wedang"},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			mock := defaultMock()

			if testCase.searchSynonymFunc != nil {
				mock.searchSynonymFunc = testCase.searchSynonymFunc
			}

			c := &client{
				&elasticsearch.Client{
					API: &esapi.API{
						Search: mock.Search,
					},
				},
				"synonym",
			}

			ctx := context.Background()
			result, err := c.Get(ctx, "jahe")
			if err != nil {
				t.Error(err)
			}

			if !reflect.DeepEqual(result, testCase.expectedResult) {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.expectedResult, result)
			}
		})
	}
}
