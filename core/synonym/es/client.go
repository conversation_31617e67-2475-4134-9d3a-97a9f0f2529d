package es

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	"github.com/pkg/errors"
	"happyfresh.io/search/core/index/es"
	"happyfresh.io/search/core/synonym"

	ddhttp "gopkg.in/DataDog/dd-trace-go.v1/contrib/net/http"
)

type client struct {
	client *elasticsearch.Client
	index  string
}

func (c *client) Get(ctx context.Context, query string) (*synonym.Synonym, error) {
	buf := &bytes.Buffer{}

	if err := json.Compact(buf, []byte(fmt.Sprintf(SynonymQuery, query))); err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] failed to encode request")
	}

	response, err := c.client.Search(
		c.client.Search.WithContext(ctx),
		c.client.Search.WithIndex(c.index),
		c.client.Search.WithBody(buf),
		c.client.Search.WithHeader(map[string]string{"Accept-Encoding": "gzip"}),
	)
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] error on search")
	}

	b, err := c.readAll(response)
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] error decoding response")
	}

	if response.StatusCode >= http.StatusBadRequest || response.IsError() {
		errB := &es.ESError{}
		err = json.Unmarshal(b, errB)
		if err != nil {
			return nil, errors.Wrap(err, "[elasticsearch] error decoding response")
		}

		return nil, errors.Errorf("[elasticsearch] error: status: %v; message: %v", response.Status(), errB.Error.Reason)
	}

	rsp := &synonymResponse{}
	if err := json.Unmarshal(b, rsp); err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] error decoding response")
	}

	s := &synonym.Synonym{}
	if len(rsp.Hits.Hits) > 0 {
		synonyms := []string{}
		for _, synonym := range rsp.Hits.Hits[0].Source.Synonyms {
			synonyms = append(synonyms, synonym)
		}

		s.Terms = synonyms
	}

	return s, nil
}

func (c *client) readAll(r *esapi.Response) (res []byte, err error) {
	body := r.Body
	if r.Header.Get("content-encoding") == "gzip" {
		body, err = gzip.NewReader(r.Body)
		if err != nil {
			return nil, err
		}
	}
	defer body.Close()

	return ioutil.ReadAll(body)
}

func NewClient(esURL string) (synonym.Adapter, error) {
	u, err := url.Parse(esURL)
	if err != nil {
		return nil, errors.Wrap(err, "[elasticsearch] invalid url")
	}

	pw, _ := u.User.Password()
	config := elasticsearch.Config{
		Addresses: []string{fmt.Sprintf("%s://%s", u.Scheme, u.Host)},
		Username:  u.User.Username(),
		Password:  pw,
		Transport: ddhttp.WrapRoundTripper(http.DefaultTransport),
	}

	es, err := elasticsearch.NewClient(config)
	if err != nil {
		return nil, err
	}

	paths := strings.Split(u.Path[1:], "/")
	if len(paths) < 1 {
		return nil, errors.Wrap(err, "[elasticsearch] invalid index")
	}
	indexName := paths[0]

	return &client{
		client: es,
		index:  indexName,
	}, nil
}
