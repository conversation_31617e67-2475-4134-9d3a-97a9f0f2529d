package es

import (
	"strings"
	"sync"

	"github.com/pkg/errors"
	"happyfresh.io/search/core/synonym"
)

type clients struct {
	clients map[string]synonym.Adapter
	enable  map[string]bool
	m       *sync.RWMutex
}

func (cs *clients) Get(key string) (synonym.Adapter, error) {
	key = strings.ToLower(key)

	cs.m.Lock()
	enabled := cs.enable[key]
	client, ok := cs.clients[key]
	cs.m.Unlock()
	if !enabled {
		return nil, errors.New("[elasticsearch] synonym shard disabled")
	}
	if !ok {
		return nil, errors.New("[elasticsearch] synonym shard not found")
	}

	return client, nil
}

type Opts func(cs *clients)

func Enable(enable map[string]bool) Opts {
	return func(cs *clients) {
		cs.enable = enable
	}
}

func NewClients(urls map[string]string, opts ...Opts) (synonym.Adapters, error) {
	cs := &clients{
		clients: make(map[string]synonym.Adapter),
		m:       &sync.RWMutex{},
	}

	for _, apply := range opts {
		apply(cs)
	}

	for k, v := range urls {
		client, err := NewClient(v)
		if err != nil {
			return nil, err
		}

		cs.clients[k] = client
	}

	return cs, nil
}
