package synonym

import (
	"context"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	orionClient "happyfresh.io/orion/lib/rpc/client"
)

//go:generate protoc --gofast_out=plugins=grpc:. synonym.proto

var std *srv

type srv struct {
	c                  *orionClient.Orion
	productBoostsCount int
}

func Get(ctx context.Context, q string) (*Synonym, error) {
	return std.Get(ctx, q)
}

func (s *srv) Get(ctx context.Context, query string) (*Synonym, error) {
	query = strings.ToLower(strings.Join(strings.Fields(query), " "))

	countryIso, err := FromContext(ctx, "country_iso")
	if err != nil {
		return nil, errors.Wrap(err, "[synonym] Error getting country code iso")
	}

	userID, err := FromContext(ctx, "user_id")
	if err != nil {
		return nil, errors.Wrap(err, "[synonym] Error getting user ID")
	}

	syno, err := s.c.GetSynonym(ctx, countryIso, query, userID, "A")
	if err != nil {
		return nil, errors.Wrapf(err, "[synonym] Error getting rec for query: %s", query)
	}

	productTypes := make(map[string]float64)
	leafIDs := make([]string, len(syno.Response.ProductTypes))
	for i, pt := range syno.Response.ProductTypes {
		leafIDs[i] = strconv.FormatInt(pt.ProductType, 10)
		for _, id := range pt.Ids {
			productTypeID := strconv.FormatInt(id, 10)
			boosting := productTypes[productTypeID]
			boosting += pt.Fraction

			productTypes[productTypeID] = boosting
		}
	}

	if len(syno.Response.ProductBoosts) >= s.productBoostsCount {
		syno.Response.ProductBoosts = syno.Response.ProductBoosts[:s.productBoostsCount]
	}

	productBoosts := make(map[string]float64)
	for _, pb := range syno.Response.ProductBoosts {
		productBoosts[pb.Sku] = pb.Fraction
	}

	if syno.Response.Result == "" {
		return &Synonym{
			Result:             query,
			ProductTypes:       productTypes,
			ValidSynonym:       syno.Response.GetValidSynonym(),
			StaticSynonym:      syno.Response.GetStaticSynonym(),
			ProductBoosts:      productBoosts,
			QueryTransformed:   syno.Response.GetQueryTransformed(),
			Terms:              []string{},
			ProductTypeLeafIds: leafIDs,
		}, nil
	}

	return &Synonym{
		Result:             syno.Response.GetResult(),
		ProductTypes:       productTypes,
		ValidSynonym:       syno.Response.GetValidSynonym(),
		StaticSynonym:      syno.Response.GetStaticSynonym(),
		ProductBoosts:      productBoosts,
		QueryTransformed:   syno.Response.GetQueryTransformed(),
		Terms:              syno.Response.GetTerms(),
		ProductTypeLeafIds: leafIDs,
	}, nil
}

type Option func(srv *srv)

func Init(opts ...Option) {
	std = &srv{
		productBoostsCount: 3,
	}

	for _, apply := range opts {
		apply(std)
	}
}

func WithOrionClient(c *orionClient.Orion) Option {
	return func(s *srv) {
		s.c = c
	}
}

func WithProductBoostCount(n int) Option {
	return func(s *srv) {
		s.productBoostsCount = n
	}
}
