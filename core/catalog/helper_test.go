package catalog_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/search/core/catalog"
)

func TestUnitPriceFactor(t *testing.T) {

	tableTest := []struct {
		name     string
		product  *cat.ProductDetail
		expected float64
	}{
		{
			name: "Test sell natural true",
			product: &cat.ProductDetail{
				Properties: &cat.Properties{
					AverageWeight: float64(0.8),
					UnitPieces:    int64(2),
					SellNatural:   true,
					Size_:         float64(10),
				},
			},
			expected: float64(1),
		},
		{
			name: "Test sell natural false",
			product: &cat.ProductDetail{
				Properties: &cat.Properties{
					AverageWeight: float64(0.0),
					UnitPieces:    int64(2),
					SellNatural:   false,
					Size_:         float64(10),
				},
			},
			expected: float64(1),
		},
		{
			name: "Test supermarket unit kg",
			product: &cat.ProductDetail{
				Properties: &cat.Properties{
					AverageWeight:   float64(0.0),
					UnitPieces:      int64(2),
					SellNatural:     false,
					Size_:           float64(10),
					SupermarketUnit: "kg",
				},
			},
			expected: 0.005,
		},
		{
			name: "Test supermarket unit ml",
			product: &cat.ProductDetail{
				Properties: &cat.Properties{
					AverageWeight:   float64(0.0),
					UnitPieces:      int64(2),
					SellNatural:     false,
					Size_:           float64(10),
					SupermarketUnit: "ml",
				},
			},
			expected: 5,
		},
	}

	for _, row := range tableTest {
		t.Run(row.name, func(t *testing.T) {
			result := catalog.UnitPriceFactor(row.product)
			assert.Equal(t, row.expected, result)
		})
	}

}
