package catalog_test

import (
	"context"
	"os"
	"testing"

	"github.com/glycerine/goconvey/convey"
	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/search/core/catalog"
	hfgrpc "happyfresh.io/search/lib/grpc"
)

func TestGetProductDetail(t *testing.T) {
	tt := []struct {
		name                 string
		stockLocationIDInput int64
		productIDInput       int64
		channelInput         string
		localeInput          string
		f                    func(ctx context.Context, key string, target proto.Message) error
		result               *cat.ProductDetail
	}{
		{
			"Returns product detail",
			3,
			1,
			"",
			"",
			func(ctx context.Context, key string, target proto.Message) error {
				rr := target.(*cat.ProductDetail)
				rr.ProductId = 1

				return nil
			},
			&cat.ProductDetail{
				ProductId: 1,
			},
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithProductDetailCache(&cacheMock{
				GetFunc: tc.f,
			}), catalog.WithgRPCClient(&catalog.ClientMock{}))
			if err != nil {
				t.Fatal(err)
			}

			res, err := cc.GetProductDetail(ctx, tc.stockLocationIDInput, tc.productIDInput, tc.channelInput, tc.localeInput)
			if err != nil {
				t.Error(err)
			}

			assert := convey.ShouldResemble(res.ProductId, tc.result.ProductId)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestProductDetailRoundTripper(t *testing.T) {
	tt := []struct {
		name      string
		f         func(context.Context, *cat.ProductDetailRequest, ...grpc.CallOption) (*cat.ProductDetail, error)
		input     []interface{}
		result    *cat.ProductDetail
		isErrgRPC bool
	}{
		{
			"Returns product detail",
			func(ctx context.Context, in *cat.ProductDetailRequest, opts ...grpc.CallOption) (*cat.ProductDetail, error) {
				return &cat.ProductDetail{
					ProductId: 999,
				}, nil
			},
			[]interface{}{
				1, 1, "", "",
			},
			&cat.ProductDetail{
				ProductId: 999,
			},
			false,
		},
		{
			"Returns gRPC Error",
			func(ctx context.Context, in *cat.ProductDetailRequest, opts ...grpc.CallOption) (*cat.ProductDetail, error) {
				return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
			},
			[]interface{}{
				-1, -1, "", "",
			},
			nil,
			true,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
				GetProductDetailFunc: tc.f,
			}))
			if err != nil {
				t.Fatal(err)
			}

			catalogClient, _ := cc.(*catalog.ClientImpl)
			res, err := catalogClient.ProductDetailRoundTripper().RoundTrip(ctx, "", tc.input...)
			if err != nil {
				_, ok := status.FromError(errors.Cause(err))
				assert := convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
				}

				return
			}

			result := res.(*cat.ProductDetail)
			assert := convey.ShouldResemble(result, tc.result)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestStoreProductsPLP(t *testing.T) {
	tt := []struct {
		name                 string
		idsInput             []int64
		stockLocationIDInput int64
		localeInput          string
		f                    func(ctx context.Context, key []string, targets []proto.Message) error
		expect               []*cat.StoreProducts
		expectErr            bool
	}{
		{
			"Returns store products",
			[]int64{999, 998},
			3,
			"id",
			func(ctx context.Context, key []string, targets []proto.Message) error {
				news := []*cat.StoreProducts{
					{
						ProductDetail: &cat.ProductDetail{
							ProductId: 999,
						},
						StoreProducts: []*cat.StoreProduct{
							{
								StoreProductId: 9991,
							},
						},
					},
					{
						ProductDetail: &cat.ProductDetail{
							ProductId: 998,
						},
						StoreProducts: []*cat.StoreProduct{
							{
								StoreProductId: 9981,
							},
						},
					},
				}

				for i, target := range targets {
					sp := target.(*cat.StoreProducts)
					sp.ProductDetail = news[i].ProductDetail
					sp.StoreProducts = news[i].StoreProducts
				}

				return nil
			},
			[]*cat.StoreProducts{
				{
					ProductDetail: &cat.ProductDetail{
						ProductId: 999,
					},
					StoreProducts: []*cat.StoreProduct{
						{
							StoreProductId: 9991,
						},
					},
				},
				{
					ProductDetail: &cat.ProductDetail{
						ProductId: 998,
					},
					StoreProducts: []*cat.StoreProduct{
						{
							StoreProductId: 9981,
						},
					},
				},
			},
			false,
		},
		{
			"Returns err",
			[]int64{},
			-1,
			"id",
			func(ctx context.Context, key []string, targets []proto.Message) error {
				return errors.New("Mocked Error")
			},
			nil,
			true,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()

			cc, err := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithStoreProductCache(&cacheMock{
				GetManyFunc: tc.f,
			}))
			if err != nil {
				t.Error(err)
			}

			result, err := cc.GetStoreProductsPLP(ctx, tc.idsInput, tc.stockLocationIDInput, tc.localeInput)
			if err != nil && convey.ShouldBeTrue(tc.expectErr) != "" {
				t.Error(err)
			}

			if err == nil && convey.ShouldBeTrue(tc.expectErr) == "" {
				t.Error(err)
			}

			if tc.expectErr {
				return
			}

			for i, r := range result {
				assert := convey.ShouldResemble(r.ProductDetail.ProductId, tc.expect[i].ProductDetail.ProductId)
				if assert != "" {
					t.Error(assert)
				}

				assert = convey.ShouldResemble(r.StoreProducts[0].StoreProductId, tc.expect[i].StoreProducts[0].StoreProductId)
				if assert != "" {
					t.Error(assert)
				}
			}
		})
	}
}

func TestStoreProductRoundTripper(t *testing.T) {
	tt := []struct {
		name      string
		f         func(ctx context.Context, r *cat.StoreProductRequest, o ...grpc.CallOption) (*cat.StoreProducts, error)
		input     []interface{}
		expect    *cat.StoreProducts
		isErrGRPC bool
	}{
		{
			"Returns store product",
			func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
				return &cat.StoreProducts{
					ProductDetail: &cat.ProductDetail{
						ProductId: 999,
					},
					StoreProducts: []*cat.StoreProduct{
						{
							StoreProductId: 9991,
						},
					},
				}, nil
			},
			[]interface{}{
				999, 3, "id",
			},
			&cat.StoreProducts{
				ProductDetail: &cat.ProductDetail{
					ProductId: 999,
				},
				StoreProducts: []*cat.StoreProduct{
					{
						StoreProductId: 9991,
					},
				},
			},
			false,
		},
		{
			"Returns gRPC Error",
			func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
				return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
			},
			[]interface{}{
				-1, -1, "",
			},
			&cat.StoreProducts{},
			true,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
				GetStoreProductsByStockLocationIDAndVariantIDFunc: tc.f,
			}))
			if err != nil {
				t.Error(err)
			}

			catalogClient, _ := cc.(*catalog.ClientImpl)
			res, err := catalogClient.StoreProductByIDRoundTripper().RoundTrip(ctx, "", tc.input...)
			if err != nil {
				_, ok := status.FromError(errors.Cause(err))
				assert := convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
				}

				return
			}

			result := res.(*cat.StoreProducts)
			assert := convey.ShouldResemble(result, tc.expect)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestStoreProductsByProductID(t *testing.T) {
	tt := []struct {
		name              string
		productID         int64
		locale            string
		f                 func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error)
		expectedIsError   bool
		expectedgRPCError bool
	}{
		{
			"Returns error if product ID is nil",
			0,
			"id",
			nil,
			true,
			false,
		},
		{
			"Returns error if locale is nil",
			1,
			"",
			nil,
			true,
			false,
		},
		{
			"Returns any result from grpc client - error",
			1,
			"ID",
			func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
				return nil, status.Error(codes.PermissionDenied, "")
			},
			true,
			true,
		},
		{
			"Returns any result from grpc client - not error",
			1,
			"ID",
			func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
				return &cat.StoreProducts{}, nil
			},
			false,
			false,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
				GetStoreProductsByProductIDFunc: tc.f,
			}))
			if err != nil {
				t.Error(err)
			}

			_, err = cc.GetStoreProductsByProductID(ctx, tc.productID, tc.locale)
			if err != nil {
				assert := convey.ShouldEqual(err != nil, tc.expectedIsError)
				if assert != "" {
					t.Error(assert)
					return
				}

				if !tc.expectedgRPCError {
					return
				}

				status, ok := status.FromError(errors.Cause(err))
				assert = convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
					return
				}

				assert = convey.ShouldBeTrue(status.Code() != codes.Unknown)
				if assert != "" {
					t.Error(assert)
					return
				}
			}
		})
	}
}

func TestStoreProductsByID(t *testing.T) {
	tt := []struct {
		name              string
		storeProductID    int64
		locale            string
		f                 func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error)
		expectedIsError   bool
		expectedgRPCError bool
	}{
		{
			"Returns error if store product ID is nil",
			0,
			"id",
			nil,
			true,
			false,
		},
		{
			"Returns error if locale is nil",
			1,
			"",
			nil,
			true,
			false,
		},
		{
			"Returns any result from grpc client - error",
			1,
			"ID",
			func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
				return nil, status.Error(codes.PermissionDenied, "")
			},
			true,
			true,
		},
		{
			"Returns any result from grpc client - not error",
			1,
			"ID",
			func(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
				return &cat.StoreProducts{}, nil
			},
			false,
			false,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
				GetStoreProductsByIDFunc: tc.f,
			}))
			if err != nil {
				t.Error(err)
			}

			_, err = cc.GetStoreProductsByID(ctx, tc.storeProductID, tc.locale)
			if err != nil {
				assert := convey.ShouldEqual(err != nil, tc.expectedIsError)
				if assert != "" {
					t.Error(assert)
					return
				}

				if !tc.expectedgRPCError {
					return
				}

				status, ok := status.FromError(errors.Cause(err))
				assert = convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
					return
				}

				assert = convey.ShouldBeTrue(status.Code() != codes.Unknown)
				if assert != "" {
					t.Error(assert)
					return
				}
			}
		})
	}
}

func TestProductsByVariantIDs(t *testing.T) {
	tt := []struct {
		name            string
		f               func(ctx context.Context, in *cat.SearchProductsRequest, opts ...grpc.CallOption) (*cat.Products, error)
		expectedIsError bool
	}{
		{
			"Returns grpc error code",
			func(ctx context.Context, in *cat.SearchProductsRequest, opts ...grpc.CallOption) (*cat.Products, error) {
				return nil, status.Error(codes.PermissionDenied, "")
			},
			true,
		},
		{
			"Returns no error",
			func(ctx context.Context, in *cat.SearchProductsRequest, opts ...grpc.CallOption) (*cat.Products, error) {
				return &cat.Products{}, nil
			},
			false,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
				SearchProductsFunc: tc.f,
			}))
			if err != nil {
				t.Error(err)
			}

			_, err = cc.GetProductsByVariantIDs(ctx, []int64{1, 2, 3}, []int64{1, 2, 3}, 1, "")
			if err != nil {
				assert := convey.ShouldEqual(err != nil, tc.expectedIsError)
				if assert != "" {
					t.Error(assert)
					return
				}

				status, ok := status.FromError(errors.Cause(err))
				assert = convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
					return
				}

				assert = convey.ShouldBeTrue(status.Code() != codes.Unknown)
				if assert != "" {
					t.Error(assert)
					return
				}
			}
		})
	}
}

func TestGetTaxonomy(t *testing.T) {
	tcs := []struct {
		name                 string
		taxonomyId           int64
		stockLocationIDInput int64
		locale               string
		country              string
		f                    func(ctx context.Context, key string, target proto.Message) error
		expect               *cat.Taxonomy
		expectErr            bool
	}{
		{
			"Returns taxonomy",
			1,
			3,
			"en",
			"ID",
			func(ctx context.Context, key string, target proto.Message) error {
				r := target.(*cat.Taxonomy)
				r.Taxons = []*cat.Taxon{{TaxonId: 1}}

				return nil
			},
			&cat.Taxonomy{
				Taxons: []*cat.Taxon{{TaxonId: 1}},
			},
			false,
		},
		{
			"Returns err",
			-1,
			-3,
			"id",
			"ID",
			func(ctx context.Context, key string, target proto.Message) error {
				return errors.New("Mocked Error")
			},
			nil,
			true,
		},
	}

	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			catalogClient, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithTaxonomyCache(&cacheMock{GetFunc: tc.f}),
				catalog.WithgRPCClient(&catalog.ClientMock{}),
			)

			if err != nil {
				t.Error(err)
			}

			resp, err := catalogClient.GetTaxonomy(ctx, tc.taxonomyId, tc.stockLocationIDInput, tc.locale, tc.country)
			if err != nil && convey.ShouldBeTrue(tc.expectErr) != "" {
				t.Error(err)
			}

			if err == nil && convey.ShouldBeFalse(tc.expectErr) != "" {
				t.Error(err)
			}

			if tc.expectErr {
				return
			}

			assert := convey.ShouldResemble(resp.Taxons, tc.expect.Taxons)
			if assert != "" {
				t.Error(assert)
			}

		})
	}

}

func TestTaxonomyRoundTripper(t *testing.T) {
	tcs := []struct {
		name   string
		f      func(context.Context, *cat.TaxonomyRequest, ...grpc.CallOption) (*cat.Taxonomy, error)
		input  []interface{}
		expect *cat.Taxonomy
	}{
		{
			"Returns taxonomy",
			func(ctx context.Context, in *cat.TaxonomyRequest, opts ...grpc.CallOption) (*cat.Taxonomy, error) {
				return &cat.Taxonomy{
					Taxons: []*cat.Taxon{{TaxonId: 1}},
				}, nil
			},
			[]interface{}{1, 3, "", ""},
			&cat.Taxonomy{Taxons: []*cat.Taxon{{TaxonId: 1}}},
		},
		{
			"Return gRPC error",
			func(ctx context.Context, in *cat.TaxonomyRequest, opts ...grpc.CallOption) (*cat.Taxonomy, error) {
				return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
			},
			[]interface{}{-1, 3, "", ""},
			nil,
		},
	}

	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithgRPCClient(&catalog.ClientMock{GetTaxonomyFunc: tc.f}),
			)

			if err != nil {
				t.Error(err)
			}

			catalogClient, _ := cc.(*catalog.ClientImpl)
			resp, err := catalogClient.TaxonomyRoundTripper().RoundTrip(ctx, "", tc.input...)
			if err != nil {
				_, ok := status.FromError(errors.Cause(err))
				assert := convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
				}
				return
			}

			result := resp.(*cat.Taxonomy)
			assert := convey.ShouldResemble(result, tc.expect)
			if assert != "" {
				t.Error(assert)
			}

		})
	}

}

func TestGetProductTypesByProductTypeID(t *testing.T) {

	assert := assert.New(t)

	cc, _ := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
		GetProductTypesByProductTypeIDFunc: func(c context.Context, ptr *cat.ProductTypeRequest, co ...grpc.CallOption) (*cat.ProductTypes, error) {
			productType := &cat.ProductTypes{
				ProductTypes: []*cat.ProductType{
					{
						Id: int64(1),
					},
				},
			}
			return productType, nil
		},
	}))

	productTypes, _ := cc.GetProductTypesByProductTypeID(context.Background(), int64(1))

	assert.NotEmpty(productTypes.ProductTypes)
	assert.Equal(int64(1), productTypes.ProductTypes[0].Id)

	cc, _ = catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
		GetProductTypesByProductTypeIDFunc: func(c context.Context, ptr *cat.ProductTypeRequest, co ...grpc.CallOption) (*cat.ProductTypes, error) {
			return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
		},
	}))

	_, err := cc.GetProductTypesByProductTypeID(context.Background(), int64(1))

	assert.Error(err)

}

func TestTrackPopularity(t *testing.T) {
	assert := assert.New(t)

	testTable := []struct {
		name        string
		mockedFn    func(c context.Context, tpr *cat.TrackPopularityRequest, co ...grpc.CallOption) (*cat.OkResponse, error)
		paramAction string
	}{
		{
			name: "Test action click",
			mockedFn: func(c context.Context, tpr *cat.TrackPopularityRequest, co ...grpc.CallOption) (*cat.OkResponse, error) {
				assert.Equal(cat.TrackPopularityRequest_CLICKED, tpr.Action)
				return &cat.OkResponse{Ok: true}, nil
			},
			paramAction: "click",
		},
		{
			name: "Test action atc",
			mockedFn: func(c context.Context, tpr *cat.TrackPopularityRequest, co ...grpc.CallOption) (*cat.OkResponse, error) {
				assert.Equal(cat.TrackPopularityRequest_ADDED_TO_CART, tpr.Action)
				return &cat.OkResponse{Ok: true}, nil
			},
			paramAction: "atc",
		},
		{
			name: "Test action purchased",
			mockedFn: func(c context.Context, tpr *cat.TrackPopularityRequest, co ...grpc.CallOption) (*cat.OkResponse, error) {
				assert.Equal(cat.TrackPopularityRequest_PURCHASED, tpr.Action)
				return &cat.OkResponse{Ok: true}, nil
			},
			paramAction: "purchased",
		},
		{
			name: "Test action unknown",
			mockedFn: func(c context.Context, tpr *cat.TrackPopularityRequest, co ...grpc.CallOption) (*cat.OkResponse, error) {
				assert.Equal(cat.TrackPopularityRequest_UNKNOWN, tpr.Action)
				return &cat.OkResponse{Ok: true}, nil
			},
			paramAction: "unknown",
		},
		{
			name: "Test error raised",
			mockedFn: func(c context.Context, tpr *cat.TrackPopularityRequest, co ...grpc.CallOption) (*cat.OkResponse, error) {
				return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
			},
			paramAction: "unknown",
		},
	}

	for _, row := range testTable {
		t.Run(row.name, func(t *testing.T) {
			cc, _ := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
				TrackPopularityFunc: row.mockedFn,
			}))
			err := cc.TrackPopularity(context.Background(), []int64{1, 2}, int64(1), row.paramAction)

			if err != nil {
				assert.Error(err)
			} else {
				assert.Nil(err)
			}

		})
	}

}

func TestGetBrands(t *testing.T) {

	cc, _ := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"),
		catalog.WithBrandCache(&cacheMock{
			GetManyFunc: func(ctx context.Context, key []string, targets []proto.Message) error {
				for i, target := range targets {
					sp := target.(*cat.Brand)
					sp.BrandId = int64(i) + 1
				}
				return nil
			},
		}),
	)

	brands, _ := cc.GetBrands(context.Background(), []int64{1, 2}, "id")
	assert.Equal(t, int64(1), brands[0].BrandId)

	cc, _ = catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"),
		catalog.WithBrandCache(&cacheMock{
			GetManyFunc: func(ctx context.Context, key []string, targets []proto.Message) error {
				return errors.New("Mocked Error")
			},
		}),
	)

	brands, err := cc.GetBrands(context.Background(), []int64{1, 2}, "id")
	assert.Error(t, err)
	assert.Nil(t, brands)

}

func TestBrandRoundTripper(t *testing.T) {

	cc, _ := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
		GetBrandFunc: func(c context.Context, br *cat.BrandRequest, co ...grpc.CallOption) (*cat.Brand, error) {
			return &cat.Brand{
				BrandId:   br.BrandId,
				NameLocal: br.Locale,
			}, nil
		},
	}))

	input := []interface{}{1, "id"}
	catalogClient, _ := cc.(*catalog.ClientImpl)
	response, _ := catalogClient.BrandRoundTripper().RoundTrip(context.Background(), "", input...)

	result := response.(*cat.Brand)
	assert.Equal(t, int64(1), result.BrandId)

	cc, _ = catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(&catalog.ClientMock{
		GetBrandFunc: func(c context.Context, br *cat.BrandRequest, co ...grpc.CallOption) (*cat.Brand, error) {
			return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
		},
	}))

	catalogClient, _ = cc.(*catalog.ClientImpl)
	_, err := catalogClient.BrandRoundTripper().RoundTrip(context.Background(), "", input...)

	assert.Error(t, err)

}

type cacheMock struct {
	GetFunc     func(ctx context.Context, key string, target proto.Message) error
	GetManyFunc func(ctx context.Context, key []string, targets []proto.Message) error
}

func (c *cacheMock) Get(ctx context.Context, key string, target proto.Message) error {
	if c.GetFunc != nil {
		return c.GetFunc(ctx, key, target)
	}

	return nil
}

func (c *cacheMock) GetMany(ctx context.Context, key []string, targets []proto.Message) error {
	if c.GetManyFunc != nil {
		return c.GetManyFunc(ctx, key, targets)
	}

	return nil
}

func TestGetTaxonIDs(t *testing.T) {
	cc, _ := catalog.New("dns:///localhost:443", os.Getenv("SSRV_REDIS_TEST_DSN"), catalog.WithgRPCClient(cat.NewApiClient(nil)))
	taxons := []*cat.Taxon{
		{
			TaxonId: 10,
		},
		{
			TaxonId: 12,
			Taxons: []*cat.Taxon{
				{
					TaxonId: 13,
				},
				{
					TaxonId: 14,
				},
				{
					TaxonId: 15,
				},
			},
		},
		{
			TaxonId: 30,
		},
	}
	taxonIDs := []int64{10, 12, 13, 14, 15, 30}

	assert.Equal(t, taxonIDs, cc.GetTaxonIDs(taxons))
}

func TestGetStoreTaxons(t *testing.T) {
	tt := []struct {
		name            string
		taxonIDs        []int64
		stockLocationID int64
		locale          string
		country         string
		f               func(context.Context, []string, []proto.Message) error
		expect          []*cat.StoreTaxon
		expectErr       bool
	}{
		{
			"Returns Store Taxons",
			[]int64{1, 2, 3},
			2,
			"en",
			"ID",
			func(ctx context.Context, keys []string, targets []proto.Message) error {
				storeTaxons := []*cat.StoreTaxon{
					{
						TaxonId: 1,
						Name:    "taxon-1",
						DisplayImage: &cat.Image{
							Id:         11,
							MiniUrl:    "mini-url-11",
							ProductUrl: "product-url-11",
						},
						Permalink: "permalink-1",
					},
					{
						TaxonId: 2,
						Name:    "taxon-2",
						DisplayImage: &cat.Image{
							Id:         21,
							MiniUrl:    "mini-url-21",
							ProductUrl: "product-url-21",
						},
						Permalink: "permalink-2",
					},
					{
						TaxonId:      3,
						Name:         "taxon-3",
						DisplayImage: nil,
						Permalink:    "permalink-3",
					},
				}

				for i, target := range targets {
					st := target.(*cat.StoreTaxon)
					st.TaxonId = storeTaxons[i].TaxonId
					st.Name = storeTaxons[i].Name
					st.Permalink = storeTaxons[i].Permalink
				}

				return nil
			},
			[]*cat.StoreTaxon{
				{
					TaxonId: 1,
					Name:    "taxon-1",
					DisplayImage: &cat.Image{
						Id:         11,
						MiniUrl:    "mini-url-11",
						ProductUrl: "product-url-11",
					},
					Permalink: "permalink-1",
				},
				{
					TaxonId: 2,
					Name:    "taxon-2",
					DisplayImage: &cat.Image{
						Id:         21,
						MiniUrl:    "mini-url-21",
						ProductUrl: "product-url-21",
					},
					Permalink: "permalink-2",
				},
				{
					TaxonId:      3,
					Name:         "taxon-3",
					DisplayImage: nil,
					Permalink:    "permalink-3",
				},
			},
			false,
		},
		{
			"Returns err",
			[]int64{1, 2, 3},
			2,
			"en",
			"ID",
			func(ctx context.Context, keys []string, targets []proto.Message) error {
				return errors.New("Mocked Error")
			},
			nil,
			true,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()

			catalogClient, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithStoreTaxonsCache(&cacheMock{GetManyFunc: tc.f}),
				catalog.WithgRPCClient(&catalog.ClientMock{}),
			)

			if err != nil {
				t.Error(err)
			}

			resp, err := catalogClient.GetStoreTaxons(ctx, tc.taxonIDs, tc.stockLocationID, tc.locale, tc.country)
			if err != nil && convey.ShouldBeTrue(tc.expectErr) != "" {
				t.Error(err)
			}

			if err == nil && convey.ShouldBeFalse(tc.expectErr) != "" {
				t.Error(err)
			}

			if tc.expectErr {
				return
			}

			for i, r := range resp {
				assert := convey.ShouldResemble(r.TaxonId, tc.expect[i].TaxonId)
				if assert != "" {
					t.Error(assert)
				}

				assert = convey.ShouldResemble(r.Name, tc.expect[i].Name)
				if assert != "" {
					t.Error(assert)
				}

				assert = convey.ShouldResemble(r.Permalink, tc.expect[i].Permalink)
				if assert != "" {
					t.Error(assert)
				}

				if r.DisplayImage != nil && tc.expect[i] != nil {

					assert = convey.ShouldResemble(r.DisplayImage.Id, tc.expect[i].DisplayImage.Id)
					if assert != "" {
						t.Error(assert)
					}
					assert = convey.ShouldResemble(r.DisplayImage.MiniUrl, tc.expect[i].DisplayImage.MiniUrl)
					if assert != "" {
						t.Error(assert)
					}
					assert = convey.ShouldResemble(r.DisplayImage.ProductUrl, tc.expect[i].DisplayImage.ProductUrl)
					if assert != "" {
						t.Error(assert)
					}
				}
			}
		})
	}
}

func TestStoreTaxonRoundTripper(t *testing.T) {
	tt := []struct {
		name   string
		f      func(context.Context, *cat.StoreTaxonRequest, ...grpc.CallOption) (*cat.StoreTaxon, error)
		input  []interface{}
		expect *cat.StoreTaxon
	}{
		{
			"Returns store taxon",
			func(ctx context.Context, in *cat.StoreTaxonRequest, opts ...grpc.CallOption) (*cat.StoreTaxon, error) {
				return &cat.StoreTaxon{}, nil
			},
			[]interface{}{277, 1, "en", "ID"},
			&cat.StoreTaxon{},
		},
		{
			"Return gRPC error",
			func(ctx context.Context, in *cat.StoreTaxonRequest, opts ...grpc.CallOption) (*cat.StoreTaxon, error) {
				return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
			},
			[]interface{}{277, 1, "en", "ID"},
			nil,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithgRPCClient(&catalog.ClientMock{GetStoreTaxonFunc: tc.f}),
			)
			if err != nil {
				t.Error(err)
			}

			catalogClient, _ := cc.(*catalog.ClientImpl)
			resp, err := catalogClient.StoreTaxonRoundTripper().RoundTrip(ctx, "", tc.input...)

			if err != nil {
				_, ok := status.FromError(errors.Cause(err))
				assert := convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
				}
				return
			}

			result := resp.(*cat.StoreTaxon)
			assert := convey.ShouldResemble(result, tc.expect)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestManualSKUBoostRoundTripper(t *testing.T) {
	tt := []struct {
		name   string
		f      func(context.Context, *cat.ManualSKUBoostRequest, ...grpc.CallOption) (*cat.ManualSKUBoosts, error)
		input  []interface{}
		expect *cat.ManualSKUBoosts
	}{
		{
			"Returns manual sku boost",
			func(ctx context.Context, in *cat.ManualSKUBoostRequest, opts ...grpc.CallOption) (*cat.ManualSKUBoosts, error) {
				return &cat.ManualSKUBoosts{ManualSkuBoosts: []*cat.ManualSKUBoost{}}, nil
			},
			[]interface{}{"PROMOCODE", 3},
			&cat.ManualSKUBoosts{ManualSkuBoosts: []*cat.ManualSKUBoost{}},
		},
		{
			"Rescue gRPC error and return empty manual sku boosts",
			func(ctx context.Context, in *cat.ManualSKUBoostRequest, opts ...grpc.CallOption) (*cat.ManualSKUBoosts, error) {
				return &cat.ManualSKUBoosts{ManualSkuBoosts: []*cat.ManualSKUBoost{}}, nil
			},
			[]interface{}{"PROMOCODE", 3},
			&cat.ManualSKUBoosts{ManualSkuBoosts: []*cat.ManualSKUBoost{}},
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithgRPCClient(&catalog.ClientMock{GetManualSKUBoostsByPromoCodeFunc: tc.f}),
			)
			if err != nil {
				t.Error(err)
			}

			catalogClient, _ := cc.(*catalog.ClientImpl)
			resp, err := catalogClient.ManualSKUBoostRoundTripper().RoundTrip(ctx, "", tc.input...)

			if err != nil {
				_, ok := status.FromError(errors.Cause(err))
				assert := convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
				}
				return
			}

			result := resp.(*cat.ManualSKUBoosts)
			assert := convey.ShouldResemble(result, tc.expect)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestGetThemes(t *testing.T) {
	tcs := []struct {
		name      string
		sli       int64
		locale    string
		f         func(ctx context.Context, key string, target proto.Message) error
		expect    *cat.Themes
		expectErr bool
	}{
		{
			"Returns themes",
			3,
			"en",
			func(ctx context.Context, key string, target proto.Message) error {
				r := target.(*cat.Themes)
				r.Themes = []*cat.Theme{{ThemeId: 1}}

				return nil
			},
			&cat.Themes{
				Themes: []*cat.Theme{{ThemeId: 1}},
			},
			false,
		},
		{
			"Returns err",
			-3,
			"en",
			func(ctx context.Context, key string, target proto.Message) error {
				return errors.New("Mocked Error")
			},
			nil,
			true,
		},
	}

	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			catalogClient, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithThemesCache(&cacheMock{GetFunc: tc.f}),
				catalog.WithgRPCClient(&catalog.ClientMock{}),
			)

			if err != nil {
				t.Error(err)
			}

			resp, err := catalogClient.GetThemes(ctx, tc.sli, tc.locale)
			if err != nil && convey.ShouldBeTrue(tc.expectErr) != "" {
				t.Error(err)
			}

			if err == nil && convey.ShouldBeFalse(tc.expectErr) != "" {
				t.Error(err)
			}

			if tc.expectErr {
				return
			}

			assert := convey.ShouldResemble(resp.Themes, tc.expect.Themes)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestGetVariantsByTheme(t *testing.T) {
	tcs := []struct {
		name      string
		themeId   int64
		f         func(ctx context.Context, key string, target proto.Message) error
		expect    *cat.ThemeVariants
		expectErr bool
	}{
		{
			"Returns variants",
			1,
			func(ctx context.Context, key string, target proto.Message) error {
				r := target.(*cat.ThemeVariants)
				r.VariantIds = []int64{1, 2, 3}

				return nil
			},
			&cat.ThemeVariants{
				VariantIds: []int64{1, 2, 3},
			},
			false,
		},
		{
			"Returns empty variants",
			1,
			func(ctx context.Context, key string, target proto.Message) error {
				return nil
			},
			&cat.ThemeVariants{},
			false,
		},
		{
			"Returns err",
			-1,
			func(ctx context.Context, key string, target proto.Message) error {
				return errors.New("Mocked Error")
			},
			nil,
			true,
		},
	}

	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			catalogClient, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithThemeVariantsCache(&cacheMock{GetFunc: tc.f}),
				catalog.WithgRPCClient(&catalog.ClientMock{}),
			)

			if err != nil {
				t.Error(err)
			}

			resp, err := catalogClient.GetVariantsByTheme(ctx, tc.themeId)
			if err != nil && convey.ShouldBeTrue(tc.expectErr) != "" {
				t.Error(err)
			}

			if err == nil && convey.ShouldBeFalse(tc.expectErr) != "" {
				t.Error(err)
			}

			if tc.expectErr {
				return
			}

			assert := convey.ShouldResemble(resp.VariantIds, tc.expect.VariantIds)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestThemesRoundTripper(t *testing.T) {
	tt := []struct {
		name   string
		f      func(context.Context, *cat.ThemeRequest, ...grpc.CallOption) (*cat.Themes, error)
		input  []interface{}
		expect *cat.Themes
	}{
		{
			"Returns themes",
			func(ctx context.Context, in *cat.ThemeRequest, opts ...grpc.CallOption) (*cat.Themes, error) {
				return &cat.Themes{Themes: []*cat.Theme{}}, nil
			},
			[]interface{}{3},
			&cat.Themes{Themes: []*cat.Theme{}},
		},
		{
			"Rescue gRPC error",
			func(ctx context.Context, in *cat.ThemeRequest, opts ...grpc.CallOption) (*cat.Themes, error) {
				return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
			},
			[]interface{}{3},
			nil,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithgRPCClient(&catalog.ClientMock{GetThemesFunc: tc.f}),
			)
			if err != nil {
				t.Error(err)
			}

			catalogClient, _ := cc.(*catalog.ClientImpl)
			resp, err := catalogClient.ThemesRoundTripper().RoundTrip(ctx, "", tc.input...)

			if err != nil {
				_, ok := status.FromError(errors.Cause(err))
				assert := convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
				}
				return
			}

			result := resp.(*cat.Themes)
			assert := convey.ShouldResemble(result, tc.expect)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestThemeVariantsRoundTripper(t *testing.T) {
	tt := []struct {
		name   string
		f      func(context.Context, *cat.ThemeRequest, ...grpc.CallOption) (*cat.ThemeVariants, error)
		input  []interface{}
		expect *cat.ThemeVariants
	}{
		{
			"Returns theme variants",
			func(ctx context.Context, in *cat.ThemeRequest, opts ...grpc.CallOption) (*cat.ThemeVariants, error) {
				return &cat.ThemeVariants{VariantIds: []int64{}}, nil
			},
			[]interface{}{1},
			&cat.ThemeVariants{VariantIds: []int64{}},
		},
		{
			"Rescue gRPC error",
			func(ctx context.Context, in *cat.ThemeRequest, opts ...grpc.CallOption) (*cat.ThemeVariants, error) {
				return nil, hfgrpc.NewStatusError(codes.Internal, errors.New("Mocked Error"))
			},
			[]interface{}{1},
			nil,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			cc, err := catalog.New(
				"dns:///localhost:443",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				catalog.WithgRPCClient(&catalog.ClientMock{GetVariantsByThemeFunc: tc.f}),
			)
			if err != nil {
				t.Error(err)
			}

			catalogClient, _ := cc.(*catalog.ClientImpl)
			resp, err := catalogClient.ThemeVariantsRoundTripper().RoundTrip(ctx, "", tc.input...)

			if err != nil {
				_, ok := status.FromError(errors.Cause(err))
				assert := convey.ShouldBeTrue(ok)
				if assert != "" {
					t.Error(assert)
				}
				return
			}

			result := resp.(*cat.ThemeVariants)
			assert := convey.ShouldResemble(result, tc.expect)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}

func TestAddTaxonFilterForSpecificClientType(t *testing.T) {
	testCases := []struct {
		name            string
		channel         string
		taxonNoneFilter []int64
		isSND           bool
		result          []int64
	}{
		{
			"user using android channel should add specific taxon",
			"android",
			[]int64{101, 102, 103, 104, 105, 106},
			false,
			[]int64{101, 102, 103, 104, 105, 106, 107, 108},
		},
		{
			"user using ios channel should not add specific taxon",
			"ios",
			[]int64{101, 102, 103, 104, 105, 106},
			false,
			[]int64{101, 102, 103, 104, 105, 106},
		},
		{
			"shopper using android channel should not add specific taxon",
			"android",
			[]int64{101, 102, 103, 104, 105, 106},
			true,
			[]int64{101, 102, 103, 104, 105, 106},
		},
		{
			"shopper using ios channel should not add specific taxon",
			"ios",
			[]int64{101, 102, 103, 104, 105, 106},
			true,
			[]int64{101, 102, 103, 104, 105, 106},
		},
	}

	assert := assert.New(t)

	androidFilteredTaxonIDs := []int64{107, 108}
	ctx := context.Background()
	for _, c := range testCases {
		t.Run(c.name, func(t *testing.T) {
			result := catalog.AddTaxonFilterForSpecificClientType(ctx, c.channel, c.isSND, c.taxonNoneFilter, androidFilteredTaxonIDs)
			assert.Equal(c.result, result)
		})
	}
}

func TestRemoveTaxonFilterForSpecificClientType(t *testing.T) {
	testCases := []struct {
		name            string
		channel         string
		taxonNoneFilter []int64
		isSND           bool
		result          []int64
	}{
		{
			"user using android channel should not remove specific taxon",
			"android",
			[]int64{101, 102, 103, 104, 105, 106, 107, 108},
			false,
			[]int64{101, 102, 103, 104, 105, 106, 107, 108},
		},
		{
			"user using ios channel should remove specific taxon",
			"ios",
			[]int64{101, 102, 103, 104, 105, 106, 107, 108},
			false,
			[]int64{101, 102, 103, 104, 105, 106},
		},
		{
			"shopper using android channel should remove specific taxon",
			"android",
			[]int64{101, 102, 103, 104, 105, 106, 107, 108},
			true,
			[]int64{101, 102, 103, 104, 105, 106},
		},
		{
			"shopper using ios channel should remove specific taxon",
			"ios",
			[]int64{101, 102, 103, 104, 105, 106, 107, 108},
			true,
			[]int64{101, 102, 103, 104, 105, 106},
		},
	}

	assert := assert.New(t)

	androidFilteredTaxonIDs := []int64{107, 108}
	ctx := context.Background()
	for _, c := range testCases {
		t.Run(c.name, func(t *testing.T) {
			result := catalog.RemoveTaxonFilterForSpecificClientType(ctx, c.channel, c.isSND, c.taxonNoneFilter, androidFilteredTaxonIDs)
			assert.Equal(c.result, result)
		})
	}
}
