package catalog

import (
	"strings"

	cat "happyfresh.io/catalog/lib/rpc/api"
)

func UnitPriceFactor(product *cat.ProductDetail) float64 {
	naturalAvgWeight := product.Properties.AverageWeight
	size := product.Properties.Size_
	unitPieces := float64(product.Properties.UnitPieces)

	factor := 1.0

	if unitPieces > 0 {
		factor = factor / unitPieces
	}

	if naturalAvgWeight > 0 && product.Properties.SellNatural {
		factor = factor / naturalAvgWeight
	} else if size > 0 {
		factor = factor / size
	}

	switch strings.ToLower(strings.TrimSpace(product.Properties.SupermarketUnit)) {
	case "kg", "l":
		factor = factor / 1000
		fallthrough
	case "ml", "g":
		factor = factor * 100
	case "packet", "each", "pack":
		// Do Nothing
	default:
		factor = 1.0
	}

	return factor
}
