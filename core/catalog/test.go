package catalog

import (
	"context"

	"google.golang.org/grpc"
	cat "happyfresh.io/catalog/lib/rpc/api"
)

type ClientMock struct {
	GetProductDetailFunc                              func(context.Context, *cat.ProductDetailRequest, ...grpc.CallOption) (*cat.ProductDetail, error)
	GetStoreProductsByProductIDFunc                   func(context.Context, *cat.StoreProductRequest, ...grpc.CallOption) (*cat.StoreProducts, error)
	GetStoreProductsByIDFunc                          func(context.Context, *cat.StoreProductRequest, ...grpc.CallOption) (*cat.StoreProducts, error)
	GetStoreProductsByStockLocationIDAndVariantIDFunc func(context.Context, *cat.StoreProductRequest, ...grpc.CallOption) (*cat.StoreProducts, error)
	GetProductTypesByProductTypeIDFunc                func(context.Context, *cat.ProductTypeRequest, ...grpc.CallOption) (*cat.ProductTypes, error)
	SearchProductsFunc                                func(context.Context, *cat.SearchProductsRequest, ...grpc.CallOption) (*cat.Products, error)
	TrackPopularityFunc                               func(context.Context, *cat.TrackPopularityRequest, ...grpc.CallOption) (*cat.OkResponse, error)
	GetTaxonomyFunc                                   func(context.Context, *cat.TaxonomyRequest, ...grpc.CallOption) (*cat.Taxonomy, error)
	GetBrandFunc                                      func(context.Context, *cat.BrandRequest, ...grpc.CallOption) (*cat.Brand, error)
	GetRestrictedTaxonsFunc                           func(context.Context, *cat.RestrictedTaxonsRequest, ...grpc.CallOption) (*cat.RestrictedTaxons, error)
	GetProductPropertiesFunc                          func(ctx context.Context, in *cat.ProductPropertiesRequest, opts ...grpc.CallOption) (*cat.Properties, error)
	GetStoreTaxonFunc                                 func(ctx context.Context, in *cat.StoreTaxonRequest, opts ...grpc.CallOption) (*cat.StoreTaxon, error)
	GetManualSKUBoostsByPromoCodeFunc                 func(context.Context, *cat.ManualSKUBoostRequest, ...grpc.CallOption) (*cat.ManualSKUBoosts, error)
	GetTaxonPinsByCountryFunc                         func(context.Context, *cat.TaxonPinRequest, ...grpc.CallOption) (*cat.TaxonPins, error)
	GetThemesFunc                                     func(context.Context, *cat.ThemeRequest, ...grpc.CallOption) (*cat.Themes, error)
	GetVariantsByThemeFunc                            func(context.Context, *cat.ThemeRequest, ...grpc.CallOption) (*cat.ThemeVariants, error)
}

func (c *ClientMock) Ping(ctx context.Context, in *cat.NullRequest, opts ...grpc.CallOption) (*cat.PingResponse, error) {
	return &cat.PingResponse{Pong: "Pong"}, nil
}

func (c *ClientMock) GetProductDetail(ctx context.Context, in *cat.ProductDetailRequest, opts ...grpc.CallOption) (*cat.ProductDetail, error) {
	if c.GetProductDetailFunc != nil {
		return c.GetProductDetailFunc(ctx, in, opts...)
	}

	return &cat.ProductDetail{}, nil
}

func (c *ClientMock) GetStoreProductsByProductID(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
	if c.GetStoreProductsByProductIDFunc != nil {
		return c.GetStoreProductsByProductIDFunc(ctx, in, opts...)
	}

	return &cat.StoreProducts{}, nil
}

func (c *ClientMock) GetStoreProductsByID(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
	if c.GetStoreProductsByIDFunc != nil {
		return c.GetStoreProductsByIDFunc(ctx, in, opts...)
	}

	return &cat.StoreProducts{}, nil
}

func (c *ClientMock) GetStoreProductsByStockLocationIDAndVariantID(ctx context.Context, in *cat.StoreProductRequest, opts ...grpc.CallOption) (*cat.StoreProducts, error) {
	if c.GetStoreProductsByStockLocationIDAndVariantIDFunc != nil {
		return c.GetStoreProductsByStockLocationIDAndVariantIDFunc(ctx, in, opts...)
	}

	return &cat.StoreProducts{}, nil
}

func (c *ClientMock) GetProductTypesByProductTypeID(ctx context.Context, in *cat.ProductTypeRequest, opts ...grpc.CallOption) (*cat.ProductTypes, error) {
	if c.GetProductTypesByProductTypeIDFunc != nil {
		return c.GetProductTypesByProductTypeIDFunc(ctx, in, opts...)
	}

	return &cat.ProductTypes{}, nil
}

func (c *ClientMock) SearchProducts(ctx context.Context, in *cat.SearchProductsRequest, opts ...grpc.CallOption) (*cat.Products, error) {
	if c.SearchProductsFunc != nil {
		return c.SearchProductsFunc(ctx, in, opts...)
	}

	return &cat.Products{}, nil
}

func (c *ClientMock) TrackPopularity(ctx context.Context, in *cat.TrackPopularityRequest, opts ...grpc.CallOption) (*cat.OkResponse, error) {
	if c.TrackPopularityFunc != nil {
		return c.TrackPopularityFunc(ctx, in, opts...)
	}

	return &cat.OkResponse{}, nil
}

func (c *ClientMock) GetTaxonomy(ctx context.Context, in *cat.TaxonomyRequest, opts ...grpc.CallOption) (*cat.Taxonomy, error) {
	if c.GetTaxonomyFunc != nil {
		return c.GetTaxonomyFunc(ctx, in, opts...)
	}

	return &cat.Taxonomy{}, nil
}

func (c *ClientMock) GetBrand(ctx context.Context, in *cat.BrandRequest, opts ...grpc.CallOption) (*cat.Brand, error) {
	if c.GetBrandFunc != nil {
		return c.GetBrandFunc(ctx, in, opts...)
	}

	return &cat.Brand{}, nil
}

func (c *ClientMock) GetRestrictedTaxons(ctx context.Context, in *cat.RestrictedTaxonsRequest, opts ...grpc.CallOption) (*cat.RestrictedTaxons, error) {
	if c.GetRestrictedTaxonsFunc != nil {
		return c.GetRestrictedTaxonsFunc(ctx, in, opts...)
	}

	return &cat.RestrictedTaxons{}, nil
}

func (c *ClientMock) GetProductProperties(ctx context.Context, in *cat.ProductPropertiesRequest, opts ...grpc.CallOption) (*cat.Properties, error) {
	if c.GetRestrictedTaxonsFunc != nil {
		return c.GetProductPropertiesFunc(ctx, in, opts...)
	}

	return &cat.Properties{}, nil
}

func (c *ClientMock) GetStoreTaxon(ctx context.Context, in *cat.StoreTaxonRequest, opts ...grpc.CallOption) (*cat.StoreTaxon, error) {
	if c.GetStoreTaxonFunc != nil {
		return c.GetStoreTaxonFunc(ctx, in, opts...)
	}

	return &cat.StoreTaxon{}, nil
}

func (c *ClientMock) GetManualSKUBoostsByPromoCode(ctx context.Context, in *cat.ManualSKUBoostRequest, opts ...grpc.CallOption) (*cat.ManualSKUBoosts, error) {
	if c.GetManualSKUBoostsByPromoCodeFunc != nil {
		return c.GetManualSKUBoostsByPromoCodeFunc(ctx, in, opts...)
	}

	return &cat.ManualSKUBoosts{}, nil
}

func (c *ClientMock) GetTaxonPinsByCountry(ctx context.Context, in *cat.TaxonPinRequest, opts ...grpc.CallOption) (*cat.TaxonPins, error) {
	if c.GetTaxonPinsByCountryFunc != nil {
		return c.GetTaxonPinsByCountryFunc(ctx, in, opts...)
	}

	return &cat.TaxonPins{}, nil
}

func (c *ClientMock) GetThemes(ctx context.Context, in *cat.ThemeRequest, opts ...grpc.CallOption) (*cat.Themes, error) {
	if c.GetThemesFunc != nil {
		return c.GetThemesFunc(ctx, in, opts...)
	}

	return &cat.Themes{}, nil
}

func (c *ClientMock) GetVariantsByTheme(ctx context.Context, in *cat.ThemeRequest, opts ...grpc.CallOption) (*cat.ThemeVariants, error) {
	if c.GetVariantsByThemeFunc != nil {
		return c.GetVariantsByThemeFunc(ctx, in, opts...)
	}

	return &cat.ThemeVariants{}, nil
}
