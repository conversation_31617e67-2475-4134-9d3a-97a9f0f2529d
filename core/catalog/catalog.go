package catalog

import (
	"context"
	"crypto/tls"
	"fmt"
	"strings"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/status"
	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/chacha"
	r "happyfresh.io/chacha/redis"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/config"
	"happyfresh.io/search/lib/trace/grpctrace"
)

var inst Client

type Client interface {
	GetProductDetail(context.Context, int64, int64, string, string) (*cat.ProductDetail, error)
	GetStoreProductsByProductID(context.Context, int64, string) (*cat.StoreProducts, error)
	GetStoreProductsByID(context.Context, int64, string) (*cat.StoreProducts, error)
	GetProductTypesByProductTypeID(context.Context, int64) (*cat.ProductTypes, error)
	GetStoreProductsPLP(context.Context, []int64, int64, string) ([]*cat.StoreProducts, error)
	GetProductsByVariantIDs(context.Context, []int64, []int64, int64, string) ([]*cat.Product, error)
	TrackPopularity(context.Context, []int64, int64, string) error
	GetTaxonomy(context.Context, int64, int64, string, string) (*cat.Taxonomy, error)
	GetTaxonIDs(taxons []*cat.Taxon) []int64
	GetBrands(context.Context, []int64, string) ([]*cat.Brand, error)
	GetRestrictedTaxons(ctx context.Context, taxonomyID int64, stockLocationID int64, channel string, isSND bool) (*cat.RestrictedTaxons, error)
	GetStoreTaxons(context.Context, []int64, int64, string, string) ([]*cat.StoreTaxon, error)
	GetManualSKUBoostsByPromoCode(context.Context, string, int64) (*cat.ManualSKUBoosts, error)
	GetTaxonPinsByCountry(context.Context, string, string) (*cat.TaxonPins, error)
	GetVariantsByTheme(context.Context, int64) (*cat.ThemeVariants, error)
	GetThemes(context.Context, int64, string) (*cat.Themes, error)
}

type ClientImpl struct {
	c                     cat.ApiClient
	useSecure             bool
	forceCache            bool
	productDetailCache    chacha.Getter
	storeProductCache     chacha.Getter
	taxonomyCache         chacha.Getter
	brandCache            chacha.Getter
	restrictedTaxonsCache chacha.Getter
	storeTaxonsCache      chacha.Getter
	manualSKUBoostsCache  chacha.Getter
	taxonPinsCache        chacha.Getter
	themesCache           chacha.Getter
	themeVariantsCache    chacha.Getter
}

type Option func(c *ClientImpl)

func New(address, redisDSN string, opts ...Option) (Client, error) {
	if inst != nil {
		return inst, nil
	}

	catalogClient := &ClientImpl{}
	for _, apply := range opts {
		apply(catalogClient)
	}

	if catalogClient.c == nil {
		secureOpt := grpc.WithInsecure()
		if catalogClient.useSecure {
			secureOpt = grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{}))
		}

		conn, err := grpc.Dial(
			address,
			secureOpt,
			grpc.WithUnaryInterceptor(
				grpctrace.ChainUnaryClientInterceptor(true, true),
			),
			grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy":"round_robin"}`),
		)
		if err != nil {
			return nil, errors.Wrap(err, "[catalog] failed to connect")
		}

		catalogClient.c = cat.NewApiClient(conn)
	}

	cbSetting := chacha.CBSetting{
		Interval:     time.Minute,
		Timeout:      time.Minute,
		IsSuccessful: catalogClient.isSuccessful,
	}

	if catalogClient.productDetailCache == nil {
		catalogClient.productDetailCache = chacha.NewGetter(
			"product-catalog:detail",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithTimeout(500*time.Millisecond),
			chacha.WithKeyRoundTripper(catalogClient.ProductDetailRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.storeProductCache == nil {
		catalogClient.storeProductCache = chacha.NewGetter(
			"product-catalog:store-product",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithTimeout(500*time.Millisecond),
			chacha.WithKeyRoundTripper(catalogClient.StoreProductByIDRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.taxonomyCache == nil {
		catalogClient.taxonomyCache = chacha.NewGetter(
			"product-catalog:taxonomy",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithKeyRoundTripper(catalogClient.TaxonomyRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.brandCache == nil {
		catalogClient.brandCache = chacha.NewGetter(
			"product-catalog:brand",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithKeyRoundTripper(catalogClient.BrandRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.restrictedTaxonsCache == nil {
		catalogClient.restrictedTaxonsCache = chacha.NewGetter(
			"product-catalog:restricted-taxon",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithKeyRoundTripper(catalogClient.RestrictedTaxonsRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.storeTaxonsCache == nil {
		catalogClient.storeTaxonsCache = chacha.NewGetter(
			"product-catalog:store-taxon",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithKeyRoundTripper(catalogClient.StoreTaxonRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.manualSKUBoostsCache == nil {
		catalogClient.manualSKUBoostsCache = chacha.NewGetter(
			"product-catalog:manual-sku-boost",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithKeyRoundTripper(catalogClient.ManualSKUBoostRoundTripper(), 1*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.taxonPinsCache == nil {
		catalogClient.taxonPinsCache = chacha.NewGetter(
			"product-catalog:taxon-pin",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithKeyRoundTripper(catalogClient.TaxonPinRoundTripper(), 1*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.themesCache == nil {
		catalogClient.themesCache = chacha.NewGetter(
			"product-catalog:themes",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithKeyRoundTripper(catalogClient.ThemesRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	if catalogClient.themeVariantsCache == nil {
		catalogClient.themeVariantsCache = chacha.NewGetter(
			"product-catalog:theme-variants",
			chacha.WithCache("redis", r.WithRedisURL(redisDSN), r.WithLWV(), r.WithForceLWV(catalogClient.forceCache)),
			chacha.WithKeyRoundTripper(catalogClient.ThemeVariantsRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
			chacha.WithCircuitBreaker(cbSetting),
		)
	}

	return catalogClient, nil
}

func (c *ClientImpl) isSuccessful(err error) bool {
	if err == nil {
		return true
	}

	s, ok := status.FromError(errors.Cause(err))
	if !ok {
		return false
	}

	if s.Code() == codes.Canceled || s.Code() == codes.InvalidArgument || s.Code() == codes.NotFound || s.Code() == codes.Unimplemented {
		return true
	}

	return false
}

func (c *ClientImpl) GetProductDetail(ctx context.Context, stockLocationID, productID int64, channel, locale string) (*cat.ProductDetail, error) {
	key, err := cacheKeyEncoder(ctx, "product-detail", stockLocationID, productID, channel, locale)
	if err != nil {
		return nil, err
	}

	resp := &cat.ProductDetail{}
	err = c.productDetailCache.Get(ctx, key, resp)
	if err != nil {
		return nil, errors.Wrap(err, "[catalog] error fetching product data")
	}

	return resp, nil
}

func (c *ClientImpl) GetStoreProductsByProductID(ctx context.Context, productID int64, locale string) (*cat.StoreProducts, error) {
	if productID <= 0 || locale == "" {
		return nil, errors.New("[catalog] invalid product ID or locale")
	}

	res, err := c.c.GetStoreProductsByProductID(ctx, &cat.StoreProductRequest{
		ProductId: productID,
		Locale:    locale,
	})
	if err != nil {
		return nil, errors.Wrap(err, "[catalog] error fetching product data")
	}

	return res, nil
}

func (c *ClientImpl) GetStoreProductsByID(ctx context.Context, storeProductID int64, locale string) (*cat.StoreProducts, error) {
	if storeProductID <= 0 || locale == "" {
		return nil, errors.New("[catalog] invalid store product ID or locale")
	}

	res, err := c.c.GetStoreProductsByID(ctx, &cat.StoreProductRequest{
		StoreProductId: storeProductID,
		Locale:         locale,
	})
	if err != nil {
		return nil, errors.Wrap(err, "[catalog] error fetching product data")
	}

	return res, nil
}

func (c *ClientImpl) GetStoreProductsPLP(ctx context.Context, variantIDs []int64, stockLocationID int64, locale string) ([]*cat.StoreProducts, error) {
	keys := make([]string, len(variantIDs))
	targets := make([]proto.Message, len(variantIDs))
	for i, id := range variantIDs {
		key, _ := cacheKeyEncoder(ctx, "store-products", id, stockLocationID, locale)
		keys[i] = key
		targets[i] = &cat.StoreProducts{}
	}

	if err := c.storeProductCache.GetMany(ctx, keys, targets); err != nil {
		return nil, errors.Wrap(err, "[catalog] error fetching product data")
	}

	result := make([]*cat.StoreProducts, len(targets))
	for i, t := range targets {
		result[i] = t.(*cat.StoreProducts)
	}

	return result, nil
}

func (c *ClientImpl) GetProductTypesByProductTypeID(ctx context.Context, productTypeID int64) (*cat.ProductTypes, error) {
	res, err := c.c.GetProductTypesByProductTypeID(ctx, &cat.ProductTypeRequest{
		Id: productTypeID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "[catalog] error fetching product types data")
	}

	return res, nil
}

func (c *ClientImpl) GetProductsByVariantIDs(ctx context.Context, variantIDs []int64, restrictedTaxonIDs []int64, storeID int64, sellableUserType string) ([]*cat.Product, error) {
	products, err := c.c.SearchProducts(ctx, &cat.SearchProductsRequest{
		VariantIds:         variantIDs,
		RestrictedTaxonIds: restrictedTaxonIDs,
		StoreId:            storeID,
		UserType:           sellableUserType,
	})
	if err != nil {
		return nil, err
	}

	return products.Products, nil
}

// TrackPopularity send popularity tracking data to catalog
func (c *ClientImpl) TrackPopularity(ctx context.Context, variantIDs []int64, stockLocationID int64, action string) error {
	var actionMap cat.TrackPopularityRequest_Action
	switch action {
	case "click":
		actionMap = cat.TrackPopularityRequest_CLICKED
	case "atc":
		actionMap = cat.TrackPopularityRequest_ADDED_TO_CART
	case "purchased":
		actionMap = cat.TrackPopularityRequest_PURCHASED
	default:
		actionMap = cat.TrackPopularityRequest_UNKNOWN
	}

	for _, variantID := range variantIDs {
		_, err := c.c.TrackPopularity(ctx, &cat.TrackPopularityRequest{
			VariantId:       variantID,
			StockLocationId: stockLocationID,
			Action:          actionMap,
		})
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("[catalog] error submit %s track for variant ID %d", action, variantID))
		}
	}

	return nil
}

func (c *ClientImpl) GetTaxonomy(ctx context.Context, taxonomyID int64, stockLocationID int64, locale string, country string) (*cat.Taxonomy, error) {
	key, err := cacheKeyEncoder(ctx, "taxonomy-list", taxonomyID, stockLocationID, locale, country)
	if err != nil {
		return nil, err
	}

	taxonomy := &cat.Taxonomy{}
	err = c.taxonomyCache.Get(ctx, key, taxonomy)
	if err != nil {
		return nil, err
	}

	return taxonomy, nil
}

func (c *ClientImpl) GetTaxonIDs(taxons []*cat.Taxon) []int64 {
	taxonIDs := make([]int64, 0)

	for _, taxon := range taxons {
		if taxon.GetTaxonId() > 0 {
			taxonIDs = append(taxonIDs, taxon.GetTaxonId())
		}

		if taxon.GetTaxons() != nil {
			taxonIDs = append(taxonIDs, c.GetTaxonIDs(taxon.GetTaxons())...)
		}
	}

	return taxonIDs
}

func (c *ClientImpl) GetBrands(ctx context.Context, brandIDs []int64, locale string) ([]*cat.Brand, error) {
	keys := make([]string, len(brandIDs))
	targets := make([]proto.Message, len(brandIDs))
	for i, brandID := range brandIDs {
		key, err := cacheKeyEncoder(ctx, "brand", brandID, locale)
		if err != nil {
			log.For("CatalogClient", "GetBrands").Warn(err)
		}
		keys[i] = key
		targets[i] = &cat.Brand{}
	}

	if err := c.brandCache.GetMany(ctx, keys, targets); err != nil {
		return nil, errors.Wrap(err, "[catalog] error fetching brand data")
	}

	result := make([]*cat.Brand, len(targets))
	for i, target := range targets {
		result[i] = target.(*cat.Brand)
	}

	return result, nil
}

func (c *ClientImpl) GetRestrictedTaxons(ctx context.Context, taxonomyID int64, stockLocationID int64, channel string, isSND bool) (*cat.RestrictedTaxons, error) {
	key, err := cacheKeyEncoder(ctx, "restricted-taxons", taxonomyID, stockLocationID)
	if err != nil {
		return nil, err
	}

	restrictedTaxons := &cat.RestrictedTaxons{}
	err = c.restrictedTaxonsCache.Get(ctx, key, restrictedTaxons)
	if err != nil {
		return nil, err
	}

	androidFilteredTaxonIDs := config.AndroidFilteredTaxonIDsInt64()
	restrictedTaxons.RestrictedTaxonIds = AddTaxonFilterForSpecificClientType(ctx, channel, isSND, restrictedTaxons.RestrictedTaxonIds, androidFilteredTaxonIDs)
	return restrictedTaxons, nil
}

func (c *ClientImpl) GetStoreTaxons(ctx context.Context, taxonIDs []int64, stockLocationID int64, locale, country string) ([]*cat.StoreTaxon, error) {
	keys := make([]string, len(taxonIDs))
	targets := make([]proto.Message, len(taxonIDs))
	for i, taxonID := range taxonIDs {
		key, err := cacheKeyEncoder(ctx, "store-taxon", taxonID, stockLocationID, locale, country)
		if err != nil {
			log.For("CatalogClient", "GetStoreTaxons").Warn(err)
		}
		keys[i] = key
		targets[i] = &cat.StoreTaxon{}
	}

	if err := c.storeTaxonsCache.GetMany(ctx, keys, targets); err != nil {
		return nil, errors.Wrap(err, "[catalog] error fetching store taxon data")
	}

	result := make([]*cat.StoreTaxon, len(targets))
	for i, target := range targets {
		result[i] = target.(*cat.StoreTaxon)
	}

	return result, nil
}

func (c *ClientImpl) GetManualSKUBoostsByPromoCode(ctx context.Context, promoCode string, stockLocationId int64) (*cat.ManualSKUBoosts, error) {
	key, err := cacheKeyEncoder(ctx, "manual-sku-boost", promoCode, stockLocationId)
	if err != nil {
		return nil, err
	}

	manualSKUBoosts := &cat.ManualSKUBoosts{}
	err = c.manualSKUBoostsCache.Get(ctx, key, manualSKUBoosts)
	if err != nil {
		return nil, err
	}

	return manualSKUBoosts, nil
}

func (c *ClientImpl) GetTaxonPinsByCountry(ctx context.Context, countryCode string, taxonPinMode string) (*cat.TaxonPins, error) {
	key, err := cacheKeyEncoder(ctx, "taxon-pin", countryCode, taxonPinMode)
	if err != nil {
		return nil, err
	}

	taxonPins := &cat.TaxonPins{}
	err = c.taxonPinsCache.Get(ctx, key, taxonPins)
	if err != nil {
		return nil, err
	}

	return taxonPins, nil
}

func (c *ClientImpl) GetThemes(ctx context.Context, stockLocationId int64, locale string) (*cat.Themes, error) {
	key, err := cacheKeyEncoder(ctx, "themes", stockLocationId, locale)
	if err != nil {
		return nil, err
	}

	themes := &cat.Themes{}
	err = c.themesCache.Get(ctx, key, themes)
	if err != nil {
		return nil, err
	}

	return themes, nil
}

func (c *ClientImpl) GetVariantsByTheme(ctx context.Context, themeId int64) (*cat.ThemeVariants, error) {
	key, err := cacheKeyEncoder(ctx, "theme-variants", themeId)
	if err != nil {
		return nil, err
	}

	variants := &cat.ThemeVariants{}
	err = c.themeVariantsCache.Get(ctx, key, variants)
	if err != nil {
		return nil, err
	}

	return variants, nil
}

func (c *ClientImpl) ProductDetailRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		pd, err := c.c.GetProductDetail(ctx, &cat.ProductDetailRequest{
			StockLocationId: str.MaybeString(args[0]).Int64(),
			ProductId:       str.MaybeString(args[1]).Int64(),
			UserType:        str.MaybeString(args[2]).String(),
			Locale:          str.MaybeString(args[3]).String(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "[catalog] error fetching product data")
		}

		return pd, nil
	})
}

func (c *ClientImpl) StoreProductByIDRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		vID := str.MaybeString(args[0]).Int64()
		sID := str.MaybeString(args[1]).Int64()
		loc := str.MaybeString(args[2]).String()

		pd, err := c.c.GetStoreProductsByStockLocationIDAndVariantID(ctx, &cat.StoreProductRequest{
			VariantId:       vID,
			StockLocationId: sID,
			Locale:          loc,
		})
		if err != nil {

			status, ok := status.FromError(err)
			if ok {
				if status.Code() == codes.InvalidArgument || status.Code() == codes.NotFound {
					log.Warnf("Use empty cache on variantID %d, stockLocationID %d, locale %s", vID, sID, loc)
					return &cat.StoreProducts{}, nil
				}
			}

			return nil, errors.Wrap(err, "[catalog] error fetching product data")
		}

		return pd, nil
	})
}

func (c *ClientImpl) TaxonomyRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		t, err := c.c.GetTaxonomy(ctx, &cat.TaxonomyRequest{
			TaxonomyId:      str.MaybeString(args[0]).Int64(),
			StockLocationId: str.MaybeString(args[1]).Int64(),
			Locale:          str.MaybeString(args[2]).String(),
			Country:         str.MaybeString(args[3]).String(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "[catalog] error fetching taxonomy data")
		}

		return t, nil
	})
}

func (c *ClientImpl) BrandRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		b, err := c.c.GetBrand(ctx, &cat.BrandRequest{
			BrandId: str.MaybeString(args[0]).Int64(),
			Locale:  str.MaybeString(args[1]).String(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "[catalog] error fetching brand data")
		}

		return b, nil
	})
}

func (c *ClientImpl) RestrictedTaxonsRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		rt, err := c.c.GetRestrictedTaxons(ctx, &cat.RestrictedTaxonsRequest{
			TaxonomyId:      str.MaybeString(args[0]).Int64(),
			StockLocationId: str.MaybeString(args[1]).Int64(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "[catalog] error fetching restricted taxons data")
		}

		return rt, nil
	})
}

func (c *ClientImpl) StoreTaxonRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		t, err := c.c.GetStoreTaxon(ctx, &cat.StoreTaxonRequest{
			TaxonId:         str.MaybeString(args[0]).Int64(),
			StockLocationId: str.MaybeString(args[1]).Int64(),
			Locale:          str.MaybeString(args[2]).String(),
			Country:         str.MaybeString(args[3]).String(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "[catalog] error fetching store taxon data")
		}

		return t, nil
	})
}

func (c *ClientImpl) ManualSKUBoostRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		rt, err := c.c.GetManualSKUBoostsByPromoCode(ctx, &cat.ManualSKUBoostRequest{
			PromoCode:       str.MaybeString(args[0]).String(),
			StockLocationId: str.MaybeString(args[1]).Int64(),
		})

		if err != nil {
			log.For("CatalogClient", "ManualSKUBoostRoundTripper").Warn(errors.Wrap(err, "[catalog] failed to fetch manual sku boosts by cache"))
			return &cat.ManualSKUBoosts{ManualSkuBoosts: []*cat.ManualSKUBoost{}}, nil
		}

		return rt, nil
	})
}

func (c *ClientImpl) TaxonPinRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		var isGlobal bool
		if str.MaybeString(args[1]).String() == "global_search" {
			isGlobal = true
		}
		rt, err := c.c.GetTaxonPinsByCountry(ctx, &cat.TaxonPinRequest{
			CountryCode: str.MaybeString(args[0]).String(),
			IsGlobal:    isGlobal,
		})

		if err != nil {
			return nil, errors.Wrap(err, "[catalog] error fetching taxon pins data from cache")
		}

		return rt, nil
	})
}

func (c *ClientImpl) ThemesRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		rt, err := c.c.GetThemes(ctx, &cat.ThemeRequest{
			StockLocationId: str.MaybeString(args[0]).Int64(),
		})

		if err != nil {
			return nil, errors.Wrap(err, "[catalog] error fetching themes data from cache")
		}

		return rt, nil
	})
}

func (c *ClientImpl) ThemeVariantsRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
		rt, err := c.c.GetVariantsByTheme(ctx, &cat.ThemeRequest{
			ThemeId: str.MaybeString(args[0]).Int64(),
		})

		if err != nil {
			return nil, errors.Wrap(err, "[catalog] error fetching variant IDs by theme from cache")
		}

		return rt, nil
	})
}

func WithgRPCClient(cc cat.ApiClient) Option {
	return func(c *ClientImpl) {
		c.c = cc
	}
}

func WithSecure(useSecure bool) Option {
	return func(c *ClientImpl) {
		c.useSecure = useSecure
	}
}

func WithForceCache(forceCache bool) Option {
	return func(c *ClientImpl) {
		c.forceCache = forceCache
	}
}

func WithProductDetailCache(cache chacha.Getter) Option {
	return func(c *ClientImpl) {
		c.productDetailCache = cache
	}
}

func WithStoreProductCache(cache chacha.Getter) Option {
	return func(c *ClientImpl) {
		c.storeProductCache = cache
	}
}

func WithTaxonomyCache(cache chacha.Getter) Option {
	return func(c *ClientImpl) {
		c.taxonomyCache = cache
	}
}

func WithBrandCache(cache chacha.Getter) Option {
	return func(c *ClientImpl) {
		c.brandCache = cache
	}
}

func WithStoreTaxonsCache(cache chacha.Getter) Option {
	return func(c *ClientImpl) {
		c.storeTaxonsCache = cache
	}
}

func WithManualSKUBoostsCache(cache chacha.Getter) Option {
	return func(c *ClientImpl) {
		c.manualSKUBoostsCache = cache
	}
}

func WithThemesCache(cache chacha.Getter) Option {
	return func(c *ClientImpl) {
		c.themesCache = cache
	}
}

func WithThemeVariantsCache(cache chacha.Getter) Option {
	return func(c *ClientImpl) {
		c.themeVariantsCache = cache
	}
}

func cacheKeyEncoder(ctx context.Context, s string, args ...interface{}) (string, error) {
	sb := strings.Builder{}
	sb.WriteString(s)
	for _, arg := range args {
		sb.WriteString(":")
		sb.WriteString(str.MaybeString(arg).String())
	}

	return sb.String(), nil
}

func cacheKeyDecoder(ctx context.Context, s string) (string, []interface{}, error) {
	keys := strings.Split(s, ":")

	var getterName string
	args := make([]interface{}, len(keys)-1)
	for i, key := range keys {
		if i == 0 {
			getterName = key
			continue
		}

		args[i-1] = key
	}

	return getterName, args, nil
}

func AddTaxonFilterForSpecificClientType(context context.Context, channel string, isSND bool, taxonNoneFilter, androidFilteredTaxonIDs []int64) []int64 {
	if len(taxonNoneFilter) == 0 {
		return taxonNoneFilter
	}

	if channel != "android" || isSND {
		return taxonNoneFilter
	}

	if len(androidFilteredTaxonIDs) == 0 {
		return taxonNoneFilter
	}

	taxonNoneFilter = append(taxonNoneFilter, androidFilteredTaxonIDs...)

	return taxonNoneFilter
}

func RemoveTaxonFilterForSpecificClientType(context context.Context, channel string, isSND bool, taxonNoneFilter, androidFilteredTaxonIDs []int64) []int64 {
	if len(taxonNoneFilter) == 0 || len(androidFilteredTaxonIDs) == 0 {
		return taxonNoneFilter
	}

	if channel == "android" && !isSND {
		return taxonNoneFilter
	}

	var filteredTaxons []int64
	for _, taxonID := range taxonNoneFilter {
		insert := true
		for _, filteredID := range androidFilteredTaxonIDs {
			if taxonID == filteredID {
				insert = false
				break
			}
		}
		if insert {
			filteredTaxons = append(filteredTaxons, int64(taxonID))
		}
	}

	return filteredTaxons
}
