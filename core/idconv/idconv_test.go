package idconv_test

import (
	"context"
	"os"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/core/idconv"
)

func TestMain(m *testing.M) {
	done := datastore.OpenTest(m, datastore.LocalQuery, os.Getenv("TEST_DATABASE_URL"), nil)
	defer done()

	os.Exit(m.Run())
}

func TestDatastoreIDConv(t *testing.T) {
	testCases := []*struct {
		name  string
		f     func(context.Context, []string) ([]string, error)
		input []string
		want  []string
	}{
		{"Return product global ID if any", idconv.ProductToGlobal, []string{"1", "2", "3"}, []string{"91", "92", "93"}},
		{"Return product global ID -1 if doesn't exist", idconv.ProductToGlobal, []string{"1", "2", "11"}, []string{"91", "92", "-1"}},
		{"Return variant global ID if any", idconv.VariantToGlobal, []string{"1", "2", "3"}, []string{"91", "92", "93"}},
		{"Return variant global ID -1 if doesn't exist", idconv.VariantToGlobal, []string{"1", "2", "11"}, []string{"91", "92", "-1"}},
		{"Return brand global ID if any", idconv.BrandToGlobal, []string{"1"}, []string{"91"}},
		{"Return brand global ID -1 if doesn't exist", idconv.BrandToGlobal, []string{"1", "11", "12"}, []string{"91", "-1", "-1"}},
		{"Return product type global ID if any", idconv.ProductTypeToGlobal, []string{"100", "101"}, []string{"9100", "9101"}},
		{"Return product type global ID -1 if doesn't exist", idconv.ProductTypeToGlobal, []string{"100", "101"}, []string{"9100", "9101"}},
	}

	ctx := context.Background()
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := tc.f(ctx, tc.input)
			if err != nil {
				t.Error(err)
			}

			assert := convey.ShouldResemble(result, tc.want)
			if assert != "" {
				t.Error(assert)
			}
		})
	}
}
