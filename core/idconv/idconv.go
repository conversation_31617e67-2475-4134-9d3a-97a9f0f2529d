package idconv

import (
	"context"

	"happyfresh.io/hubble/lib/rpc"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/core/hubble"
)

var std *conv

type IDConv interface {
	PLPIDs(context.Context, []int64) ([]*rpc.TranslatedID, error)
	ProductToGlobal(context.Context, []string) ([]string, error)
	VariantToGlobal(context.Context, []string) ([]string, error)
	BrandToGlobal(context.Context, []string) ([]string, error)
	ProductTypeToGlobal(context.Context, []string) ([]string, error)
}

func New(h *hubble.Client) IDConv {
	if std != nil {
		return std
	}

	c := &conv{hubble: h}
	std = c

	return c
}

type conv struct {
	hubble *hubble.Client
}

func (c *conv) PLPIDs(ctx context.Context, legacyProductIDs []int64) ([]*rpc.TranslatedID, error) {
	return c.hubble.TranslateIDs(ctx, legacyProductIDs)
}

func (c *conv) ProductToGlobal(ctx context.Context, legacyIDs []string) ([]string, error) {
	return datastore.GlobalIDsByLegacyIDs(ctx, "product", legacyIDs)
}

func (c *conv) VariantToGlobal(ctx context.Context, legacyIDs []string) ([]string, error) {
	return datastore.GlobalIDsByLegacyIDs(ctx, "variant", legacyIDs)
}

func (c *conv) BrandToGlobal(ctx context.Context, legacyIDs []string) ([]string, error) {
	return datastore.GlobalIDsByLegacyIDs(ctx, "brand", legacyIDs)
}

func (c *conv) ProductTypeToGlobal(ctx context.Context, legacyIDs []string) ([]string, error) {
	return datastore.GlobalIDsByLegacyIDs(ctx, "product-type", legacyIDs)
}

func PLPIDs(ctx context.Context, legacyProductIDs []int64) ([]*rpc.TranslatedID, error) {
	return std.PLPIDs(ctx, legacyProductIDs)
}

func ProductToGlobal(ctx context.Context, legacyIDs []string) ([]string, error) {
	return std.ProductToGlobal(ctx, legacyIDs)
}

func VariantToGlobal(ctx context.Context, legacyIDs []string) ([]string, error) {
	return std.VariantToGlobal(ctx, legacyIDs)
}

func BrandToGlobal(ctx context.Context, legacyIDs []string) ([]string, error) {
	return std.BrandToGlobal(ctx, legacyIDs)
}

func ProductTypeToGlobal(ctx context.Context, legacyIDs []string) ([]string, error) {
	return std.ProductTypeToGlobal(ctx, legacyIDs)
}
