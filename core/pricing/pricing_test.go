package pricing

import (
	"context"
	"testing"

	cat "happyfresh.io/catalog/lib/rpc/api"
	"happyfresh.io/search/core/datastore"

	"github.com/stretchr/testify/assert"
	"happyfresh.io/search/core/response"
)

type searchResponseMock struct {
	mockFunc func(v interface{}) error
}

func (s *searchResponseMock) Page() int64 { return 100 }

func (s *searchResponseMock) PageSize() int64 { return 100 }

func (s *searchResponseMock) TotalPages() int64 { return 100 }

func (s *searchResponseMock) Count() int64 { return 100 }

func (s *searchResponseMock) TotalCount() int64 { return 100 }

func (s *searchResponseMock) UnmarshalRaw(v interface{}) error {
	if s.mockFunc != nil {
		return s.mockFunc(v)
	}

	result := v.(*response.Response)

	result.StockItems = append(result.StockItems, []datastore.StockItem{
		{
			Sku:               "P-001",
			StoreId:           1101,
			NormalPrice:       10,
			Price:             10,
			NormalCost:        10,
			Cost:              10,
			OriginalPromoCost: 10,
		},
		{
			Sku:               "P-002",
			StoreId:           1101,
			NormalPrice:       10,
			Price:             10,
			NormalCost:        10,
			Cost:              10,
			OriginalPromoCost: 10,
		},
	}...)

	return nil
}

func (s *searchResponseMock) UnmarshalSnipet(v interface{}) error { return nil }

func (s *searchResponseMock) Body() []byte { return nil }

func (s *searchResponseMock) SearchID() string { return "" }

func TestGet(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        func(ctx context.Context, opt *Option) (map[string]*Product, error)
		expected map[string]*Product
	}{
		{
			"Case ProductPriceGetter not nil",
			func(ctx context.Context, opt *Option) (map[string]*Product, error) {
				return map[string]*Product{
					"P-001-TH": {
						Code:    "P-001",
						StoreID: 1101,
						Price: &Price{
							NormalPrice:       100000,
							PromoPrice:        100000,
							NormalCost:        100000,
							PromoCost:         100000,
							StoreNormalPrice:  100000,
							StorePromoPrice:   100000,
							Markup:            0.1,
							Quantity:          5,
							ThresholdQuantity: 3,
						},
						Promotions: []*Promotion{},
					},
				}, nil
			},
			map[string]*Product{
				"P-001-TH": {
					Code:    "P-001",
					StoreID: 1101,
					Price: &Price{
						NormalPrice:       100000,
						PromoPrice:        100000,
						NormalCost:        100000,
						PromoCost:         100000,
						StoreNormalPrice:  100000,
						StorePromoPrice:   100000,
						Markup:            0.1,
						Quantity:          5,
						ThresholdQuantity: 3,
					},
					Promotions: []*Promotion{},
				},
			},
		},
		{
			"Case ProductPriceGetter nil",
			func(ctx context.Context, opt *Option) (map[string]*Product, error) {
				return nil, nil
			},
			nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ProductPriceGetter = tc.p
			reality, err := Get(context.Background(), WithCountryCode("TH"))
			assert.Equal(tc.expected, reality)
			assert.NoError(err)

		})
	}
}

func TestGetAllChannel(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name     string
		p        func(ctx context.Context, opt *Option) (map[int64]*Product, error)
		expected map[int64]*Product
	}{
		{
			"Case ProductPriceAllChannelGetter not nil",
			func(ctx context.Context, opt *Option) (map[int64]*Product, error) {
				return map[int64]*Product{
					1101: {
						Code:    "P-001-ID",
						StoreID: 1101,
						Price: &Price{
							NormalPrice:       100000,
							PromoPrice:        100000,
							NormalCost:        100000,
							PromoCost:         100000,
							StoreNormalPrice:  100000,
							StorePromoPrice:   100000,
							Markup:            0.1,
							Quantity:          5,
							ThresholdQuantity: 3,
						},
						Promotions: []*Promotion{},
					},
				}, nil
			},
			map[int64]*Product{
				1101: {
					Code:    "P-001-ID",
					StoreID: 1101,
					Price: &Price{
						NormalPrice:       100000,
						PromoPrice:        100000,
						NormalCost:        100000,
						PromoCost:         100000,
						StoreNormalPrice:  100000,
						StorePromoPrice:   100000,
						Markup:            0.1,
						Quantity:          5,
						ThresholdQuantity: 3,
					},
					Promotions: []*Promotion{},
				},
			},
		},
		{
			"Case ProductPriceAllChannelGetter nil",
			func(ctx context.Context, opt *Option) (map[int64]*Product, error) {
				return nil, nil
			},
			nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ProductPriceAllChannelGetter = tc.p
			reality, err := GetAllChannel(context.Background(), WithCountryCode("TH"))
			assert.Equal(tc.expected, reality)
			assert.NoError(err)

		})
	}
}

func TestWithCatalogProducts(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name          string
		storeProduct  *cat.StoreProducts
		expectedItems []*Item
	}{
		{
			name: "With catalog store products",
			storeProduct: &cat.StoreProducts{
				StoreProducts: []*cat.StoreProduct{
					{
						StoreId: 1101,
					},
				},
				ProductDetail: &cat.ProductDetail{
					Properties: &cat.Properties{
						SellNatural:   true,
						AverageWeight: 10,
					},
					Variants: []*cat.Variant{
						{
							Sku: "72206-ID",
						},
					},
				},
			},
			expectedItems: []*Item{
				{
					StoreID:          1101,
					SKU:              "72206-ID",
					SellNatural:      true,
					NaturalAvgWeight: 10,
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithCatalogProducts(testCase.storeProduct)

			opt := Option{}
			f(&opt)

			assert.Equal(testCase.expectedItems, opt.Items)
		})
	}
}

func TestWithProducts(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name          string
		storeProducts []*cat.StoreProducts
		expectedItems []*Item
	}{
		{
			name: "With catalog store products",
			storeProducts: []*cat.StoreProducts{
				{
					StoreProducts: []*cat.StoreProduct{
						{
							StoreId: 1101,
						},
					},
					ProductDetail: &cat.ProductDetail{
						Properties: &cat.Properties{
							SellNatural:   true,
							AverageWeight: 10,
						},
						Variants: []*cat.Variant{
							{
								Sku: "72206-ID",
							},
						},
					},
				},
			},
			expectedItems: []*Item{
				{
					StoreID:          1101,
					SKU:              "72206-ID",
					SellNatural:      true,
					NaturalAvgWeight: 10,
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithProducts(testCase.storeProducts)

			opt := Option{}
			f(&opt)

			assert.Equal(testCase.expectedItems, opt.Items)
		})
	}
}

func TestWithCatalogProductDetail(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name          string
		product       *cat.ProductDetail
		storeID       int64
		expectedItems []*Item
	}{
		{
			name: "With catalog product detail",
			product: &cat.ProductDetail{
				Variants: []*cat.Variant{
					{
						Sku: "72206-ID",
					},
				},
				Properties: &cat.Properties{
					AverageWeight: 10,
					SellNatural:   true,
				},
			},
			storeID: 1101,
			expectedItems: []*Item{
				{
					StoreID:          1101,
					SKU:              "72206-ID",
					SellNatural:      true,
					NaturalAvgWeight: 10,
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			f := WithCatalogProductDetail(testCase.product, testCase.storeID)

			opt := Option{}
			f(&opt)

			assert.Equal(testCase.expectedItems, opt.Items)
		})
	}
}

func TestWithChannel(t *testing.T) {
	assert := assert.New(t)

	channel := "android"
	f := WithChannel(channel)

	opt := Option{}
	f(&opt)

	assert.Equal(channel, opt.Channel)
}

func TestWithUserType(t *testing.T) {
	assert := assert.New(t)

	userType := "hc"
	f := WithUserType(userType)

	opt := Option{}
	f(&opt)

	assert.Equal(userType, opt.UserType)
}

func TestWithCountryCode(t *testing.T) {
	assert := assert.New(t)

	countryCode := "id"
	f := WithCountryCode(countryCode)

	opt := Option{}
	f(&opt)

	assert.Equal(countryCode, opt.CountryCode)
}
