package priceclient

import (
	"time"

	price "happyfresh.io/price/lib/rpc/clientcompat"
)

var PriceClient price.Client

func ConnectPrice(address string, useSecure bool) error {
	priceConnOpt := &price.CircuitBreakerOption{
		Timeout:                      60 * time.Second,
		Interval:                     60 * time.Second,
		ConsecutiveFailuresThreshold: 5,
		RPM:                          300,
		FailureRatioThreshold:        0.6,
	}

	if useSecure {
		err := price.NewConnectionSecure(address, priceConnOpt)
		if err != nil {
			return err
		}
	} else {
		err := price.NewConnection(address, priceConnOpt)
		if err != nil {
			return err
		}
	}

	PriceClient = price.NewClient()
	return nil
}
