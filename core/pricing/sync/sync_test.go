package sync_test

import (
	"testing"

	cat "happyfresh.io/catalog/lib/rpc/api"

	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/pricing"
	"happyfresh.io/search/core/pricing/sync"
)

func TestIsSync(t *testing.T) {
	testCases := []struct {
		testName      string
		indexResponse *index.StockItem
		productDetail *cat.ProductDetail
		price         *pricing.Product
		channel       string
		isSync        bool
		syncType      string
	}{
		{
			testName: "Mismatch \"in stock\"",
			indexResponse: &index.StockItem{
				InStock: "true",
			},
			price: &pricing.Product{
				InStock: false,
			},
			isSync:   true,
			syncType: "in_stock",
		},
		{
			testName: "Mismatch price and promotion",
			indexResponse: &index.StockItem{
				InStock:       "true",
				PriceAndroid:  4500,
				PromotionType: "discount",
			},
			productDetail: &cat.ProductDetail{
				Variants: []*cat.Variant{
					{
						Sku: "8993363150012",
					},
				},
			},
			price: &pricing.Product{
				InStock: true,
				Price: &pricing.Price{
					PromoPrice: 5000,
				},
				Promotions: nil,
			},
			channel:  "android",
			isSync:   true,
			syncType: "price_and_promotion",
		}, {
			testName: "No mismatch",
			indexResponse: &index.StockItem{
				InStock:       "true",
				PriceAndroid:  5000,
				PromotionType: "",
			},
			productDetail: &cat.ProductDetail{
				Variants: []*cat.Variant{
					{
						Sku: "8993363150012",
					},
				},
			},
			price: &pricing.Product{
				InStock: true,
				Price: &pricing.Price{
					PromoPrice: 5000,
				},
			},
			channel: "android",
			isSync:  false,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.testName, func(t *testing.T) {
			is, st := sync.IsSync(testCase.indexResponse, testCase.productDetail, testCase.price, testCase.channel)

			if is != testCase.isSync {
				t.Errorf("\nexpectation: %v\nreality: %v", testCase.isSync, is)
			}

			if is {
				if st != testCase.syncType {
					t.Errorf("\nexpectation: %v\nreality: %v", testCase.syncType, st)
				}
			}
		})
	}
}
