package sync

import (
	"context"
	"fmt"
	"strings"

	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"

	"happyfresh.io/search/lib/strs"
	"happyfresh.io/search/worker"

	cat "happyfresh.io/catalog/lib/rpc/api"

	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/pricing"
)

func Sync(ctx context.Context, indexResponse *index.SearchResponse, products []*cat.StoreProducts, prices map[string]*pricing.Product, channel, countryCode string) error {
	for i, p := range products {
		if p.ProductDetail == nil {
			continue
		}

		sku := strs.SKU(p.ProductDetail.Variants[0].Sku).ToProductCode(countryCode)
		price, ok := prices[sku]
		if !ok {
			continue
		}

		if doSync, syncType := IsSync(indexResponse.StockItems[i], p.ProductDetail, price, channel); doSync {
			logPayload := map[string]interface{}{
				"product_id":        indexResponse.StockItems[i].ProductID,
				"variant_id":        indexResponse.StockItems[i].VariantID,
				"stock_location_id": p.StoreProducts[0].StockLocationId,
				"sync_type":         syncType,
			}
			log.For("aloha", "Sync").WithFields(logPayload).Info("Add to sync queue")

			payload, err := worker.NewStockItemPayload(int(p.StoreProducts[0].StoreProductId), countryCode)
			if err != nil {
				log.For("aloha", "Sync").WithFields(logPayload).Warnf("Create payload failed")
			}

			queueName := "update_stock_item"
			if len(countryCode) != 0 {
				queueName = fmt.Sprintf("%s_%s", queueName, strings.ToLower(countryCode))
			}
			err = worker.Submit(ctx, queueName, payload)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func IsSync(si *index.StockItem, pd *cat.ProductDetail, pp *pricing.Product, channel string) (bool, string) {
	indexInStock := str.MaybeString(si.InStock).Bool()
	priceInStock := pp.InStock

	// sycn data if there is stock status difference
	if indexInStock != priceInStock {
		return true, "in_stock"
	}

	var indexPrice float64
	switch channel {
	case "android":
		indexPrice = si.PriceAndroid
	case "ios":
		indexPrice = si.PriceIos
	case "webapp":
		indexPrice = si.PriceWebapp
	case "mobileweb":
		indexPrice = si.PriceMobileweb
	case "grabfresh":
		indexPrice = si.PriceGrabfresh
	}

	promoPrice := str.MaybeString(pp.Price.PromoPrice).Float64()

	// sync data if there is price difference
	if promoPrice != indexPrice {

		// and index has promotion, price has promotion, or both
		if si.PromotionType != "" || len(pp.Promotions) > 0 {
			return true, "price_and_promotion"
		}
	}

	return false, ""
}
