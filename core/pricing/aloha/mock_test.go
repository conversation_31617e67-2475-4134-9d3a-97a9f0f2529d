package aloha

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"happyfresh.io/calculator/lib/rpc"
)

func TestMockPing(t *testing.T) {
	assert := assert.New(t)
	c := &CalculatorClientMock{}

	result, err := c.<PERSON>(context.Background(), &rpc.NullRequest{})

	assert.Nil(result)
	assert.NoError(err)
}

func TestMockGetStockItemsPriceAndPromotion(t *testing.T) {
	assert := assert.New(t)
	c := &CalculatorClientMock{}

	result, err := c.GetStockItemsPriceAndPromotion(context.Background(), nil)

	assert.Nil(result)
	assert.NoError(err)
}

func TestMockGetStockItemsPriceAndPromotionChannel(t *testing.T) {
	assert := assert.New(t)
	c := &CalculatorClientMock{}

	result, err := c.GetStockItemsPriceAndPromotionChannel(context.Background(), nil)

	assert.Nil(result)
	assert.NoError(err)
}

func TestMockGetPromotedProductCodes(t *testing.T) {
	assert := assert.New(t)
	c := &CalculatorClientMock{}

	result, err := c.GetPromotedProductCodes(context.Background(), nil)

	assert.Equal(&rpc.ProductCodes{ProductCodes: []string{}}, result)
	assert.NoError(err)
}

func TestMockGetPromotedBundle(t *testing.T) {
	assert := assert.New(t)
	c := &CalculatorClientMock{}

	result, err := c.GetPromotedBundle(context.Background(), nil)

	assert.Equal(&rpc.ProductCodes{ProductCodes: []string{}}, result)
	assert.NoError(err)
}

func TestMockGetPromotionDetail(t *testing.T) {
	assert := assert.New(t)
	c := &CalculatorClientMock{}

	result, err := c.GetPromotionDetail(context.Background(), nil)

	assert.Nil(result)
	assert.NoError(err)
}
