package aloha

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsMpqEligible(t *testing.T) {
	type param struct {
		redisClient   string
		key           string
		value         string
		clientType    string
		clientVersion string
	}

	for _, c := range []struct {
		name      string
		p         *param
		expect    bool
		errExpect error
	}{
		{
			"Return android mpq eligible",
			&param{
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				"minimum_mpq_versions",
				"{\"webapp\":\"0.0.0\",\"android\":\"3.23\",\"ios\":\"3.23\",\"dana\":\"0.0.0\"}",
				"android",
				"10",
			},
			true,
			nil,
		},
		{
			"Return android mpq not eligible",
			&param{
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				"minimum_mpq_versions",
				"{\"webapp\":\"0.0.0\",\"android\":\"3.23\",\"ios\":\"3.23\",\"dana\":\"0.0.0\"}",
				"android",
				"3.12",
			},
			false,
			nil,
		},
		{
			"Return ios mpq not eligible",
			&param{
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				"minimum_mpq_versions",
				"{\"webapp\":\"0.0.0\",\"android\":\"3.23\",\"ios\":\"3.23\",\"dana\":\"0.0.0\"}",
				"ios",
				"3.12",
			},
			false,
			nil,
		},
		{
			"Return is mpq eligible for webapp",
			&param{
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				"minimum_mpq_versions",
				"{\"webapp\":\"0.0.0\",\"android\":\"3.23\",\"ios\":\"3.23\",\"dana\":\"0.0.0\"}",
				"webapp",
				"3.10",
			},
			true,
			nil,
		},
		{
			"Return mpq status if empty minimum version",
			&param{
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				"minimum_mpq_versions",
				"{\"webapp\":\"0.0.0\",\"android\":\"3.23\",\"ios\":\"3.23\",\"dana\":\"0.0.0\"}",
				"android",
				"",
			},
			false,
			nil,
		},
		{
			"Return mpq status if empty key from redis",
			&param{
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				"minimum_mpq_versions_failed",
				"{\"webapp\":\"0.0.0\",\"android\":\"3.23\",\"ios\":\"3.23\",\"dana\":\"0.0.0\"}",
				"ios",
				"10",
			},
			false,
			nil,
		},
		{
			"Return empty value from redis",
			&param{
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				"minimum_mpq_versions",
				"",
				"ios",
				"10",
			},
			false,
			nil,
		},
		{
			"Return mpq status if invalid value format",
			&param{
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				"minimum_mpq_versions",
				"/",
				"ios",
				"10",
			},
			false,
			nil,
		},
	} {
		t.Run(c.name, func(t *testing.T) {
			err := DialMPQ(c.p.redisClient)
			if err != nil {
				t.Error(err)
			}

			redisClient.Append(context.Background(), c.p.key, c.p.value)

			isMpqEligible := IsMpqEligible(context.Background(), c.p.clientType, c.p.clientVersion)

			defer redisClient.Del(context.Background(), c.p.key)

			assert.Equal(t, c.expect, isMpqEligible)

		})
	}
}
