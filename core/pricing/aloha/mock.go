package aloha

import (
	"context"

	"google.golang.org/grpc"
	calculatorRPC "happyfresh.io/calculator/lib/rpc"
)

type CalculatorClientMock struct {
	MockFunc                                   func(in *calculatorRPC.StockItemsPriceAndPromotionRequest) (*calculatorRPC.StockItemsPriceAndPromotion, error)
	GetPromotedProductCodesFunc                func(in *calculatorRPC.PromotedProductRequest) (*calculatorRPC.ProductCodes, error)
	GetPromotedBundleFunc                      func(in *calculatorRPC.PromotedBundleRequest) (*calculatorRPC.ProductCodes, error)
	GetStockItemsPriceAndPromotionQuantityFunc func(in *calculatorRPC.StockItemsPriceAndPromotionQuantityRequest) (*calculatorRPC.StockItemsPriceAndPromotionQuantity, error)
}

func (a *CalculatorClientMock) Ping(ctx context.Context, in *calculatorRPC.NullRequest, opts ...grpc.CallOption) (*calculatorRPC.PingResponse, error) {
	return nil, nil
}

func (a *CalculatorClientMock) GetStockItemsPriceAndPromotion(ctx context.Context, in *calculatorRPC.StockItemsPriceAndPromotionRequest, opts ...grpc.CallOption) (*calculatorRPC.StockItemsPriceAndPromotion, error) {
	if a.MockFunc == nil {
		return nil, nil
	}

	return a.MockFunc(in)
}

func (a *CalculatorClientMock) GetStockItemsPriceAndPromotionChannel(ctx context.Context, in *calculatorRPC.StockItemsPriceAndPromotionRequest, opts ...grpc.CallOption) (*calculatorRPC.StockItemsPriceAndPromotion, error) {
	if a.MockFunc == nil {
		return nil, nil
	}

	return a.MockFunc(in)
}

func (a *CalculatorClientMock) GetPromotedProductCodes(ctx context.Context, in *calculatorRPC.PromotedProductRequest, opts ...grpc.CallOption) (*calculatorRPC.ProductCodes, error) {
	if a.GetPromotedProductCodesFunc == nil {
		return &calculatorRPC.ProductCodes{ProductCodes: []string{}}, nil
	}

	return a.GetPromotedProductCodesFunc(in)
}

func (a *CalculatorClientMock) GetPromotedBundle(ctx context.Context, in *calculatorRPC.PromotedBundleRequest, opts ...grpc.CallOption) (*calculatorRPC.ProductCodes, error) {
	if a.GetPromotedBundleFunc == nil {
		return &calculatorRPC.ProductCodes{ProductCodes: []string{}}, nil
	}

	return a.GetPromotedBundleFunc(in)
}

func (a *CalculatorClientMock) GetPromotionDetail(ctx context.Context, in *calculatorRPC.PromotionDetailRequest, opts ...grpc.CallOption) (*calculatorRPC.PromotionDetailResponse, error) {
	return nil, nil
}

func (a *CalculatorClientMock) GetStockItemsPriceAndPromotionQuantity(ctx context.Context, in *calculatorRPC.StockItemsPriceAndPromotionQuantityRequest, opts ...grpc.CallOption) (*calculatorRPC.StockItemsPriceAndPromotionQuantity, error) {
	return nil, nil
}
