package aloha

import (
	"context"
	"encoding/json"
	"sync"

	"github.com/go-redis/redis/v8"
	"happyfresh.io/lib/log"
)

var (
	redisClient *redis.Client
	redisOnce   sync.Once
)

func DialMPQ(uri string) (err error) {
	redisOnce.Do(func() {
		var opt *redis.Options
		opt, err = redis.ParseURL(uri)
		redisClient = redis.NewClient(opt)
	})

	return
}

func IsMpqEligible(ctx context.Context, clientType string, clientVersion string) bool {
	minimumVersion := getMinimumMpqVersions(ctx)[clientType]
	if minimumVersion == "" {
		return false
	}
	if clientType != "android" && clientType != "ios" {
		return true
	}
	return versionOrdinal(clientVersion) >= versionOrdinal(minimumVersion)
}

func versionOrdinal(version string) string {
	const maxByte = 1<<8 - 1
	vo := make([]byte, 0, len(version)+8)
	j := -1
	for i := 0; i < len(version); i++ {
		b := version[i]
		if '0' > b || b > '9' {
			vo = append(vo, b)
			j = -1
			continue
		}
		if j == -1 {
			vo = append(vo, 0x00)
			j = len(vo) - 1
		}
		if vo[j] == 1 && vo[j+1] == '0' {
			vo[j+1] = b
			continue
		}
		if vo[j]+1 > maxByte {
			panic("VersionOrdinal: invalid version")
		}
		vo = append(vo, b)
		vo[j]++
	}
	return string(vo)
}

func getMinimumMpqVersions(ctx context.Context) map[string]string {
	versionsMap := make(map[string]string)

	if redisClient == nil {
		return versionsMap
	}
	b, err := redisClient.Get(ctx, "minimum_mpq_versions").Result()
	if err != nil {
		log.For("aloha", "GetMinimumMpqVersions").Info("could not find mpq versions")
		return versionsMap
	}

	err = json.Unmarshal([]byte(b), &versionsMap)
	if err != nil {
		log.For("aloha", "GetMinimumMpqVersions").Errorf("Error: %+v", err)
		return versionsMap
	}

	return versionsMap
}
