package response

import "happyfresh.io/search/core/datastore"

type Response struct {
	StockItems []datastore.StockItem
	Filters    Filters
}

func (r *Response) VariantIDs() (v []int64) {
	for _, s := range r.StockItems {
		v = append(v, s.VariantId)
	}
	return
}

type Filters struct {
	TaxonID []int   `json:"taxon_ids"`
	Brand   []Brand `json:"brand"`
}

type Brand struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}
