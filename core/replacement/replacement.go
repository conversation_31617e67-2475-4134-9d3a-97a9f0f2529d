package replacement

import (
	"context"
	"encoding/json"
	fmt "fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"happyfresh.io/chacha"
	r "happyfresh.io/chacha/redis"
	"happyfresh.io/lib/str"
)

//go:generate protoc --gofast_out=plugins=grpc:. replacementservice.proto

var (
	std = &Srv{
		httpClient: &http.Client{Timeout: 5 * time.Second},
	}
)

type ReplacementService interface {
	GetReplacement(context.Context, int64, int64) (map[int64]int64, error)
}

type Srv struct {
	httpClient *http.Client
	url        string
	redisDSN   string

	replacementGetter chacha.Getter

	onceReplacementGetter sync.Once
}

func (s *Srv) GetReplacement(ctx context.Context, stockLocationId int64, productId int64) (map[int64]int64, error) {
	s.onceReplacementGetter.Do(s.initReplacementGetter)
	key, err := cacheKeyEncoder(ctx, "replacement", stockLocationId, productId)
	if err != nil {
		return nil, err
	}

	replacementSuggestions := &ReplacementSuggestionsResponse{}
	err = s.replacementGetter.Get(ctx, key, replacementSuggestions)
	if err != nil {
		return nil, err
	}

	replacementSuggestionsMap := make(map[int64]int64, len(replacementSuggestions.Products))
	for _, replacementSuggestions := range replacementSuggestions.Products {
		replacementSuggestionsMap[replacementSuggestions.Id] = replacementSuggestions.VariantId
	}

	return replacementSuggestionsMap, nil
}

func (s *Srv) GetReplacementRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, query string, args ...interface{}) (proto.Message, error) {
		stockLocationId := str.MaybeString(args[0]).Int64()
		productId := str.MaybeString(args[1]).Int64()

		request, err := s.newRequest(fmt.Sprintf("/api/stock_locations/%d/products/replacement_suggestions/%d", stockLocationId, productId))
		if err != nil {
			return nil, err
		}

		response, err := s.httpClient.Do(request.WithContext(ctx))
		if err != nil {
			return nil, err
		}

		if response.StatusCode != http.StatusOK {
			return nil, errors.Wrap(errors.Errorf("Status <%v>", response.StatusCode), "[replacementservice] error fetching replacement suggestions")
		}

		b, err := ioutil.ReadAll(response.Body)
		defer response.Body.Close()
		if err != nil {
			return nil, errors.Wrap(err, "[replacementsuggestions] error fetching replacement suggestions")
		}

		replacementSuggestions := &ReplacementSuggestionsResponse{}
		err = json.Unmarshal(b, replacementSuggestions)
		if err != nil {
			return nil, errors.Wrap(err, "[replacementsuggestions] failed to decode response")
		}

		return replacementSuggestions, nil
	})
}

func (s *Srv) initReplacementGetter() {
	if s.replacementGetter == nil {
		s.replacementGetter = chacha.NewGetter("replacement-suggestions",
			chacha.WithKeyRoundTripper(s.GetReplacementRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(CacheKeyDecoder)),
		)
	}
}

func (s *Srv) newRequest(path string) (*http.Request, error) {
	request, err := http.NewRequest(http.MethodGet, fmt.Sprintf("%s%s", s.url, path), nil)
	if err != nil {
		return request, errors.Wrap(err, "[replacementsuggestions] failed to prepare request")
	}
	request.Header.Add("content-type", "application/json")

	return request, nil
}

func cacheKeyEncoder(ctx context.Context, s string, args ...interface{}) (string, error) {
	sb := strings.Builder{}
	sb.WriteString(s)
	for _, arg := range args {
		sb.WriteString(":")
		sb.WriteString(str.MaybeString(arg).String())
	}

	return sb.String(), nil
}

func CacheKeyDecoder(ctx context.Context, s string) (string, []interface{}, error) {
	keys := strings.Split(s, ":")

	var getterName string
	args := make([]interface{}, len(keys)-1)
	for i, key := range keys {
		if i == 0 {
			getterName = key
			continue
		}

		args[i-1] = key
	}

	return getterName, args, nil
}

func New(address string, redisDSN string) ReplacementService {
	srv := &Srv{
		url:        address,
		httpClient: std.httpClient,
		redisDSN:   redisDSN,
	}

	srv.replacementGetter = chacha.NewGetter("replacement-suggestions",
		chacha.WithCache("redis", r.WithRedisURL(redisDSN)),
		chacha.WithKeyRoundTripper(srv.GetReplacementRoundTripper(), 24*time.Hour),
		chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(CacheKeyDecoder)),
		chacha.WithTimeout(5*time.Second),
	)

	return srv
}
