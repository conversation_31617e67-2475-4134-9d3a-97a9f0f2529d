package promotionclient

import (
	"time"

	promo "happyfresh.io/product-promotion/lib/rpc/client"
)

var (
	PromoClient promo.Client
)

func ConnectPromotion(address string, useSecure bool) error {
	promoConnOpt := &promo.CircuitBreakerOption{
		Timeout:                      60 * time.Second,
		Interval:                     60 * time.Second,
		ConsecutiveFailuresThreshold: 5,
		RPM:                          300,
		FailureRatioThreshold:        0.6,
	}

	if useSecure {
		err := promo.NewConnectionSecure(address, promoConnOpt)
		if err != nil {
			return err
		}
	} else {
		err := promo.NewConnection(address, promoConnOpt)
		if err != nil {
			return err
		}
	}

	PromoClient = promo.NewClient()
	return nil
}
