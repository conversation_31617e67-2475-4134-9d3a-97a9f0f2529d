package sprinkles

import (
	"context"
	"encoding/json"
	fmt "fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"happyfresh.io/lib/log"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/lib/chacha"
)

//go:generate protoc --gofast_out=plugins=grpc:. sprinkles.proto
var (
	std = &srv{
		client: &http.Client{Timeout: 5 * time.Second},
	}
)

func cacheKeyEncoder(ctx context.Context, s string, args ...interface{}) (string, error) {
	sb := strings.Builder{}
	sb.WriteString(s)
	for _, arg := range args {
		sb.WriteString(":")
		sb.WriteString(str.MaybeString(arg).String())
	}

	return sb.String(), nil
}

func cacheKeyDecoder(ctx context.Context, s string) (string, []interface{}, error) {
	keys := strings.Split(s, ":")

	var getterName string
	args := make([]interface{}, len(keys)-1)
	for i, key := range keys {
		if i == 0 {
			getterName = key
			continue
		}

		args[i-1] = key
	}

	return getterName, args, nil
}

type SprinklesService interface {
	GetMarkupByDeviceID(context.Context, string) (string, error)
}

func GetMarkupByDeviceID(ctx context.Context, deviceID string) (string, error) {
	return std.GetMarkupByDeviceID(ctx, deviceID)
}

func GetAbMarkup(ctx context.Context, userID int, deviceID, orderNumber string) string {
	abMarkup := ""
	var err error

	if len(orderNumber) > 0 {
		abMarkup = datastore.GetAbMarkupByOrderNumber(ctx, orderNumber)
		return abMarkup
	}

	if userID > 0 {
		abMarkup = datastore.GetAbMarkupByUserID(ctx, userID)
	}

	if len(abMarkup) == 0 && len(deviceID) > 0 {
		abMarkup, err = GetMarkupByDeviceID(ctx, deviceID)
		if err != nil {
			log.ForContext(ctx, "sprinkles", "GetAbMarkup").Error(err)
			return ""
		}
	}

	return abMarkup
}

type srv struct {
	client *http.Client
	getter chacha.Getter
	url    string

	onceGetter sync.Once
}

func (s *srv) GetMarkupByDeviceID(ctx context.Context, deviceID string) (string, error) {
	s.onceGetter.Do(s.initGetter)
	markupResponse := &MarkupResponse{}

	key, err := cacheKeyEncoder(ctx, "markup-device_id", deviceID)
	if err != nil {
		return "", err
	}

	err = s.getter.Get(ctx, key, markupResponse)
	if err != nil {
		return "", errors.Wrapf(err, "[sprinkles] Error getting rec for deviceID: %s", deviceID)
	}

	return markupResponse.Markup, nil
}

func (s *srv) RoundTrip(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
	deviceID := fmt.Sprint(args[0])
	request, err := s.newRequest(deviceID)
	if err != nil {
		return nil, err
	}

	response, err := s.client.Do(request.WithContext(ctx))
	if err != nil {
		return nil, err
	}

	bod, err := io.ReadAll(response.Body)
	defer response.Body.Close()
	if err != nil {
		return nil, errors.Wrap(err, "[sprinkles] error searching sprinkles")
	}

	if response.StatusCode != http.StatusOK {
		return nil, errors.Errorf("[sprinkles] error searching sprinkles: [%s]", string(bod))
	}

	markupResponse := &MarkupResponse{}
	err = json.Unmarshal(bod, markupResponse)
	if err != nil {
		return nil, errors.Wrap(err, "[sprinkles] Failed to decode response")
	}

	return markupResponse, nil
}

func (s *srv) initGetter() {
	if s.getter == nil {
		s.getter = chacha.NewGetter("sprinkles",
			chacha.WithKeyRoundTripper(s, 1*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
		)
	}
}

func (s *srv) newRequest(deviceID string) (*http.Request, error) {
	request, err := http.NewRequest(http.MethodGet, fmt.Sprintf("%s%s", s.url, "/config/experiments?names=markup"), nil)
	if err != nil {
		return request, errors.Wrap(err, "[sprinkles] failed to prepare request")
	}

	request.Header.Add("content-type", "application/json")
	request.Header.Add("X-DEVICE-ID", deviceID)

	return request, nil
}

type Option func(srv *srv)

func Dial(address string, opts ...Option) SprinklesService {
	srv := &srv{
		url:    address,
		client: std.client,
	}

	for _, opt := range opts {
		opt(srv)
	}

	return srv
}

func InitDefault(opts ...Option) {
	for _, apply := range opts {
		apply(std)
	}
}

func WithAddress(address string) Option {
	return func(s *srv) {
		s.url = address
	}
}

func WithHTTPClient(c *http.Client) Option {
	return func(s *srv) {
		s.client = c
	}
}

func WithCacheOptions(name string, opts ...chacha.DialOption) Option {
	return func(s *srv) {
		s.getter = chacha.NewGetter("sprinkles",
			chacha.WithCache(name, opts...),
			chacha.WithKeyRoundTripper(s, 1*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(cacheKeyDecoder)),
		)
	}
}
