package sprinkles

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"happyfresh.io/search/config"
	"happyfresh.io/search/lib/chacha/redis"
)

func TestSprinklesCall(t *testing.T) {
	t.<PERSON><PERSON><PERSON>ow()
	InitDefault(
		WithAddress(config.SprinklesAddress()),
		WithCacheOptions("redis", redis.WithRedisURL(config.RedisDSN())),
	)

	assert := assert.New(t)

	assert.NotEmpty(std)
	ctx := context.Background()
	value, err := std.GetMarkupByDeviceID(ctx, "123")
	assert.NotEmpty(value)
	assert.NoError(err)

}

func TestMarshalKey(t *testing.T) {
	key, err := cacheKeyEncoder(context.Background(), "markup:device_id", "123")
	assert.NoError(t, err)
	assert.NotEmpty(t, key)
}
