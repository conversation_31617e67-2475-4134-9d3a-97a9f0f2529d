package translation

import (
	"context"
	"testing"

	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/stretchr/testify/assert"
)

func TestTranslate(t *testing.T) {
	testCases := []struct {
		name       string
		sourceLang string
		targetLang string
		text       string
		getFunc    func(ctx context.Context, key string, target proto.Message) error
		result     string
	}{
		{
			"TH translation with enabled source and target",
			"th",
			"en",
			"น้ำดื่ม",
			func(ctx context.Context, key string, target proto.Message) error {
				t := target.(*wrappers.StringValue)
				t.Value = "drinking water"

				return nil
			},
			"drinking water",
		},
		{
			"TH translation without TH characters",
			"th",
			"id",
			"drinking water",
			func(ctx context.Context, key string, target proto.Message) error {
				t := target.(*wrappers.StringValue)
				t.Value = "drinking water"

				return nil
			},
			"",
		},
		{
			"Translation with invalid source or target",
			"th",
			"id",
			"น้ำดื่ม",
			func(ctx context.Context, key string, target proto.Message) error {
				t := target.(*wrappers.StringValue)
				t.Value = "drinking water"

				return nil
			},
			"",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			assert := assert.New(t)

			translator, err := New(WithTranslateGetter(&cacheMock{
				GetFunc: testCase.getFunc,
			}))
			assert.NoError(err)

			ctx := context.Background()
			res, err := translator.Translate(ctx, testCase.sourceLang, testCase.targetLang, testCase.text)
			assert.NoError(err)
			assert.Equal(testCase.result, res)
		})
	}
}

type cacheMock struct {
	GetFunc     func(ctx context.Context, key string, target proto.Message) error
	GetManyFunc func(ctx context.Context, key []string, targets []proto.Message) error
}

func (c *cacheMock) Get(ctx context.Context, key string, target proto.Message) error {
	if c.GetFunc != nil {
		return c.GetFunc(ctx, key, target)
	}

	return nil
}

func (c *cacheMock) GetMany(ctx context.Context, key []string, targets []proto.Message) error {
	if c.GetManyFunc != nil {
		return c.GetManyFunc(ctx, key, targets)
	}

	return nil
}

func TestWithEnabledSourceAndTarget(t *testing.T) {
	testCases := []struct {
		name  string
		input map[string][]string
	}{
		{
			"Parse string to map of string to slice of string",
			map[string][]string{
				"key1": {"val1"},
				"key2": {"val2"},
				"key3": {"val3"},
			},
		},
		{
			"Use nil as map of string to slice of string",
			nil,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			assert := assert.New(t)

			tr := translator{
				enabledSourceAndTarget: defaultEnabledSourceAndTarget,
			}
			apply := WithEnabledSourceAndTarget(testCase.input)

			apply(&tr)

			if testCase.input == nil {
				assert.Equal(defaultEnabledSourceAndTarget, tr.enabledSourceAndTarget)
			} else {
				assert.Equal(testCase.input, tr.enabledSourceAndTarget)
			}
		})
	}
}

func TestWithEnabledSourceAndTargetInString(t *testing.T) {
	testCases := []struct {
		name       string
		input      string
		correctMap map[string][]string
	}{
		{
			"Parse string to map of string to slice of string",
			"key1:val1,val2;key2:val3,val4;key3:val5,val6",
			map[string][]string{
				"key1": {"val1", "val2"},
				"key2": {"val3", "val4"},
				"key3": {"val5", "val6"},
			},
		},
		{
			"Parse key with empty value to map of string to slice of string",
			"key1;key2:val2",
			map[string][]string{
				"key2": {"val2"},
			},
		},
		{
			"Parse empty string to map of string to slice of string",
			"",
			map[string][]string{},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			assert := assert.New(t)

			tr := translator{}
			apply := WithEnabledSourceAndTargetInString(testCase.input)

			apply(&tr)

			assert.Equal(len(testCase.correctMap), len(tr.enabledSourceAndTarget))

			for key, value := range tr.enabledSourceAndTarget {
				assert.Equal(testCase.correctMap[key], value)
			}
		})
	}
}
