package translation

import "context"

type TranslatorMock struct {
	TranslateFunc func(ctx context.Context, termCountryCode string, resultCountryCode string, term string) (result string, err error)
}

func (t *TranslatorMock) Translate(ctx context.Context, termCountryCode string, resultCountryCode string, term string) (result string, err error) {
	if t.TranslateFunc != nil {
		return t.TranslateFunc(ctx, termCountryCode, resultCountryCode, term)
	}

	return "", nil
}
