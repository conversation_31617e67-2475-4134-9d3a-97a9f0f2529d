package translation

import (
	"context"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/translate"
	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/ptypes/wrappers"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/ext"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"happyfresh.io/chacha"
	"happyfresh.io/chacha/redis"
	"happyfresh.io/lib/str"
)

var (
	awsRegion    = "ap-southeast-1"
	cacheName    = "translation"
	cacheKeyName = "aws"

	defaultEnabledSourceAndTarget = map[string][]string{
		str.TH: {str.EN},
	}
)

type Translator interface {
	Translate(ctx context.Context, termCountryCode string, resultCountryCode string, term string) (result string, err error)
}

type translator struct {
	awsTranslateClient *translate.Translate

	cacheKeyEncoder chacha.EncodeKeyFunc
	translateGetter chacha.Getter

	enabledSourceAndTarget map[string][]string
}

func (t *translator) Translate(ctx context.Context, sourceLanguageCode string, targetLanguageCode string, text string) (result string, err error) {
	sourceLanguageCode = strings.ToLower(sourceLanguageCode)
	targetLanguageCode = strings.ToLower(targetLanguageCode)

	if !t.isTranslationEnabled(sourceLanguageCode, targetLanguageCode) {
		return
	}

	if sourceLanguageCode == str.TH && !isThai(text) {
		return
	}

	if t.translateGetter != nil {
		key, err := t.cacheKeyEncoder(ctx, cacheKeyName, sourceLanguageCode, targetLanguageCode, text)
		if err != nil {
			return "", err
		}

		res := &wrappers.StringValue{}
		if err := t.translateGetter.Get(ctx, key, res); err != nil {
			return "", err
		}

		return res.GetValue(), nil
	}

	return t.translateRequest(ctx, sourceLanguageCode, targetLanguageCode, text)
}

func (t *translator) translateRequest(ctx context.Context, sourceLanguageCode string, targetLanguageCode string, text string) (result string, err error) {
	span, _ := tracer.StartSpanFromContext(ctx, "aws.translate", []ddtrace.StartSpanOption{
		tracer.SpanType(ext.SpanTypeHTTP),
		tracer.Tag("aws.translate.sourcelang", sourceLanguageCode),
		tracer.Tag("aws.translate.targetlang", targetLanguageCode),
		tracer.Tag("aws.translate.text", text),
	}...)
	defer span.Finish()

	output, err := t.awsTranslateClient.TextWithContext(ctx, &translate.TextInput{
		SourceLanguageCode: &sourceLanguageCode,
		TargetLanguageCode: &targetLanguageCode,
		Text:               &text,
	})
	if err != nil {
		return
	}

	if output != nil {
		result = strings.ToLower(*output.TranslatedText)
		span.SetTag("aws.translate.result", result)
	}

	return
}

func (t *translator) translateRoundTripper(ctx context.Context, key string, args ...interface{}) (result proto.Message, err error) {
	sourceLanguageCode := str.MaybeString(args[0]).String()
	targetLanguageCode := str.MaybeString(args[1]).String()
	text := str.MaybeString(args[2]).String()

	translated, err := t.translateRequest(ctx, sourceLanguageCode, targetLanguageCode, text)
	if err != nil {
		return nil, err
	}

	result = &wrappers.StringValue{Value: translated}
	return
}

func (t *translator) isTranslationEnabled(source, target string) bool {
	enabledTargets := t.enabledSourceAndTarget[source]
	if enabledTargets != nil {
		for _, enabledTarget := range enabledTargets {
			if enabledTarget == target {
				return true
			}
		}
	}

	return false
}

func New(opts ...Option) (t Translator, err error) {
	ts := &translator{
		cacheKeyEncoder:        chacha.DefaultKeyEncoder,
		enabledSourceAndTarget: defaultEnabledSourceAndTarget,
	}

	for _, apply := range opts {
		apply(ts)
	}

	sess, _ := session.NewSession(&aws.Config{
		Region: aws.String(awsRegion),
	})
	ts.awsTranslateClient = translate.New(sess)

	t = ts
	return
}

type Option func(t *translator)

// WithCacheKeyEncoder use defined encode key function
func WithCacheEncodeKeyFunc(encodeKeyFunc chacha.EncodeKeyFunc) Option {
	return func(t *translator) {
		if encodeKeyFunc != nil {
			t.cacheKeyEncoder = encodeKeyFunc
		}
	}
}

// WithCachedTranslate use cache on endpoint Translate
func WithCachedTranslate(redisDSN string, expiresIn time.Duration) Option {
	return func(t *translator) {
		t.translateGetter = chacha.NewGetter(
			cacheName,
			chacha.WithCache("redis", redis.WithRedisURL(redisDSN), redis.WithLWV()),
			chacha.WithKeyRoundTripper(chacha.KeyRoundTripperFunc(t.translateRoundTripper), expiresIn),
		)
	}
}

// WithEnabledSourceAndTarget use map to define enabled translation source and target
func WithEnabledSourceAndTarget(m map[string][]string) Option {
	return func(t *translator) {
		if m != nil {
			t.enabledSourceAndTarget = m
		}
	}
}

// WithEnabledSourceAndTargetInString use map to define enabled translation source and target
func WithEnabledSourceAndTargetInString(s string) Option {
	return func(t *translator) {
		m := make(map[string][]string)

		for _, scScplit := range strings.Split(s, ";") {
			cl := strings.Split(scScplit, ":")

			if len(cl) > 1 {
				m[cl[0]] = strings.Split(cl[1], ",")
			}
		}

		t.enabledSourceAndTarget = m
	}
}

// WithTranslateGetter use defined cache definition on endpoint Translate
func WithTranslateGetter(cache chacha.Getter) Option {
	return func(t *translator) {
		t.translateGetter = cache
	}
}
