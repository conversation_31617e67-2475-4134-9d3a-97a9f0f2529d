package nullrecommendation

import (
	"context"
	"encoding/json"
	fmt "fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/pkg/errors"
)

//go:generate protoc --gofast_out=plugins=grpc:. nullrecommendationservice.proto

var (
	std = &Srv{
		httpClient: &http.Client{Timeout: 5 * time.Second},
	}
)

type NullRecommendationService interface {
	GetRecommendationProducts(context.Context, map[int64]float64) (map[int64]int64, error)
}

type Srv struct {
	httpClient *http.Client
	url        string
}

func New(address string) NullRecommendationService {
	srv := &Srv{
		url:        address,
		httpClient: std.httpClient,
	}

	return srv
}

func (s *Srv) GetRecommendationProducts(ctx context.Context, input map[int64]float64) (map[int64]int64, error) {
	products := []*NullRecommendationProductRequest{}
	for k, v := range input {
		products = append(products, &NullRecommendationProductRequest{
			ProductId: k,
			Score:     v,
		})
	}

	payload := &NullRecommendationRequest{
		Products: products,
	}

	bodyReq, _ := json.Marshal(payload)
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/products/", s.url), strings.NewReader(string(bodyReq)))
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := s.httpClient.Do(req.WithContext(ctx))
	if err != nil {
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		return nil, errors.Wrap(errors.Errorf("Status <%v>", res.StatusCode), "[nullrecommendationservice] error null recommendation")
	}

	b, err := ioutil.ReadAll(res.Body)
	defer res.Body.Close()
	if err != nil {
		return nil, errors.Wrap(err, "[nullrecommendationservice] error fetching null recommendation")
	}

	nullRecommendations := &NullRecommendationResponse{}
	err = json.Unmarshal(b, nullRecommendations)
	if err != nil {
		return nil, errors.Wrap(err, "[nullrecommendationservice] failed to decode response")
	}

	nullRecommendationsMap := make(map[int64]int64, len(nullRecommendations.Products))
	for _, p := range nullRecommendations.Products {
		nullRecommendationsMap[p.ProductId] = p.VariantId
	}

	return nullRecommendationsMap, nil
}
