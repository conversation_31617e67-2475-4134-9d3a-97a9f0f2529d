package taxon

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"happyfresh.io/chacha"
	r "happyfresh.io/chacha/redis"
	"happyfresh.io/lib/str"
	"happyfresh.io/search/core/index"
)

//go:generate protoc --gofast_out=plugins=grpc:. taxonservice.proto

var (
	std = &Srv{
		httpClient: &http.Client{Timeout: 5 * time.Second},
	}
)

type TaxonService interface {
	GetRank(context.Context, int64, string) (map[int64]int64, error)
	GetUnavailableTaxon(ctx context.Context, stockLocationId int64, countryCode string, taxonIds []int64) (map[int64]bool, error)
}

type Srv struct {
	httpClient *http.Client
	url        string
	redisDSN   string

	elasticsearchClient index.Client

	rankGetter         chacha.Getter
	productCountGetter chacha.Getter

	onceRankGetter         sync.Once
	onceProductCountGetter sync.Once
}

func (s *Srv) GetRank(ctx context.Context, stockLocationId int64, userId string) (map[int64]int64, error) {
	s.onceRankGetter.Do(s.initRankGetter)
	key, err := cacheKeyEncoder(ctx, "ranks", stockLocationId, userId)
	if err != nil {
		return nil, err
	}

	taxonRank := &TaxonRankResponse{}
	err = s.rankGetter.Get(ctx, key, taxonRank)
	if err != nil {
		return nil, err
	}

	taxonRankMap := make(map[int64]int64, len(taxonRank.Ranks))
	for _, taxonRank := range taxonRank.Ranks {
		taxonRankMap[taxonRank.TaxonId] = taxonRank.Rank
	}

	return taxonRankMap, nil
}

func (s *Srv) GetUnavailableTaxon(ctx context.Context, stockLocationId int64, countryCode string, taxonIds []int64) (map[int64]bool, error) {
	s.onceProductCountGetter.Do(s.initProductCountGetter)
	keys := make([]string, len(taxonIds))
	targets := make([]proto.Message, len(taxonIds))
	for i, taxonId := range taxonIds {
		key, _ := cacheKeyEncoder(ctx, "product-count", stockLocationId, countryCode, taxonId)
		keys[i] = key
		targets[i] = &TaxonProductCountResponse{}
	}

	if err := s.productCountGetter.GetMany(ctx, keys, targets); err != nil {
		return nil, err
	}

	unavailableTaxonMap := make(map[int64]bool)
	for _, target := range targets {
		productCountResponse, _ := target.(*TaxonProductCountResponse)
		if productCountResponse.GetTaxonId() > 0 && productCountResponse.GetCount() == 0 {
			unavailableTaxonMap[productCountResponse.GetTaxonId()] = true
		}
	}

	return unavailableTaxonMap, nil
}

func (s *Srv) GetRankRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, query string, args ...interface{}) (proto.Message, error) {
		stockLocationId := str.MaybeString(args[0]).Int64()
		userId := str.MaybeString(args[1]).String()

		request, err := s.newRequest(fmt.Sprintf("/api/stock_locations/%d/taxon_preferences", stockLocationId), userId)
		if err != nil {
			return nil, err
		}

		response, err := s.httpClient.Do(request.WithContext(ctx))
		if err != nil {
			return nil, err
		}

		if response.StatusCode != http.StatusOK {
			return nil, errors.Wrap(errors.Errorf("Status <%v>", response.StatusCode), "[taxonrank] error fetching taxon rank")
		}

		b, err := ioutil.ReadAll(response.Body)
		defer response.Body.Close()
		if err != nil {
			return nil, errors.Wrap(err, "[taxonrank] error fetching taxon rank")
		}

		taxonRank := &TaxonRankResponse{}
		err = json.Unmarshal(b, taxonRank)
		if err != nil {
			return nil, errors.Wrap(err, "[taxonrank] failed to decode response")
		}

		return taxonRank, nil
	})
}

func (s *Srv) GetProductCountRoundTripper() chacha.KeyRoundTripper {
	return chacha.KeyRoundTripperFunc(func(ctx context.Context, query string, args ...interface{}) (proto.Message, error) {
		stockLocationId := str.MaybeString(args[0]).Int64()
		countryCode := str.MaybeString(args[1]).String()
		taxonId := str.MaybeString(args[2]).Int64()

		i, err := s.elasticsearchClient.Index(countryCode)
		if err != nil {
			return nil, err
		}

		searchOptions := []index.SearchOption{
			index.WithTermFilterInt64("stock_location_id", stockLocationId),
			index.WithTermFilterInt64("taxon_ids", taxonId),
			index.WithTermFilterString("in_stock", "true"),
			index.WithNoneTermFilterInt64("price", 0),
			index.WithNoneTermFilterString("sellable_items", "non_sellable"),
			index.WithResultFields("product_id"),
			index.WithPagination(0, 1),
		}
		productCountResponse, err := i.Search(ctx, searchOptions...)
		if err != nil {
			return nil, err
		}

		response := &TaxonProductCountResponse{}
		if productCountResponse.Meta != nil {
			response.TaxonId = taxonId
			response.Count = productCountResponse.Meta.TotalCount
		}

		return response, nil
	})
}

func (s *Srv) initRankGetter() {
	if s.rankGetter == nil {
		s.rankGetter = chacha.NewGetter("taxon-rank-v1",
			chacha.WithKeyRoundTripper(s.GetRankRoundTripper(), 24*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(CacheKeyDecoder)),
		)
	}
}

func (s *Srv) initProductCountGetter() {
	if s.productCountGetter == nil {
		s.productCountGetter = chacha.NewGetter("taxon-product-count",
			chacha.WithKeyRoundTripper(s.GetProductCountRoundTripper(), 2*time.Hour),
			chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(CacheKeyDecoder)),
		)
	}
}

func (s *Srv) newRequest(path, query string) (*http.Request, error) {
	request, err := http.NewRequest(http.MethodGet, fmt.Sprintf("%s%s", s.url, path), nil)
	if err != nil {
		return request, errors.Wrap(err, "[taxonrank] failed to prepare request")
	}
	request.Header.Add("content-type", "application/json")

	if len(query) > 0 {
		q := request.URL.Query()
		q.Add("user_id", strings.ToLower(query))
		request.URL.RawQuery = q.Encode()
	}

	return request, nil
}

type Option func(srv *Srv)

func New(address string, redisDSN string, opts ...Option) TaxonService {
	srv := &Srv{
		url:        address,
		httpClient: std.httpClient,
		redisDSN:   redisDSN,
	}

	cbSetting := chacha.CBSetting{
		Interval: time.Minute,
		Timeout:  time.Minute,
		IsSuccessful: func(err error) bool {
			if err == nil {
				return true
			}

			re := regexp.MustCompile(`<(.*)>`)
			match := re.FindStringSubmatch(err.Error())
			if len(match) <= 1 {
				return false
			}

			statusCode, err := strconv.Atoi(match[1])
			if err != nil {
				return false
			}

			if statusCode == http.StatusBadRequest || statusCode == http.StatusNotFound || statusCode == http.StatusNotImplemented {
				return true
			}

			return false
		},
	}

	srv.rankGetter = chacha.NewGetter("taxon-rank-v1",
		chacha.WithCache("redis", r.WithRedisURL(redisDSN)),
		chacha.WithKeyRoundTripper(srv.GetRankRoundTripper(), 24*time.Hour),
		chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(CacheKeyDecoder)),
		chacha.WithTimeout(5*time.Second),
		chacha.WithCircuitBreaker(cbSetting),
	)

	srv.productCountGetter = chacha.NewGetter("taxon-product-count",
		chacha.WithCache("redis", r.WithRedisURL(redisDSN)),
		chacha.WithKeyRoundTripper(srv.GetProductCountRoundTripper(), 2*time.Hour),
		chacha.WithKeyEncoding(chacha.EncodeKeyFunc(cacheKeyEncoder), chacha.DecodeKeyFunc(CacheKeyDecoder)),
	)

	for _, opt := range opts {
		opt(srv)
	}

	return srv
}

func WithElasticsearchClient(client index.Client) Option {
	return func(s *Srv) {
		s.elasticsearchClient = client
	}
}

func WithRankGetter(getter chacha.Getter) Option {
	return func(s *Srv) {
		s.rankGetter = getter
	}
}

func WithProductCountGetter(getter chacha.Getter) Option {
	return func(s *Srv) {
		s.productCountGetter = getter
	}
}

func cacheKeyEncoder(ctx context.Context, s string, args ...interface{}) (string, error) {
	sb := strings.Builder{}
	sb.WriteString(s)
	for _, arg := range args {
		sb.WriteString(":")
		sb.WriteString(str.MaybeString(arg).String())
	}

	return sb.String(), nil
}

func CacheKeyDecoder(ctx context.Context, s string) (string, []interface{}, error) {
	keys := strings.Split(s, ":")

	var getterName string
	args := make([]interface{}, len(keys)-1)
	for i, key := range keys {
		if i == 0 {
			getterName = key
			continue
		}

		args[i-1] = key
	}

	return getterName, args, nil
}
