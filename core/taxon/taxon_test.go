package taxon_test

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"happyfresh.io/search/core/taxon"
)

func TestGetRank(t *testing.T) {
	for _, tc := range []struct {
		name          string
		getFunc       func(context.Context, string, proto.Message) error
		expect        map[int64]int64
		expectedError bool
	}{
		{
			"With expected response",
			func(ctx context.Context, key string, target proto.Message) error {
				r := target.(*taxon.TaxonRankResponse)
				r.Ranks = []*taxon.Rank{
					{
						TaxonId: 2,
						Rank:    1,
					},
					{
						TaxonId: 1,
						Rank:    2,
					},
				}

				return nil
			},
			map[int64]int64{2: 1, 1: 2},
			false,
		},
		{
			"With error",
			func(ctx context.Context, key string, target proto.Message) error {
				return errors.New("Error get")
			},
			nil,
			true,
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			taxonService := taxon.New(
				"",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				taxon.WithRankGetter(&cacheMock{GetFunc: tc.getFunc}),
			)

			taxonRankMap, err := taxonService.GetRank(ctx, 1, "user")
			if err != nil && convey.ShouldBeTrue(tc.expectedError) != "" {
				t.Error(err)
			}
			if err == nil && convey.ShouldBeFalse(tc.expectedError) != "" {
				t.Error(err)
			}

			if tc.expectedError {
				return
			}

			msg := convey.ShouldResemble(taxonRankMap, tc.expect)
			if msg != "" {
				t.Error(msg)
			}
		})
	}
}

func TestRoundTrip(t *testing.T) {

	for _, tc := range []struct {
		name       string
		serverMock func() *httptest.Server
		expect     *taxon.TaxonRankResponse
		expectErr  bool
	}{
		{
			"With expected response",
			func() *httptest.Server {
				handler := http.NewServeMux()
				handler.HandleFunc(
					"/api/stock_locations/1/taxon_preferences",
					func(w http.ResponseWriter, r *http.Request) {
						resp := taxon.TaxonRankResponse{
							Ranks: []*taxon.Rank{
								{TaxonId: 1, Rank: 1},
							},
						}

						b, _ := json.Marshal(resp)
						_, _ = w.Write(b)
					})

				srv := httptest.NewServer(handler)

				return srv
			},
			&taxon.TaxonRankResponse{
				Ranks: []*taxon.Rank{
					{TaxonId: 1, Rank: 1},
				},
			},
			false,
		}, {
			"With expected error status not ok",
			func() *httptest.Server {
				handler := http.NewServeMux()
				handler.HandleFunc(
					"/api/stock_locations/1/taxon_preferences",
					func(w http.ResponseWriter, r *http.Request) {
						w.WriteHeader(http.StatusInternalServerError)
					})

				srv := httptest.NewServer(handler)

				return srv
			},
			nil,
			true,
		}, {
			"With error invalid response",
			func() *httptest.Server {
				handler := http.NewServeMux()
				handler.HandleFunc(
					"/api/stock_locations/1/taxon_preferences",
					func(w http.ResponseWriter, r *http.Request) {
						_, _ = w.Write([]byte{})
					})

				srv := httptest.NewServer(handler)

				return srv
			},
			nil,
			true,
		}, {
			"With expected error timeout",
			func() *httptest.Server {
				handler := http.NewServeMux()
				handler.HandleFunc(
					"/api/stock_locations/1/taxon_preferences",
					func(w http.ResponseWriter, r *http.Request) {
						time.Sleep(10 * time.Second)
						_, _ = w.Write([]byte{})
					})

				srv := httptest.NewServer(handler)

				return srv
			},
			nil,
			true,
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.serverMock().Close()

			ctx := context.Background()
			service := taxon.New(
				tc.serverMock().URL,
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				// taxon.WithHttpClient(&clientMock{doFunc: tc.doFunc}),
			)

			taxonService, _ := service.(*taxon.Srv)
			rankRoundTripper := taxonService.GetRankRoundTripper()
			response, err := rankRoundTripper.RoundTrip(ctx, "", "1", "1")
			if err != nil && convey.ShouldBeTrue(tc.expectErr) != "" {
				t.Error(err)
			}

			if err == nil && convey.ShouldBeFalse(tc.expectErr) != "" {
				t.Errorf("should be error")
			}

			if tc.expectErr {
				return
			}

			result := response.(*taxon.TaxonRankResponse)
			assert := convey.ShouldResemble(result, tc.expect)
			if assert != "" {
				t.Error(assert)
			}

		})
	}

}

func TestCacheKeyDecode(t *testing.T) {
	t.Run("test", func(t *testing.T) {
		getterName, args, err := taxon.CacheKeyDecoder(context.Background(), "getter:args1:args2")
		if err != nil {
			t.Error(err)
		}
		assert := convey.ShouldEqual(getterName, "getter")
		if assert != "" {
			t.Error(assert)
		}
		assert = convey.ShouldResemble(args, []interface{}{"args1", "args2"})
		if assert != "" {
			t.Error(assert)
		}
	})
}

type cacheMock struct {
	GetFunc     func(ctx context.Context, key string, target proto.Message) error
	GetManyFunc func(ctx context.Context, key []string, targets []proto.Message) error
}

func (c *cacheMock) Get(ctx context.Context, key string, target proto.Message) error {
	if c.GetFunc != nil {
		return c.GetFunc(ctx, key, target)
	}

	return nil
}

func (c *cacheMock) GetMany(ctx context.Context, key []string, targets []proto.Message) error {
	if c.GetManyFunc != nil {
		return c.GetManyFunc(ctx, key, targets)
	}

	return nil
}

func TestGetUnavailableTaxon(t *testing.T) {
	testCases := []struct {
		name            string
		getManyFunc     func(context.Context, []string, []proto.Message) error
		stockLocationId int64
		countryCode     string
		taxonIds        []int64
		expect          map[int64]bool
		expectedError   bool
	}{
		{
			"With expected response",
			func(ctx context.Context, keys []string, targets []proto.Message) error {
				for i := range targets {
					pc := targets[i].(*taxon.TaxonProductCountResponse)
					pc.TaxonId = 2
					pc.Count = 0
				}

				return nil
			},
			3,
			"id",
			[]int64{1, 2, 3, 4, 5},
			map[int64]bool{2: true},
			false,
		},
		{
			"With error",
			func(ctx context.Context, keys []string, targets []proto.Message) error {
				return errors.New("Error get")
			},
			3,
			"id",
			[]int64{1, 2, 3, 4, 5},
			nil,
			true,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			ctx := context.Background()
			taxonService := taxon.New(
				"",
				os.Getenv("SSRV_REDIS_TEST_DSN"),
				taxon.WithProductCountGetter(&cacheMock{GetManyFunc: testCase.getManyFunc}),
			)

			assert := assert.New(t)

			unavailableTaxon, err := taxonService.GetUnavailableTaxon(ctx, testCase.stockLocationId, testCase.countryCode, testCase.taxonIds)
			if testCase.expectedError {
				assert.Error(err)
			}

			assert.Equal(testCase.expect, unavailableTaxon)
		})
	}
}
