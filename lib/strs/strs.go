package strs

import (
	"fmt"
	"strings"
)

type SKU string

func (s SKU) ToProductCode(countryCode string) string {
	countryCode = strings.ToUpper(countryCode)
	v := string(s)
	if strings.HasSuffix(v, "-"+countryCode) {
		v = strings.TrimSuffix(v, "-"+countryCode)
	}

	return v
}

type ProductCode string

func (p ProductCode) ToSKU(countryCode string) string {
	countryCode = strings.ToUpper(countryCode)

	v := string(p)
	if countryCode != "MY" {
		v = fmt.Sprintf("%s-%s", v, countryCode)
	}

	return v
}

func Interpolate(s string, i map[string]interface{}) string {
	pairs := []string{}
	for k, v := range i {
		pairs = append(pairs, ":"+k, fmt.Sprint(v))
	}
	return strings.NewReplacer(pairs...).Replace(s)
}
