package pq

import (
	"happyfresh.io/lib/log"
)

type StockItemID struct {
	ID int64
}

// NewStockItemID function
func NewStockItemID() *StockItemID {
	return &StockItemID{}
}

// GetStockItemID :nodoc:
func (s *StockItemID) GetStockItemID(queries string, iso string) ([]*StockItemID, error) {
	var stockItems []*StockItemID

	rows, err := db.Query(queries, iso)
	if err != nil {
		log.For("pq", "stock item").Error(err)
		return nil, err
	}

	for rows.Next() {
		s := &StockItemID{}
		if err := rows.Scan(
			&s.ID); err != nil {
			log.For("pq", "stock item").Error(err)
		}

		stockItems = append(stockItems, s)
	}

	return stockItems, nil
}
