package grpc

import (
	"context"
	"net/http"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	h2gStatus = map[int]codes.Code{
		http.StatusOK:                  codes.OK,
		http.StatusCreated:             codes.OK,
		http.StatusBadRequest:          codes.InvalidArgument,
		http.StatusUnauthorized:        codes.Unauthenticated,
		http.StatusForbidden:           codes.PermissionDenied,
		http.StatusNotFound:            codes.Unimplemented,
		http.StatusTooManyRequests:     codes.ResourceExhausted,
		http.StatusInternalServerError: codes.Internal,
		http.StatusBadGateway:          codes.Unavailable,
		http.StatusServiceUnavailable:  codes.Unavailable,
		http.StatusGatewayTimeout:      codes.Unavailable,
	}

	g2hStatus = map[codes.Code]int{
		codes.OK:                http.StatusOK,
		codes.InvalidArgument:   http.StatusBadRequest,
		codes.Unauthenticated:   http.StatusUnauthorized,
		codes.PermissionDenied:  http.StatusForbidden,
		codes.Unimplemented:     http.StatusNotFound,
		codes.ResourceExhausted: http.StatusTooManyRequests,
		codes.Internal:          http.StatusInternalServerError,
		codes.Unavailable:       http.StatusBadGateway,
		codes.NotFound:          http.StatusNotFound,
		codes.Canceled:          499,
	}
)

func StatusFromHTTP(httpStatusCode int) codes.Code {
	s, ok := h2gStatus[httpStatusCode]
	if !ok {
		return codes.Unknown
	}

	return s
}

func StatusToHTTP(statusCode codes.Code) int {
	s, ok := g2hStatus[statusCode]
	if !ok {
		return http.StatusBadGateway
	}

	return s
}

type StatusError struct {
	s *status.Status
	o error
}

func (se *StatusError) Error() string {
	return se.o.Error()
}

func (se *StatusError) Unwrap() error {
	return se.o
}

func (se *StatusError) GRPCStatus() *status.Status {
	if se.s == nil {
		se.s = status.New(codes.Unknown, "")
	}

	return se.s
}

func NewStatusError(code codes.Code, origin error) *StatusError {
	return &StatusError{
		s: status.New(code, origin.Error()),
		o: origin,
	}
}

func ErrorFrom(ctx context.Context, err error) error {
	origin := errors.Cause(err)
	if status.Code(origin) != codes.Unknown {
		return err
	}

	if origin == context.Canceled {
		return NewStatusError(codes.Canceled, err)
	}

	if ctx.Err() == context.Canceled {
		return NewStatusError(codes.Canceled, err)
	}

	return NewStatusError(codes.Internal, err)
}
