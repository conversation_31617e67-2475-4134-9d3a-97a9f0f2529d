package elastictrace

import (
	"fmt"
	"net/http"
	"net/url"
	"os"
	"testing"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/stretchr/testify/assert"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/ext"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/mocktracer"
)

func TestRoundTrip(t *testing.T) {
	assert := assert.New(t)

	u, err := url.Parse(os.Getenv("SSRV_INDEX_ES_SHARDS_TEST_ID"))
	if err != nil {
		t.Error(err)
	}

	pw, _ := u.User.Password()

	for _, tc := range []struct {
		name                string
		opts                []ClientOption
		expectedServiceName string
		expectedSampleRate  float64
		expectedStatusCode  string
	}{
		{
			"Add tags, gzip-ed",
			[]ClientOption{
				WithServiceName("search service"),
				WithAnalytics(true),
				WithAnalyticsRate(1.0),
				WithResourceNamer(quantize),
			},
			"search service",
			1.0,
			"200",
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			mt := mocktracer.Start()
			defer mt.Stop()

			rt := NewHTTPRoundTripper(tc.opts...)

			esclient, err := elasticsearch.NewClient(elasticsearch.Config{
				Addresses: []string{fmt.Sprintf("%s://%s", u.Scheme, u.Host)},
				Username:  u.User.Username(),
				Password:  pw,
				Transport: rt,
			})
			if err != nil {
				t.Error(err)
			}

			esclient.Info(
				esclient.Info.WithHeader(map[string]string{"Accept-Encoding": "gzip"}),
				esclient.Info.WithFilterPath("-**._ignored"),
			)

			span := mt.FinishedSpans()[0]
			assert.Equal(tc.expectedServiceName, span.Tag(ext.ServiceName))
			assert.Equal("elasticsearch", span.Tag(ext.SpanType))
			assert.Equal("GET /", span.Tag(ext.ResourceName))
			assert.Equal(tc.expectedSampleRate, span.Tag(ext.EventSampleRate))
			assert.Equal(http.MethodGet, span.Tag("elasticsearch.method"))
			assert.Equal("/", span.Tag("elasticsearch.url"))
			assert.Equal(tc.expectedStatusCode, span.Tag(ext.HTTPCode))
		})
	}
}
