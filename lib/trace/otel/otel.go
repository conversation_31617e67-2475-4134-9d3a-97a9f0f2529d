package otel

import (
	"context"
	"io/ioutil"
	"net"
	"net/http"
	"time"

	o "go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	st "go.opentelemetry.io/otel/sdk/trace"
	"happyfresh.io/lib/log"
)

var (
	DefaultExporter       st.SpanExporter
	DefaultMetricExporter metric.Exporter
)

type config struct {
	ApplicationName string
	Env             string
	Version         string
	SpanExporter    st.SpanExporter
	MetricExporter  metric.Exporter
	RuntimeMetric   *runtimeMetric
}

type Option func(*config)

func SetDefaultExporter(exporter st.SpanExporter) {
	DefaultExporter = exporter
}

func WithApplicationName(name string) Option {
	return func(c *config) {
		c.ApplicationName = name
	}
}

func WithEnv(env string) Option {
	return func(c *config) {
		c.Env = env
	}
}

func WithVersion(version string) Option {
	return func(c *config) {
		c.Version = version
	}
}

func WithECSAgentExporter(port string) Option {
	fallbackExporter := func(c *config) {
		c.SpanExporter = DefaultExporter
		c.MetricExporter = DefaultMetricExporter
	}
	req, err := http.NewRequest("GET", "http://***************/latest/meta-data/local-ipv4", nil)
	if err != nil {
		log.For("otel", "Exporter").Warn(err)
		return fallbackExporter
	}

	cctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	req = req.WithContext(cctx)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.For("otel", "Exporter").Warn(err)
		return fallbackExporter
	}

	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.For("otel", "Exporter").Warn(err)
		return fallbackExporter
	}

	host := string(b)
	if len(port) == 0 {
		port = "4317"
	}

	endpoint := net.JoinHostPort(host, port)

	spanExporter, err := otlptrace.New(
		context.Background(),
		otlptracegrpc.NewClient(
			otlptracegrpc.WithInsecure(),
			otlptracegrpc.WithEndpoint(endpoint),
		),
	)

	if err != nil {
		log.For("otel", "Exporter").Warn(err)
		return fallbackExporter
	}

	metricExporter, err := otlpmetricgrpc.New(
		context.Background(),
		otlpmetricgrpc.WithInsecure(),
		otlpmetricgrpc.WithEndpoint(endpoint),
	)

	if err != nil {
		log.For("otel", "Exporter").Warn(err)
		return fallbackExporter
	}

	return func(c *config) {
		c.SpanExporter = spanExporter
		c.MetricExporter = metricExporter
	}
}

func WithLocalAgentExporter(port string) Option {
	endpoint := net.JoinHostPort("localhost", port)

	spanExporter, _ := otlptrace.New(
		context.Background(),
		otlptracegrpc.NewClient(
			otlptracegrpc.WithInsecure(),
			otlptracegrpc.WithEndpoint(endpoint),
		),
	)

	metricExporter, _ := otlpmetricgrpc.New(
		context.Background(),
		otlpmetricgrpc.WithInsecure(),
		otlpmetricgrpc.WithEndpoint(endpoint),
	)

	return func(c *config) {
		c.SpanExporter = spanExporter
		c.MetricExporter = metricExporter
	}
}

func WithEnvSelectionExporter(env, port string) Option {
	if env != "dev" {
		return WithECSAgentExporter(port)
	}

	return WithLocalAgentExporter(port)
}

func Start(opts ...Option) (stop func(), err error) {
	cfg := &config{}

	for _, apply := range opts {
		apply(cfg)
	}

	ctx := context.Background()
	b := st.NewBatchSpanProcessor(cfg.SpanExporter)

	r, err := resource.New(
		ctx,
		resource.WithAttributes(
			attribute.String("service.name", cfg.ApplicationName),
			attribute.String("service.env", cfg.Env),
			attribute.String("service.version", cfg.Version),
		),
	)

	if err != nil {
		return nil, err
	}

	tp := st.NewTracerProvider(
		st.WithSpanProcessor(b),
		st.WithResource(r),
		st.WithSampler(st.AlwaysSample()),
	)

	o.SetTracerProvider(tp)
	o.SetTextMapPropagator(
		propagation.NewCompositeTextMapPropagator(
			propagation.TraceContext{},
			propagation.Baggage{},
		),
	)

	metricShutdown := func(ctx context.Context) {}
	if cfg.RuntimeMetric != nil {
		metricShutdown, err = cfg.RuntimeMetric.Start(ctx)
		if err != nil {
			return nil, err
		}
	}

	return func() {
		tp.Shutdown(ctx)
		metricShutdown(ctx)
	}, nil
}

func init() {
	DefaultExporter, _ = otlptrace.New(
		context.Background(),
		otlptracegrpc.NewClient(
			otlptracegrpc.WithInsecure(),
			otlptracegrpc.WithEndpoint("localhost:4317"),
		),
	)

	DefaultMetricExporter, _ = otlpmetricgrpc.New(
		context.Background(),
		otlpmetricgrpc.WithInsecure(),
		otlpmetricgrpc.WithEndpoint("localhost:4317"),
	)
}
