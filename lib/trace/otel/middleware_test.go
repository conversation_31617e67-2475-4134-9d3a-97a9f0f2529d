package otel

import (
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
)

func Test_Middleware_ChiRouterShouldBeLogProperly(t *testing.T) {
	err := testInitExporter()
	if err != nil {
		t.Error(err)
	}

	stop, err := testStart("search_test", "test")
	defer stop()
	if err != nil {
		t.Error(err)
	}

	router := chi.NewRouter()
	router.Use(Middleware("search_test", router))
	for _, tt := range []struct {
		name    string
		path    string
		message string
		status  int
	}{
		{"home", "/", "Success", 200},
		{"invalid", "/invalid", "Not Success", 500},
		{"not_found", "/not_found", "Not Founc", 404},
		{"path", "/user", "Success", 200},
	} {
		t.Run(tt.name, func(t *testing.T) {
			router.Get(tt.path, func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.status)
				w.Write([]byte(tt.message))
			})
			r := httptest.NewRequest(http.MethodGet, tt.path, nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, r)

			res := w.Result()
			_, err := ioutil.ReadAll(res.Body)
			if err != nil {
				t.Error(err)
			}
		})
	}
}
