package otel

import (
	"context"
	"time"

	"go.opentelemetry.io/contrib/instrumentation/runtime"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric/global"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
)

type runtimeMetric struct {
	config *config
}

func (r *runtimeMetric) Start(ctx context.Context) (func(context.Context), error) {
	reader := metric.NewPeriodicReader(
		r.config.MetricExporter,
		metric.WithInterval(1*time.Second),
	)

	res, err := resource.New(
		ctx,
		resource.WithAttributes(
			attribute.String("service.name", r.config.ApplicationName),
			attribute.String("service.env", r.config.Env),
			attribute.String("service.version", r.config.Version),
		),
	)

	if err != nil {
		return nil, err
	}

	provider := metric.NewMeterProvider(
		metric.WithResource(res),
		metric.WithReader(reader),
	)

	global.SetMeterProvider(provider)

	err = runtime.Start(runtime.WithMinimumReadMemStatsInterval(time.Second))
	if err != nil {
		return nil, err
	}

	return func(ctx context.Context) {
		provider.Shutdown(ctx)
	}, nil
}

func WithRuntimeMetric() Option {
	return func(c *config) {
		c.RuntimeMetric = &runtimeMetric{c}
	}
}
