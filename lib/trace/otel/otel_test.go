package otel

import (
	"context"
	"fmt"
	"testing"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
)

func testStart(name, env string) (func(), error) {
	return Start(
		WithApplicationName(name),
		WithEnv(env),
	)
}

func testInitExporter() error {
	exp, err := stdouttrace.New(stdouttrace.WithPrettyPrint())
	if err != nil {
		return fmt.Errorf("failed to initialize stdouttrace exporter: %w", err)
	}

	SetDefaultExporter(exp)

	return nil
}

func Test_Start_ShouldBeSuccessfull(t *testing.T) {
	stop, err := testStart("search_test", "test")
	defer stop()
	if err != nil {
		t.Error(err)
	}
}

func Test_Trace_ShouldPrintToStdoutWithSTDOUTExporter(t *testing.T) {
	err := testInitExporter()
	if err != nil {
		t.Error(err)
	}
	stop, err := testStart("search_test", "test")
	defer stop()
	if err != nil {
		t.Error(err)
	}

	tp := otel.Tracer("test_handler")
	_, span := tp.Start(context.TODO(), "test_handler")
	defer span.End()
}
