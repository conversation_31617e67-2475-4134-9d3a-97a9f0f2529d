package grpctrace

import (
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"

	middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	otelgrpc "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	ddgrpctrace "gopkg.in/DataDog/dd-trace-go.v1/contrib/google.golang.org/grpc"
)

func ChainUnaryClientInterceptor(dd, otel bool, others ...grpc.UnaryClientInterceptor) grpc.UnaryClientInterceptor {
	i := []grpc.UnaryClientInterceptor{}
	if dd {
		i = append(i, ddgrpctrace.UnaryClientInterceptor())
	}
	if otel {
		i = append(i, otelgrpc.UnaryClientInterceptor())
	}

	return middleware.ChainUnaryClient(append(i, others...)...)
}

func ChainUnaryServerIntercepetor(dd bool, otel bool, others ...grpc.UnaryServerInterceptor) grpc.UnaryServerInterceptor {
	i := []grpc.UnaryServerInterceptor{}
	if dd {
		i = append(i, ddgrpctrace.UnaryServerInterceptor(ddgrpctrace.NonErrorCodes(codes.InvalidArgument, codes.NotFound)))
	}

	if otel {
		i = append(i, otelgrpc.UnaryServerInterceptor())
	}

	return middleware.ChainUnaryServer(append(i, others...)...)
}
