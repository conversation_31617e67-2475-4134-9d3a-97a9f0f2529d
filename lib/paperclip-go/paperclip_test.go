package paperclip

import "testing"

type asset_ struct {
	id_        int64
	updatedAt_ int64
	fileName_  string
}

func (a asset_) ID() int64 {
	return a.id_
}

func (a asset_) UpdatedAt() int64 {
	return a.updatedAt_
}

func (a asset_) FileName() string {
	return a.fileName_
}

func TestPaperclip_URL(t *testing.T) {
	config := map[string]interface{}{
		"secret_key": "dummy",
		"cdn":        "https://test.local",
		"styles":     []string{"small", "large"},
		"attachment": map[string]string{
			"path": "attachments",
		},
		"class": map[string]string{
			"path": "spree/images",
		},
		"format": map[string]string{
			"hash_data": ":class/:attachment/:id/:style/:updated_at",
			"path":      "/t/:transform/:class/:attachment/:hash-:style.:extension",
		},
		"transform": map[string]string{
			"composition": "fit",
		},
	}

	p, err := New(config)
	if err != nil {
		t.Error(err)
	}

	for _, tt := range []struct {
		id        int64
		updatedAt int64
		fileName  string
		labels    []string
		want      []string
	}{
		{2868388, 1542364942, "738989414036.jpg", []string{}, []string{
			"https://test.local/t/s_small,c_fit/spree/images/attachments/31d0ace74994a5b98a4a780ebf5ed04da9abd7e2-original.jpg",
			"https://test.local/t/c_fit,s_small/spree/images/attachments/31d0ace74994a5b98a4a780ebf5ed04da9abd7e2-original.jpg",
		}},
		{2868388, 1542364942, "738989414036.jpg", []string{"pink"}, []string{
			"https://test.local/t/s_small,c_fit,l_pink/spree/images/attachments/31d0ace74994a5b98a4a780ebf5ed04da9abd7e2-original.jpg",
			"https://test.local/t/c_fit,s_small,l_pink/spree/images/attachments/31d0ace74994a5b98a4a780ebf5ed04da9abd7e2-original.jpg",
		}},
	} {
		a := asset_{tt.id, tt.updatedAt, tt.fileName}
		tMap := map[string]string{"s": "small"}

		if w := p.StyleURL(tMap, a, tt.labels); w != tt.want[0] && w != tt.want[1] {
			t.Errorf("want: %s or %s, got: %s", tt.want[0], tt.want[1], w)
		}
	}
}
