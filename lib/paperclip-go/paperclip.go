package paperclip // import "happyfresh.io/search/lib/paperclip-go"

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"net/url"
	"path/filepath"
	"strings"

	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"happyfresh.io/search/lib/strs"
)

const (
	OriginalStyle Style = "original"
)

type Paperclip interface {
	URL(Asset, []string) map[Style]string
	StyleURL(map[string]string, Asset, []string) string
}

type Style string

type Asset interface {
	ID() int64
	UpdatedAt() int64
	FileName() string
}

type Config map[string]interface{}

type standard struct {
	c cfg
}

type cfg struct {
	SecretKey  string   `mapstructure:"secret_key"`
	CDN        string   `mapstructure:"cdn"`
	Styles     []string `mapstructure:"styles"`
	Attachment struct {
		Path string `mapstructure:"path"`
		Name string `mapstructure:"name"`
	} `mapstructure:"attachment"`
	Class struct {
		Path string `mapstructure:"path"`
		Name string `mapstructure:"name"`
	} `mapstructure:"class"`
	Format struct {
		Path     string `mapstructure:"path"`
		HashData string `mapstructure:"hash_data"`
	} `mapstructure:"format"`
	Transform struct {
		Composition string `mapstructure:"composition"`
	} `mapstructure:"transform"`
}

func New(c Config) (Paperclip, error) {
	p := &standard{}
	err := mapstructure.Decode(c, &p.c)
	if err != nil {
		return nil, errors.Wrap(err, "[paperclip] Error while reading config")
	}

	return p, nil
}

func (p *standard) URL(a Asset, labels []string) map[Style]string {
	m := map[Style]string{}
	for _, s := range p.c.Styles {
		t := map[string]string{"s": s}

		m[Style(s)] = p.StyleURL(t, a, labels)
	}

	return m
}

func (p *standard) StyleURL(tMap map[string]string, a Asset, labels []string) string {
	ext := filepath.Ext(a.FileName())
	if len(ext) > 0 {
		ext = ext[1:]
	}

	tMap["c"] = p.c.Transform.Composition

	t := make([]string, 0, len(tMap))
	for k, v := range tMap {
		t = append(t, fmt.Sprintf("%s_%s", k, v))
	}

	for _, l := range labels {
		t = append(t, fmt.Sprintf("l_%s", l))
	}

	data := map[string]interface{}{
		"id":         a.ID(),
		"style":      "original",
		"updated_at": a.UpdatedAt(),
		"class":      p.c.Class.Path,
		"attachment": p.c.Attachment.Path,
		"extension":  ext,
		"transform":  strings.Join(t, ","),
	}

	data["hash"] = sha1HexDigest(
		[]byte(strs.Interpolate(p.c.Format.HashData, data)),
		[]byte(p.c.SecretKey),
	)

	return p.cdn(strs.Interpolate(p.c.Format.Path, data))
}

func (p *standard) cdn(path string) string {
	u, _ := url.Parse(p.c.CDN)
	u.Path = path
	return u.String()
}

func sha1HexDigest(data, key []byte) string {
	mac := hmac.New(sha1.New, key)
	_, _ = mac.Write(data)
	return hex.EncodeToString(mac.Sum(nil))
}
