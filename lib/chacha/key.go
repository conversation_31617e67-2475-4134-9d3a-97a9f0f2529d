package chacha

import (
	"context"
)

type KeyDecoder interface {
	Decode(context.Context, string) (string, []interface{}, error)
}

type KeyEncoder interface {
	Encode(context.Context, string, ...interface{}) (string, error)
}

type EncodeKeyFunc func(context.Context, string, ...interface{}) (string, error)

func (m EncodeKeyFunc) Encode(ctx context.Context, key string, args ...interface{}) (string, error) {
	return m(ctx, key, args...)
}

type DecodeKeyFunc func(context.Context, string) (string, []interface{}, error)

func (m DecodeKeyFunc) Decode(ctx context.Context, enc string) (string, []interface{}, error) {
	return m(ctx, enc)
}
