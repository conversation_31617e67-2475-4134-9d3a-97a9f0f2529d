package chacha

import (
	"context"
	"database/sql"
)

type QueryPreparrer interface {
	QueryRow(...interface{}) *sql.Row
	QueryRowContext(context.Context, ...interface{}) *sql.Row
	Query(...interface{}) (*sql.Rows, error)
	QueryContext(context.Context, ...interface{}) (*sql.Rows, error)
}

type unpreparred struct {
	db    *sql.DB
	query string
}

func (u *unpreparred) QueryRow(args ...interface{}) *sql.Row {
	return u.db.QueryRow(u.query, args...)
}
func (u *unpreparred) QueryRowContext(ctx context.Context, args ...interface{}) *sql.Row {
	return u.db.QueryRowContext(ctx, u.query, args...)

}
func (u *unpreparred) Query(args ...interface{}) (*sql.Rows, error) {
	return u.db.Query(u.query, args...)
}
func (u *unpreparred) QueryContext(ctx context.Context, args ...interface{}) (*sql.Rows, error) {
	return u.db.QueryContext(ctx, u.query, args...)
}
