package chacha // import "happyfresh.io/search/lib/chacha"

import (
	"context"
	"database/sql"
	"net/http"
	"sync"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
)

const (
	CacheDisabled CacheType = iota
	CacheEnabled
	CacheSlaveEnabled
)

var (
	cacheBackend = map[string]Dialer{}
	cacheMu      sync.RWMutex
)

type Cache interface {
	Get(context.Context, string, Sink) error
	GetMany(context.Context, []string, []Sink) error
}

type Dialer interface {
	Dial(string, GetterFunc, ...DialOption) Cache
	Sink(context.Context, interface{}) Sink
}

type DialOption interface{}

type CacheType uint8

type RowScanner func(r *sql.Row) (proto.Message, error)

type RowsScanner func(r *sql.Rows) (proto.Message, error)

type KeyRoundTripper interface {
	RoundTrip(context.Context, string, ...interface{}) (proto.Message, error)
}

type KeyRoundTripperFunc func(context.Context, string, ...interface{}) (proto.Message, error)

func (k KeyRoundTripperFunc) RoundTrip(ctx context.Context, key string, args ...interface{}) (proto.Message, error) {
	return k(ctx, key, args...)
}

type Option func(client *client)

type Getter interface {
	Get(ctx context.Context, key string, target proto.Message) error
	GetMany(ctx context.Context, key []string, targets []proto.Message) error
}

type Sink interface {
	SetProto(proto.Message, time.Time) error
	SetBytes([]byte, time.Time) error
	SetString(string, time.Time) error
}

type RequestBuilder interface {
	Build(string, ...interface{}) *http.Request
}

type GetterFunc func(context.Context, string, Sink) error

func (g GetterFunc) Get(ctx context.Context, key string, sink Sink) error {
	return g(ctx, key, sink)
}

func (g GetterFunc) GetMany(ctx context.Context, keys []string, sinks []Sink) error {
	for i, v := range keys {
		_ = g(ctx, v, sinks[i])
	}

	return nil
}

type client struct {
	name       string
	stmtCache  map[string]QueryPreparrer
	getter     GetterFunc
	cache      Cache
	Sink       func(context.Context, interface{}) Sink
	KeyDecoder KeyDecoder
	KeyEncoder KeyEncoder

	cacheOnce sync.Once
	mu        sync.RWMutex
}

func (c *client) Get(ctx context.Context, key string, target proto.Message) error {
	c.cacheOnce.Do(c.initializeCache)
	return c.cache.Get(ctx, key, c.Sink(ctx, target))
}

func (c *client) GetMany(ctx context.Context, keys []string, dests []proto.Message) error {
	c.cacheOnce.Do(c.initializeCache)
	targets := make([]Sink, len(dests))
	for i, v := range dests {
		targets[i] = c.Sink(ctx, v)
	}
	return c.cache.GetMany(ctx, keys, targets)
}

func (c *client) QueryRow(name string, args ...interface{}) *sql.Row {
	return c.stmt(name).QueryRow(args...)
}

func (c *client) QueryRowContext(ctx context.Context, name string, args ...interface{}) *sql.Row {
	return c.stmt(name).QueryRowContext(ctx, args...)
}

func (c *client) Query(name string, args ...interface{}) (*sql.Rows, error) {
	return c.stmt(name).Query(args...)
}

func (c *client) QueryContext(ctx context.Context, name string, args ...interface{}) (*sql.Rows, error) {
	return c.stmt(name).QueryContext(ctx, args...)
}

func (c *client) stmt(name string) QueryPreparrer {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.stmtCache[name]
}

func (c *client) initializeCache() {
	if c.cache == nil {
		c.cache = c.getter
		c.Sink = func(ctx context.Context, target interface{}) Sink {
			return &noCacheSink{target.(proto.Message)}
		}
	}
}

type noCacheSink struct {
	m proto.Message
}

func (n *noCacheSink) SetProto(s proto.Message, t time.Time) error {
	proto.Merge(n.m, s)
	return nil
}

func (n *noCacheSink) SetBytes(s []byte, t time.Time) error {
	return nil
}

func (n *noCacheSink) SetString(s string, t time.Time) error {
	return nil
}

func WithQueries(db *sql.DB, queries map[string]string) Option {
	return func(c *client) {
		c.mu.Lock()
		defer c.mu.Unlock()

		c.stmtCache = map[string]QueryPreparrer{}

		for k, query := range queries {
			c.stmtCache[k] = &unpreparred{db, query}
		}
	}
}

func WithRowScanner(scan RowScanner, expireIn time.Duration) Option {
	return func(c *client) {
		c.getter = GetterFunc(func(ctx context.Context, key string, sink Sink) error {
			query, args, err := c.KeyDecoder.Decode(ctx, key)
			if err != nil {
				return errors.Wrapf(err, "[chacha] Invalid key: %s", key)
			}

			m, err := scan(c.QueryRowContext(ctx, query, args...))
			if err != nil {
				return errors.Wrapf(err, "[chacha] Error getting from database: %s", key)
			}

			err = sink.SetProto(m, time.Now().Add(expireIn))
			if err != nil {
				return errors.Wrapf(err, "[chacha] Invalid value: %s", key)
			}

			return nil
		})
	}
}

func WithKeyRoundTripper(rt KeyRoundTripper, expiresIn time.Duration) Option {
	return func(c *client) {
		c.getter = GetterFunc(func(ctx context.Context, key string, sink Sink) error {
			key, args, err := c.KeyDecoder.Decode(ctx, key)
			if err != nil {
				return errors.Wrapf(err, "[chacha] Invalid key: %s", key)
			}

			m, err := rt.RoundTrip(ctx, key, args...)
			if err != nil {
				return errors.Wrapf(err, "[chacha] Error roundriping key: %s", key)
			}

			err = sink.SetProto(m, time.Now().Add(expiresIn))
			if err != nil {
				return errors.Wrapf(err, "[chacha] Invalid value: %s", key)
			}

			return nil
		})
	}
}

func WithRowsScanner(scan RowsScanner, expireIn time.Duration) Option {
	return func(c *client) {
		c.getter = GetterFunc(func(ctx context.Context, key string, sink Sink) error {
			query, args, err := c.KeyDecoder.Decode(ctx, key)
			if err != nil {
				return errors.Wrapf(err, "[chacha] Invalid key: %s", key)
			}

			r, err := c.QueryContext(ctx, query, args...)
			if err != nil {
				return errors.Wrapf(err, "[chacha] Error getting from database: %s", key)
			}

			m, err := scan(r)
			if err != nil {
				return errors.Wrapf(err, "[chacha] Error marshal result: %s", key)
			}

			err = sink.SetProto(m, time.Now().Add(expireIn))
			if err != nil {
				return errors.Wrapf(err, "[chacha] Invalid value: %s", key)
			}

			return nil
		})
	}
}

func WithCache(name string, options ...DialOption) Option {
	return func(c *client) {
		cacheMu.RLock()
		b, ok := cacheBackend[name]
		cacheMu.RUnlock()
		if !ok {
			panic(errors.Errorf("[chacha] Backend unknwon: %s", name))
		}

		c.cache = b.Dial(c.name, func(ctx context.Context, key string, sink Sink) error {
			return c.getter.Get(ctx, key, sink)
		}, options...)
		c.Sink = b.Sink
	}
}

func WithKeyEncoding(enc EncodeKeyFunc, dec DecodeKeyFunc) Option {
	return func(c *client) {
		c.KeyEncoder = enc
		c.KeyDecoder = dec
	}
}

func Register(name string, d Dialer) {
	cacheMu.Lock()
	defer cacheMu.Unlock()
	if _, ok := cacheBackend[name]; ok {
		panic(errors.Errorf("[chacha] Backend already registerd: %s", name))
	}

	cacheBackend[name] = d
}

func NewGetter(name string, options ...Option) Getter {
	c := &client{name: name}
	for _, apply := range options {
		if apply != nil {
			apply(c)
		}
	}

	return c
}
