// Code generated by "stringer -type=CacheType"; DO NOT EDIT.

package chacha

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[CacheDisabled-0]
	_ = x[CacheEnabled-1]
	_ = x[CacheSlaveEnabled-2]
}

const _CacheType_name = "CacheDisabledCacheEnabledCacheSlaveEnabled"

var _CacheType_index = [...]uint8{0, 13, 25, 42}

func (i CacheType) String() string {
	if i >= CacheType(len(_CacheType_index)-1) {
		return "CacheType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _CacheType_name[_CacheType_index[i]:_CacheType_index[i+1]]
}
