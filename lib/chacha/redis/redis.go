package redis // import "happyfresh.io/search/lib/chacha/redis"

import (
	"context"
	"hash/crc32"
	"os"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gogo/protobuf/proto"
	"github.com/pkg/errors"
	"happyfresh.io/search/lib/chacha"
	"happyfresh.io/search/lib/trace/redistrace.v8"
)

var (
	clientPool = map[uint32]*redis.Client{}
	coLock     sync.Mutex
	getScript  = redis.NewScript(`
	    local results = {};
		for i, k in ipairs(KEYS) do
			local val = redis.call("GET", k);
			local ttl = redis.call("TTL", k);
			results[i] = {val, ttl};
		end

		return results
	`)

	setScript = redis.NewScript(`
		for i, k in ipairs(KEYS) do
			local val = table.remove(ARGV, 1);
			local ttl = table.remove(ARGV, 1);
			redis.call("SET", k, val);
			redis.call("EXPIRE", k, ttl);
		end
	`)
)

type Option func(c *client)

type client struct {
	dialOption *redis.Options
	hostSum    uint32
}

type getter struct {
	c      *redis.Client
	name   string
	loader chacha.GetterFunc
}

func newRedisClient(sum uint32, o *redis.Options) *redis.Client {
	if sum == 0 {
		return redistrace.AddTraceHook(redis.NewClient(o)).(*redis.Client)
	}
	coLock.Lock()
	defer coLock.Unlock()
	c, ok := clientPool[sum]
	if ok {
		return c
	}

	c = redis.NewClient(o)
	c = redistrace.AddTraceHook(c).(*redis.Client)
	clientPool[sum] = c
	return c
}

func (g *getter) load(ctx context.Context, key string, m *sink) error {
	err := g.loader.Get(ctx, key, m)
	if err != nil {
		return errors.Wrap(err, "[chacha/redis] Failed to load")
	}

	return nil
}

func (g *getter) Get(ctx context.Context, key string, m chacha.Sink) error {
	s, ok := m.(*sink)
	if !ok {
		return errors.Errorf("[chacha/redis] Unsupported sink: %T", m)
	}
	cKey := g.name + ":" + key
	p := g.c.Pipeline()
	r := p.Get(ctx, cKey)
	t := p.TTL(ctx, cKey)
	_, err := p.Exec(ctx)
	if err != nil {
		err = errors.Wrap(r.Err(), "[chacha/redis] Error")
	}

	if err != nil {
		err = g.load(ctx, key, s)
		if err != nil {
			return err
		}
		_ = g.c.Set(ctx, cKey, s.b, time.Until(s.expiresAt))

		return nil
	}

	ttl, _ := t.Result()
	b, _ := r.Bytes()

	return s.SetBytes(b, time.Now().Add(ttl))
}

func (g *getter) GetMany(ctx context.Context, keys []string, m []chacha.Sink) error {
	cKeys := make([]string, len(keys))
	for i := range keys {
		cKeys[i] = g.name + ":" + keys[i]
	}

	r, err := getScript.Run(ctx, g.c, cKeys).Result()
	if err != nil {
		return errors.Wrap(err, "[chacha/redis] Unable to scan chacha")
	}

	sKeys := []string{}
	sVals := []interface{}{}
	for i, v := range r.([]interface{}) {
		s, ok := m[i].(*sink)
		if !ok {
			return errors.Errorf("[chacha/redis] Unsupported sink: %T", m)
		}

		r := v.([]interface{})
		if r[0] == nil {
			_ = g.load(ctx, keys[i], s)
			sKeys = append(sKeys, cKeys[i])
			sVals = append(sVals, string(s.b))
			sVals = append(sVals, int64(time.Until(s.expiresAt).Seconds()))
			continue
		}
		ttl := time.Duration(r[1].(int64)) * time.Second
		_ = s.SetString(r[0].(string), time.Now().Add(ttl))
	}

	if len(sKeys) > 0 {
		_ = setScript.Run(ctx, g.c, sKeys, sVals...)
	}
	return nil
}

func (c *client) Dial(name string, loader chacha.GetterFunc, options ...chacha.DialOption) chacha.Cache {
	g := &getter{loader: loader}
	for _, o := range options {
		if apply, ok := o.(Option); ok {
			apply(c)
		}
	}

	g.c = newRedisClient(c.hostSum, c.dialOption)
	g.name = name

	return g
}

func (c *client) Sink(ctx context.Context, i interface{}) chacha.Sink {
	switch payload := i.(type) {
	case proto.Message:
		return ProtoSink(payload)
	default:
		return i.(chacha.Sink)
	}
}

func WithRedisURL(u string) chacha.DialOption {
	return Option(func(c *client) {
		o, err := redis.ParseURL(u)
		if err != nil {
			panic(errors.Wrap(err, "[cache/redis] url error"))
		}

		o.MaxRetries = 3

		c.dialOption = o
		if os.Getenv("CACHA_DISABLE_HOST_CHECKSUM") != "" {
			c.hostSum = 0
		} else {
			c.hostSum = crc32.ChecksumIEEE([]byte(u))
		}
	})
}

func init() {
	chacha.Register("redis", &client{})
}
