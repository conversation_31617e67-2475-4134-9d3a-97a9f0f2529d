package redis

import (
	"time"

	"github.com/golang/protobuf/proto"
	"happyfresh.io/search/lib/chacha"
)

type sink struct {
	message   proto.Message
	b         []byte
	expiresAt time.Time
}

func (s *sink) SetBytes(b []byte, expiresAt time.Time) error {
	s.expiresAt = expiresAt
	s.b = b
	return proto.Unmarshal(b, s.message)
}

func (s *sink) SetString(b string, expiresAt time.Time) error {
	s.expiresAt = expiresAt
	s.b = []byte(b)
	return proto.Unmarshal([]byte(b), s.message)
}

func (s *sink) SetProto(m proto.Message, expiresAt time.Time) error {
	b, err := proto.Marshal(m)
	if err != nil {
		return err
	}

	return s.SetBytes(b, expiresAt)
}

func ProtoSink(m proto.Message) chacha.Sink {
	return &sink{message: m}
}
