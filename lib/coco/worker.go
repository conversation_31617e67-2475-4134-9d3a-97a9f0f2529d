package coco // import "happyfresh.io/search/lib/coco"

import (
	"context"
	"net/url"
	"strings"
	"sync"
	"time"

	slaves "github.com/fitraditya/GoSlaves"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/lib/coco/broker"
)

var (
	// Brokers configurations
	bLock sync.RWMutex
	bMap  = map[string]broker.Dialer{}
)

type Worker struct {
	queueName string
	queueType string
	broker    broker.Broker
	jobs      map[string]Job
	logger    log.Logger
	kLock     sync.RWMutex
	numWorker int
}

type Option func(*Worker)

func (w *Worker) Open(dsn string) error {
	uri, err := url.Parse(dsn)
	if err != nil {
		return errors.Wrap(err, "Invalid data source")
	}

	if uri.User != nil {
		uri.User = url.UserPassword(uri.User.Username(), "[filtered]")
	}

	bLock.RLock()
	b, ok := bMap[uri.Scheme]
	if !ok {
		return errors.Errorf("Unknown data source: %s", uri.String())
	}
	bLock.RUnlock()

	w.logger.Infof("Opening datasource from: %s", uri.String())
	q, err := b.Dial(dsn, broker.DialOption{QueueName: w.queueName, QueueType: w.queueType})
	if err != nil {
		return errors.Wrapf(err, "Unable to open datasource: %s", uri.String())
	}

	w.broker = q

	return nil
}

func (w *Worker) Add(key string, j Job) {
	w.kLock.Lock()
	defer w.kLock.Unlock()
	w.jobs[key] = j
}

func (w *Worker) Retrieve(key string) (Job, error) {
	w.kLock.RLock()
	j, ok := w.jobs[key]
	w.kLock.RUnlock()
	if !ok {
		return nil, errors.Errorf("Job not found for: %s", key)
	}

	return j, nil
}

func (w *Worker) Submit(ctx context.Context, key string, payload []byte) error {
	return w.broker.Publish(ctx, key, payload)
}

func (w *Worker) Start(ctx context.Context) error {
	g, ctx := errgroup.WithContext(ctx)
	wg := &sync.WaitGroup{}

	for key := range w.jobs {
		wg.Add(1)
		g.Go(func() error {
			pool := slaves.NewPool(w.numWorker, func(payload interface{}) {
				m := payload.(broker.Message)
				j, err := w.Retrieve(m.Key())
				if err != nil {
					log.Info(err)
					return
				}

				start := time.Now()
				log.Infof("Start processing message id: [%s] on job %s [at %s]", m.ID(), m.Key(), start.Format(time.RFC3339))

				if err = j.Do(ctx, m.Body()); err != nil {
					rErr := m.Reject()

					if strings.Contains(err.Error(), "NotFound") {
						log.Warnf("Data not found when processing [%s] %+v, error: %s", m.ID(), m.Key(), err.Error())
					} else {
						log.Errorf("Error when processing [%s] %+v, error: %+v", m.ID(), m.Key(), err)
					}

					if rErr != nil {
						err = errors.Wrap(err, rErr.Error())
					}
					return
				}

				log.WithFields(map[string]interface{}{
					"elapsed_time": time.Since(start).Nanoseconds() / 1000000,
				}).Infof("Finish processing message id: [%s] on job %s", m.ID(), m.Key())

				err = m.Ack()
				if err != nil {
					log.Errorf("Error: %+v", err)
				}
			})

			messages, err := w.broker.Consume(ctx, key)
			if err != nil {
				return errors.Wrapf(err, "[%s] Error consuming data", key)
			}

			wg.Done()
			for {
				select {
				case m := <-messages:
					pool.Serve(m)
				case <-ctx.Done():
					return errors.Errorf("[%s] Consume end", key)
				}
			}
		})

		log.For("coco", "Start").Infof("Starting Worker for %s [%d spawned]", key, w.numWorker)
		wg.Wait()
	}

	return g.Wait()
}

func NewWorker(name string, options ...Option) *Worker {
	w := &Worker{
		queueName: name,
		queueType: "direct",
		jobs:      map[string]Job{},
		logger:    log.Standard(),
		numWorker: 5,
	}

	for _, apply := range options {
		apply(w)
	}

	return w
}

func WithLogger(l log.Logger) Option {
	return func(w *Worker) {
		w.logger = l
	}
}

func WithWorkerCount(c int) Option {
	return func(w *Worker) {
		w.numWorker = c
	}
}

func RegisterBroker(name string, b broker.Dialer, aliases ...string) {
	bLock.Lock()
	defer bLock.Unlock()
	if _, ok := bMap[name]; ok {
		panic(errors.New("Broker already registered"))
	}

	bMap[name] = b
	for _, alias := range aliases {
		bMap[alias] = b
	}
}
