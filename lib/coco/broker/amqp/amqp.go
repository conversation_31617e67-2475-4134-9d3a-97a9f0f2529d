package amqp // import "happyfresh.io/search/lib/coco/broker/amqp"

import (
	"context"

	"github.com/pkg/errors"
	"github.com/rs/xid"
	"github.com/streadway/amqp"
	coco "happyfresh.io/search/lib/coco"
	"happyfresh.io/search/lib/coco/broker"
)

type queue struct {
	conn         *amqp.Connection
	exchange     string
	exchangeKind string
}

type message struct {
	delivery amqp.Delivery
}

func (m *message) Body() []byte {
	return m.delivery.Body
}

func (m *message) Ack() error {
	return m.delivery.Ack(false)
}

func (m *message) ID() string {
	return m.delivery.MessageId
}

func (m *message) Reject() error {
	return m.delivery.Reject(true)
}

func (m *message) Key() string {
	return m.delivery.Type
}

func (q *queue) Publish(ctx context.Context, key string, body []byte) (err error) {
	ch, routingKey, err := q.channel(key)
	if err != nil {
		return errors.Wrap(err, "Failed to open channel")
	}

	defer func() {
		err = ch.Close()
	}()

	payload := amqp.Publishing{
		MessageId:    xid.New().String(),
		DeliveryMode: amqp.Persistent,
		ContentType:  "text/plain",
		Body:         body,
	}

	err = ch.Publish(q.exchange, routingKey, false, false, payload)
	if err != nil {
		return errors.Wrap(err, "Failed to publish message")
	}

	return
}

func (q *queue) Consume(ctx context.Context, key string) (chan broker.Message, error) {
	r := make(chan broker.Message)
	ch, routingKey, err := q.channel(key)
	if err != nil {
		return r, err
	}
	deliveries, err := ch.Consume(key, routingKey, false, false, false, false, nil)
	if err != nil {
		return r, errors.Wrapf(err, "Failed to listen to: %s", key)
	}

	ctx, cancel := context.WithCancel(ctx)

	go func() {
		for {
			select {
			case d := <-deliveries:
				r <- &message{d}
			case <-ctx.Done():
				err := ch.Cancel(routingKey, false)
				close(r)
				if err != nil {
					cancel()
				}
			}
		}
	}()

	return r, nil
}

func (q *queue) channel(t string) (*amqp.Channel, string, error) {
	ch, err := q.conn.Channel()
	key := q.exchange + ":" + t
	if err != nil {
		return ch, key, errors.Wrap(err, "Failed open channel")
	}
	err = ch.ExchangeDeclare(q.exchange, q.exchangeKind, false, false, false, false, nil)
	if err != nil {
		return ch, key, errors.Wrap(err, "Failed to declaer exchange")
	}

	chQ, err := ch.QueueDeclare(t, false, false, false, false, nil)
	if err != nil {
		return ch, key, errors.Wrap(err, "Failed to declare queue")
	}

	err = ch.QueueBind(chQ.Name, key, q.exchange, false, nil)
	if err != nil {
		return ch, key, errors.Wrapf(err, "Failed to bind to an exchange: %s", key)
	}

	err = ch.Confirm(false)
	if err != nil {
		return ch, key, errors.Wrap(err, "Failed to confirm channel")
	}

	return ch, key, nil
}

func init() {
	coco.RegisterBroker("amqp", broker.DialFunc(func(dsn string, opt broker.DialOption) (broker.Broker, error) {
		q := &queue{
			exchange:     opt.QueueName,
			exchangeKind: opt.QueueType,
		}

		conn, err := amqp.Dial(dsn)
		if err != nil {
			return q, errors.Wrap(err, "Failed to open connection")
		}

		q.conn = conn

		return q, nil
	}), "amqps")
}
