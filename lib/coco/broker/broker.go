package broker

import "context"

type Dialer interface {
	Dial(dsn string, opt DialOption) (Broker, error)
}

type DialFunc func(string, DialOption) (Broker, error)

func (d DialFunc) Dial(dsn string, opt DialOption) (Broker, error) {
	return d(dsn, opt)
}

type DialOption struct {
	QueueName string
	QueueType string
}

type Broker interface {
	Publish(ctx context.Context, key string, body []byte) error
	Consume(ctx context.Context, key string) (chan Message, error)
}

type Message interface {
	ID() string
	Key() string
	Body() []byte
	Ack() error
	Reject() error
}
