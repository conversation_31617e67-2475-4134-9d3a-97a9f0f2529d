package redis // import "happyfresh.io/search/lib/coco/broker/redis"

import (
	"context"
	"sync"
	"time"

	"github.com/rs/xid"

	"github.com/adjust/rmq"
	"github.com/go-redis/redis"
	"github.com/pkg/errors"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/config"
	coco "happyfresh.io/search/lib/coco"
	"happyfresh.io/search/lib/coco/broker"
)

var (
	cleanerBeat = 30 * time.Second
)

type queue struct {
	conn  rmq.Connection
	chans map[string]chan rmq.Delivery
	cLock sync.RWMutex
}

type message struct {
	id       string
	key      string
	delivery rmq.Delivery
}

func (m *message) Body() []byte {
	return []byte(m.delivery.Payload())
}

func (m *message) Ack() error {
	ok := m.delivery.Ack()
	if !ok {
		return errors.New("[redis] Failed to ack batch")
	}
	return nil
}

func (m *message) Reject() error {
	ok := m.delivery.Reject()
	if !ok {
		return errors.New("[redis] Failed to reject batch")
	}
	return nil
}

func (m *message) ID() string {
	return m.id
}

func (m *message) Key() string {
	return m.key
}

func (q *queue) Publish(ctx context.Context, key string, body []byte) (err error) {
	task := q.conn.OpenQueue(key)
	ok := task.PublishBytes(body)
	if !ok {
		return errors.New("[redis] Unable to publish message")
	}

	return
}

func (q *queue) Consume(ctx context.Context, key string) (chan broker.Message, error) {
	r := make(chan broker.Message)
	q.cLock.Lock()
	q.chans[key] = make(chan rmq.Delivery)
	q.cLock.Unlock()
	task := q.conn.OpenQueue(key)
	task.StartConsuming(1, time.Duration(config.WorkerPollDuration())*time.Second)
	task.AddConsumerFunc(key, rmq.ConsumerFunc(func(b rmq.Delivery) {
		q.cLock.RLock()
		ch, ok := q.chans[key]
		q.cLock.RUnlock()
		if ok {
			ch <- b
		}
	}))

	clean := make(chan struct{})

	go func() {
		for {
			time.Sleep(cleanerBeat)
			log.For("redis", "Consume").Info("Cleaning inactive connections")
			clean <- struct{}{}
		}
	}()

	go func(r chan broker.Message) {
		q.cLock.RLock()
		ch := q.chans[key]
		q.cLock.RUnlock()
		for {
			select {
			case d := <-ch:
				r <- &message{
					key:      key,
					id:       "job-" + xid.New().String(),
					delivery: d,
				}
			case <-clean:
				err := rmq.NewCleaner(q.conn).Clean()
				log.For("redis", "Consume").Infof("Purge %d Rejected", task.PurgeRejected())
				if err != nil {
					log.For("redis", "Consume").Error(errors.Wrap(err, "[redis] Failed to clear queue"))
				}
			case <-ctx.Done():
				<-task.StopConsuming()
				close(r)
				return
			}
		}
	}(r)

	return r, nil
}

func init() {
	coco.RegisterBroker("redis", broker.DialFunc(func(dsn string, opt broker.DialOption) (broker.Broker, error) {

		q := &queue{
			chans: make(map[string]chan rmq.Delivery),
		}

		u, err := redis.ParseURL(dsn)
		if err != nil {
			return nil, errors.Wrap(err, "[redis] Cannot parse dsn")
		}

		u.MaxRetries = 3

		conn := rmq.OpenConnectionWithRedisClient(opt.QueueName, redis.NewClient(u))

		q.conn = conn

		return q, nil
	}))
}
