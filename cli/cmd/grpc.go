package cmd // import "happyfresh.io/search/cli/cmd"

import (
	"fmt"
	"net"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"google.golang.org/grpc"
	"happyfresh.io/lib/log"
	ssrv "happyfresh.io/search/api/handler"
	ssrv_rpc "happyfresh.io/search/api/rpc"
	"happyfresh.io/search/config"
	"happyfresh.io/search/core/catalog"
	"happyfresh.io/search/core/hubble"
	"happyfresh.io/search/core/idconv"

	"happyfresh.io/search/core/index"
	"happyfresh.io/search/core/index/pq"
	"happyfresh.io/search/core/pricing/aloha"
)

var (
	// GRPCCmd command to start a new search service api server
	GRPCCmd = &cobra.Command{
		Use:   "grpc",
		Short: "Start grpc server",
	}
)

func grpcRunE(cmd *cobra.Command, args []string) error {
	port := cmd.PersistentFlags().Lookup("port").Value.String()

	index.UseTaxonSuggestion = config.UseTaxonSuggestion()

	pqClient := pq.New()

	err := aloha.Connect(config.CalculatorUseSecure(), config.CalculatorAddress())
	if err != nil {
		log.For("cmd", "grpcRunE").Error(err)
		return err
	}

	// Write log to a file in non-development environment
	if !config.Development() {
		log.SetFormat(log.JSONFormat)
	}

	hubbleClient, err := hubble.New(
		config.HubbleAddress(),
		config.RedisDSN(),
	)
	if err != nil {
		log.For("cmd", "gatewayRunE").Error(err)
		return err
	}

	idConv := idconv.New(hubbleClient)

	catalogService, err := catalog.New(config.CatalogAddress(), config.RedisDSN())
	if err != nil {
		log.For("cmd", "gatewayRunE").Error(err)
		return err
	}

	searchService := ssrv.New(
		ssrv.WithPQClient(pqClient),
		ssrv.WithDisableSync(config.DisableSync()),
		ssrv.WithDisableSearchSuggestion(config.DisableSearchSuggestion()),
		ssrv.WithDisableOrion(!config.OrionEnable()),
		ssrv.WithTaxonNoneFilter(config.FilteredTaxonIDsInt64()),
		ssrv.WithRawPopularityBoostScore(config.RawPopularityBoostScore()),
		ssrv.WithIDConverter(idConv),
		ssrv.WithHubbleClient(hubbleClient),
		ssrv.WithCatalogClient(catalogService),
	)

	listener, err := net.Listen("tcp", fmt.Sprintf(":%s", port))
	if err != nil {
		log.For("cmd", "grpcRunE").Error(err)
		return err
	}

	ssrv_rpc.AddAllowedChannels(config.AllowedChannels())

	server := grpc.NewServer()
	ssrv_rpc.RegisterSearchServiceServer(server, searchService)
	log.For("cmd", "grpcRunE").Infof("Search Service is running on port %s\n", port)

	err = server.Serve(listener)
	if err != nil {
		log.For("cmd", "grpcRunE").Error(err)
		return err
	}

	return nil
}

func grpcPreRunE(cmd *cobra.Command, args []string) error {
	err := log.AddSentryHook(config.SentryDSN(), "ssrv-grpc"+config.Env(), config.Version())
	if err != nil {
		log.For("cmd", "grpcPreRunE").Error(err)
		return err
	}

	return nil
}

func init() {
	GRPCCmd.PreRunE = grpcPreRunE
	GRPCCmd.RunE = grpcRunE

	GRPCCmd.PersistentFlags().Int64P("port", "p", 4700, "GRPC server port")
	err := viper.BindPFlag("port", GRPCCmd.PersistentFlags().Lookup("port"))
	if err != nil {
		log.For("cmd", "init").Error(err)
	}
}
