package cmd // import "happyfresh.io/search/cli/cmd"

import (
	"os"

	"github.com/XSAM/otelsql"
	"github.com/spf13/cobra"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/config"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/core/pricing/aloha"
	recommendation "happyfresh.io/search/core/recommendation"
	sprinkles "happyfresh.io/search/core/sprinkles"
	"happyfresh.io/search/lib/chacha"
	r "happyfresh.io/search/lib/chacha/redis"
	w "happyfresh.io/search/lib/coco"
	"happyfresh.io/search/lib/paperclip-go"
	"happyfresh.io/search/lib/pq"

	// Import driver
	_ "happyfresh.io/search/lib/coco/broker/redis"

	_ "happyfresh.io/search/core/index/swiftype"
)

var (
	RootCmd = &cobra.Command{
		Use:   "search",
		Short: "ssrv",
		Long: `

███████╗███████╗ █████╗ ██████╗  ██████╗██╗  ██╗    ███████╗███████╗██████╗ ██╗   ██╗██╗ ██████╗███████╗
██╔════╝██╔════╝██╔══██╗██╔══██╗██╔════╝██║  ██║    ██╔════╝██╔════╝██╔══██╗██║   ██║██║██╔════╝██╔════╝
███████╗█████╗  ███████║██████╔╝██║     ███████║    ███████╗█████╗  ██████╔╝██║   ██║██║██║     █████╗
╚════██║██╔══╝  ██╔══██║██╔══██╗██║     ██╔══██║    ╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██║██║     ██╔══╝
███████║███████╗██║  ██║██║  ██║╚██████╗██║  ██║    ███████║███████╗██║  ██║ ╚████╔╝ ██║╚██████╗███████╗
╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝ ╚═════╝╚══════╝


`,
	}

	coco *w.Worker
)

func rootE(cmd *cobra.Command, args []string) error {
	return cmd.Help()
}

func rootPreE(cmd *cobra.Command, args []string) error {
	os.Stdout.WriteString(RootCmd.Long)

	recommendation.InitDefault(
		recommendation.WithAddress(config.RecommendationAddress()),
		recommendation.WithBoostConst(config.RecommendationBoostValue()),
		recommendation.WithCacheOptions("redis", r.WithRedisURL(config.RedisDSN())),
	)

	sprinkles.InitDefault(
		sprinkles.WithAddress(config.SprinklesAddress()),
		sprinkles.WithCacheOptions("redis", r.WithRedisURL(config.RedisDSN())),
	)

	cacheBackend := chacha.WithCache("redis",
		r.WithRedisURL(config.RedisDSN()),
	)

	err := aloha.DialMPQ(config.RedisDSN())
	if err != nil {
		return err
	}

	p, err := paperclip.New(config.Paperclip())
	if err != nil {
		return err
	}
	drName, err := otelsql.Register("postgres")
	if err != nil {
		log.For("registerotel", "runRoot").Warn(err)
		drName = "postgres"
	}
	o := datastore.Option{
		Driver:       drName,
		WriteDB:      config.WriteDatabaseDSN(),
		ReadDB:       config.ReadDatabaseDSNs(),
		CacheBackend: cacheBackend,
		Paperclip:    p,
		MaxIdleConns: 2,
		MaxOpenConns: 60,
		QuerySource:  config.QuerySource(),
	}
	datastore.Open(o)
	dbConn, dbROConn := datastore.GetCurrentConn()
	pq.SetDatabaseConn(dbConn, dbROConn...)

	coco = w.NewWorker("ssrv",
		w.WithLogger(log.For("cmd", "root")),
		w.WithWorkerCount(config.WorkerCount()),
	)

	if err := coco.Open(config.WorkerQueue()); err != nil {
		log.For("cmd", "root").Errorf("Err: %+v", err)
		return err
	}

	return nil
}

func init() {
	RootCmd.RunE = rootE
	RootCmd.PersistentPreRunE = rootPreE
	RootCmd.AddCommand(
		GRPCCmd,
		GatewayCmd,
		PublisherCmd,
		WorkerCmd,
	)
}
