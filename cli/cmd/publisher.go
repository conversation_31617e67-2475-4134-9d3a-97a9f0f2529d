package cmd

import (
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cobra"
	p "happyfresh.io/coco/lib/publisher"

	_ "happyfresh.io/coco/broker/redis"
	"happyfresh.io/lib/log"
	orionRPCLib "happyfresh.io/orion/lib/rpc/client"
	"happyfresh.io/search/config"
	"happyfresh.io/search/core/catalog"
	"happyfresh.io/search/publisher/trending"

	"happyfresh.io/search/lib/trace/datadog"
)

var (
	PublisherCmd = &cobra.Command{
		Use:   "publisher",
		Short: "Spawn publisher for reindexing job",
	}
)

func publisher(cmd *cobra.Command, args []string) {
	if !config.Development() {
		log.SetFormat(log.JSONFormat)
	}

	stop, err := datadog.Start(fmt.Sprintf("%s-Publisher", config.AppName()), config.Env(), config.Version())
	if err == nil {
		defer stop()
	} else {
		log.For("cmd", "publisher").Warn(err)
	}

	job, err := cmd.PersistentFlags().GetString("job")
	if err != nil {
		log.For("cmd", "publisher").Error(err)
		return
	}

	switch job {
	case "trending":
		updatedDate, _ := cmd.PersistentFlags().GetString("date")
		sourceDate, _ := cmd.PersistentFlags().GetString("source-date")
		if len(sourceDate) == 0 {
			sourceDate = updatedDate
		}

		if err := trendingJob(sourceDate, updatedDate); err != nil {
			log.For("cmd", "publisher").Error(err)
		}
		return
	}

	log.For("cmd", "publisher").Error(errors.New("Unknown job"))
}

func trendingJob(sourceUpdatedDate, targetUpdatedDate string) error {
	if len(sourceUpdatedDate) > 0 {
		_, err := time.Parse("2006-01-02", sourceUpdatedDate)
		if err != nil {
			return err
		}
	} else {
		sourceUpdatedDate = time.Now().Format("2006-01-02")
	}

	if len(targetUpdatedDate) > 0 {
		_, err := time.Parse("2006-01-02", targetUpdatedDate)
		if err != nil {
			return err
		}
	} else {
		targetUpdatedDate = time.Now().Format("2006-01-02")
	}

	orionClient := orionRPCLib.NewOrionClient(
		config.OrionAddress(),
		orionRPCLib.WithSecureConnection(config.OrionUseSecure()),
	)

	catalogClient, err := catalog.New(
		config.CatalogAddress(),
		config.RedisDSN(),
		catalog.WithSecure(config.CatalogUseSecure()),
	)
	if err != nil {
		return err
	}

	publisher, err := p.New(config.WorkerQueue())
	if err != nil {
		return err
	}

	trendingPublisher := trending.NewTrendingJob(
		publisher,
		trending.WithOrionClient(orionClient.Client),
		trending.WithCatalogClient(catalogClient),
		trending.WithSourceUpdatedDate(sourceUpdatedDate),
		trending.WithTargetUpdatedDate(targetUpdatedDate),
	)
	if err := trendingPublisher.Publish(); err != nil {
		return err
	}

	return nil
}

func init() {
	PublisherCmd.PersistentFlags().StringP("job", "j", "", "run a job (available job: trending)")
	PublisherCmd.MarkFlagRequired("job")
	PublisherCmd.PersistentFlags().StringP("date", "d", "", "configure update date for trending job")
	PublisherCmd.PersistentFlags().StringP("source-date", "s", "", "configure source date for trending job")
	PublisherCmd.Run = publisher
}
