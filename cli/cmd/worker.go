package cmd

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/config"
	"happyfresh.io/search/core/catalog"
	"happyfresh.io/search/core/index"

	"happyfresh.io/search/core/pricing/aloha"
	"happyfresh.io/search/lib/trace/datadog"
	"happyfresh.io/search/worker"
)

var (
	WorkerCmd = &cobra.Command{
		Use:   "worker",
		Short: "Spawn worker for reindexing job",
	}
)

func workerE(cmd *cobra.Command, args []string) error {
	if !config.Development() {
		log.SetFormat(log.JSONFormat)
	}

	stop, err := datadog.Start(fmt.Sprintf("%s-Worker", config.AppName()), config.Env(), config.Version())
	if err == nil {
		defer stop()
	} else {
		log.For("cmd", "gatewayRunE").Warn(err)
	}

	var o []index.Option
	for k, v := range config.IndexShards() {
		o = append(o, index.WithShard(k, v))
	}

	indexClient, err := index.New(config.IndexBackend(), o...)
	if err != nil {
		log.For("cmd", "workerE").Error(err)
		return err
	}

	catalogService, err := catalog.New(
		config.CatalogAddress(),
		config.RedisDSN(),
		catalog.WithSecure(config.CatalogUseSecure()),
	)
	if err != nil {
		log.For("cmd", "workerE").Error(err)
		return err
	}

	err = aloha.Connect(config.CalculatorUseSecure(), config.CalculatorAddress())
	if err != nil {
		log.For("cmd", "workerE").Error(err)
		return err
	}

	newStoreProductThreshold := config.IndexPopularityNewStoreProductThreshold()
	// excludedNewStoreProductStoreIds := config.IndexPopularityExcludedNewStoreProductStoreIds()
	excludedNewStoreProductStoreIds := []int{225001}
	indexPopularitySource := config.IndexPopularitySource()
	indexProductWorker := worker.NewIndexProductJob(indexClient, catalogService, indexPopularitySource, newStoreProductThreshold)
	indexStoreProductWorker := worker.NewIndexStockItemJob(indexClient, catalogService, indexPopularitySource, newStoreProductThreshold, excludedNewStoreProductStoreIds)
	for _, v := range []string{"", "_id", "_my", "_th"} {
		coco.Add(fmt.Sprintf("index_product%s", v), indexProductWorker)
		coco.Add(fmt.Sprintf("index_stock_item%s", v), indexStoreProductWorker)
		coco.Add(fmt.Sprintf("update_stock_item%s", v), indexStoreProductWorker)
	}

	return coco.Start(context.Background())
}

func workerPreE(cmd *cobra.Command, args []string) error {
	err := log.AddSentryHook(config.SentryDSN(), "ssrv-"+config.Env(), config.Version())
	if err != nil {
		log.For("cmd", "init").Error(err)
	}

	log.SetFormat(log.JSONFormat)

	return nil
}

func init() {
	WorkerCmd.RunE = workerE
	WorkerCmd.PreRunE = workerPreE
}
