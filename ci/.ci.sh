#! /bin/bash
set -x

if [ "$1" == "" ]; then
  printf "\t\033[31mPlease use option 'ubuntu' or 'macos' \033[0;30m\033[41mFAILED!\033[0m\n"
fi

os="$1"

if [ "$os" == "ubuntu" ] && [ "$2" != "--skip-prerequisite" ]; then
  apt-get update && apt-get install unzip
elif [ "$os" == "macos" ] && [ "$2" != "--skip-prerequisite" ]; then
  brew install unzip
fi

if ! command -v bzr &>/dev/null; then
  if [ "$os" == "ubuntu" ]; then
    apt-get install -y bzr
  elif [ "$os" == "macos" ]; then
    brew install bzr
  fi
fi

# Install golangci-lint
if [ ! -f /usr/local/bin/golangci-lint ]; then
  curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.61.0
fi

# Install protoc
if [ ! -f /usr/local/bin/protoc ]; then
  if [ "$os" == "ubuntu" ]; then
    curl -L https://github.com/google/protobuf/releases/download/v3.6.1/protoc-3.6.1-linux-x86_64.zip -o protoc3.zip
  elif [ "$os" == "macos" ]; then
    curl -L https://github.com/protocolbuffers/protobuf/releases/download/v3.6.1/protoc-3.6.1-osx-x86_64.zip -o protoc3.zip
  fi

  unzip protoc3.zip -d /tmp/protoc3

  mv /tmp/protoc3/bin/* /usr/local/bin/
  mv /tmp/protoc3/include/* /usr/local/include/
fi

if [ ! -d /usr/local/include/protobuf-1.3.1/gogoproto ]; then
  curl -OL https://github.com/gogo/protobuf/archive/v1.3.1.zip
  unzip v1.3.1.zip -d /tmp

  mv /tmp/protobuf-1.3.1 /usr/local/include
fi

# Install protoc-gen-gofast
if [ ! -f /usr/local/bin/protoc-gen-gofast ]; then
  go install github.com/gogo/protobuf/protoc-gen-gofast@latest
  cp $(which protoc-gen-gofast) /usr/local/bin/
fi

# Install esc
if [ ! -f /usr/local/bin/esc ]; then
  go install github.com/mjibson/esc@v0.1.0
  cp $(which esc) /usr/local/bin/
fi

# Install protoc-gen-gofast
if [ ! -f /usr/local/bin/protoc-gen-gofast ]; then
  go install github.com/gogo/protobuf/protoc-gen-gofast@latest
  cp `which protoc-gen-gofast` /usr/local/bin/
fi


exit 0
