package trending

import (
	"context"
	"io"
	"strings"
	"sync"
	"time"

	slaves "github.com/fitraditya/GoSlaves"
	"happyfresh.io/coco/lib/proto/boost"
	p "happyfresh.io/coco/lib/publisher"
	"happyfresh.io/lib/log"
	orionRPCLib "happyfresh.io/orion/lib/rpc"
	"happyfresh.io/search/core/catalog"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/publisher"
)

type trendingJob struct {
	publisher         *p.Publisher
	queueName         string
	sourceUpdatedDate string
	targetUpdatedDate string

	pool slaves.Pool

	catalog catalog.Client
	orion   orionRPCLib.ApiClient
}

func (t *trendingJob) Publish(opts ...publisher.Opt) error {
	log.For("trending", "Publish").Infof("Start publisher with source date: %s, target date: %s", t.sourceUpdatedDate, t.targetUpdatedDate)

	ctx := context.Background()
	req := &orionRPCLib.TrendingProductsRequest{
		UpdatedDate: t.sourceUpdatedDate,
	}
	stream, err := t.orion.GetTrendingProducts(ctx, req)
	if err != nil {
		log.For("trending", "Publish").Error(err)
		return err
	}

	wg := sync.WaitGroup{}

	for {
		msg, err := stream.Recv()
		if err == io.EOF {
			// reached end of stream
			break
		}

		if err != nil {
			log.For("trending", "Publish").Fatalf("Error while reading stream: %v", err)
		} else {
			t.pool.Serve(msg)
		}
	}

	wg.Wait()

	log.For("trending", "Publish").Info("Done")
	return nil
}

func (t *trendingJob) job(payload interface{}) {
	msg, ok := payload.(*orionRPCLib.TrendingProduct)
	if !ok {
		log.For("trending", "job").Errorf("Error cast payload to trending product")
		return
	}

	ctx := context.Background()

	storeProduct, err := t.catalog.GetStoreProductsPLP(ctx,
		[]int64{msg.GetVariantID()},
		msg.GetStockLocationID(),
		"en",
	)
	if err != nil {
		log.For("trending", "Publish").Errorf("Error while getting store product information: %v\n", err)
		return
	}

	countryCode, err := datastore.CountryByStockLocationID(ctx, int(msg.GetStockLocationID()))
	if err != nil {
		log.For("trending", "Publish").Errorf("Error while getting country information: %v\n", err)
		return
	}

	if len(storeProduct) == 0 || len(storeProduct[0].StoreProducts) == 0 {
		log.For("trending", "Publish").Errorf("Got empty store product ID (variantID %d, stockLocationID %d)", msg.GetVariantID(), msg.GetStockLocationID())
		return
	}

	t.publisher.PublishProto(ctx, t.queueName, &boost.Trending{
		StoreProductId: storeProduct[0].GetStoreProducts()[0].GetStoreProductId(),
		CountryIso:     strings.ToLower(countryCode.GetIsoName()),
		Score:          msg.GetScore(),
		UpdatedDate:    t.targetUpdatedDate,
	})
}

type opt func(t *trendingJob)

func NewTrendingJob(p *p.Publisher, opts ...opt) publisher.Job {
	t := &trendingJob{
		publisher:         p,
		queueName:         "trending-boost-update",
		sourceUpdatedDate: time.Now().Format("2006-01-02"),
		targetUpdatedDate: time.Now().Format("2006-01-02"),
	}

	t.pool = slaves.NewPool(0, t.job)

	for _, apply := range opts {
		apply(t)
	}

	return t
}

func WithCatalogClient(c catalog.Client) opt {
	return func(t *trendingJob) {
		t.catalog = c
	}
}

func WithOrionClient(o orionRPCLib.ApiClient) opt {
	return func(t *trendingJob) {
		t.orion = o
	}
}

func WithSourceUpdatedDate(u string) opt {
	return func(t *trendingJob) {
		t.sourceUpdatedDate = u
	}
}

func WithTargetUpdatedDate(u string) opt {
	return func(t *trendingJob) {
		t.targetUpdatedDate = u
	}
}
