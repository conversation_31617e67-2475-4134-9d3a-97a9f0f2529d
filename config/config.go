package config

import (
	"io/ioutil"
	"os"
	"runtime"
	"strconv"
	"strings"

	"github.com/spf13/viper"
	"happyfresh.io/lib/log"
	"happyfresh.io/search/core/datastore"
	"happyfresh.io/search/lib/paperclip-go"
)

func AppName() string {
	return "CatalogService-Search"
}

func QuerySource() datastore.QuerySource {
	if Development() {
		return datastore.LocalQuery
	}

	return datastore.EmbeddedQuery
}

func Env() string {
	return viper.GetString("env")
}

func Development() bool {
	return Env() == "development"
}

func WriteDatabaseDSN() string {
	return viper.GetString("database.dsn")
}

func ReadDatabaseDSNs() []string {
	return viper.GetStringSlice("database.read.dsn")
}

func ReadDatabaseTestGatewayDSN() string {
	return viper.GetString("database.read.test.gateway.dsn")
}

func WorkerQueue() string {
	return viper.GetString("worker.queue")
}

func WorkerBatches() int {
	return viper.GetInt("worker.batches")
}

func WorkerPollDuration() int {
	return viper.GetInt("worker.duration")
}

func WorkerCount() int {
	return viper.GetInt("worker.count")
}

func RedisDSN() string {
	return viper.GetString("redis.dsn")
}

func RedisDSNTest() string {
	return viper.GetString("redis.test.dsn")
}

func IndexBackend() string {
	return viper.GetString("index.backend")
}

func IndexShards() map[string]string {
	switch IndexBackend() {
	case "swiftype":
		return IndexAppsearchShards()
	case "elasticsearch":
		return IndexESShards()
	default:
		return nil
	}
}

func IndexAppsearchShards() map[string]string {
	// TODO: shards should be a config
	c := map[string]string{}
	for _, v := range []string{"id", "my", "th"} {
		c[v] = viper.GetString("index.shards." + v)
	}

	return c
}

func IndexShardsTest() map[string]string {
	// TODO: shards should be a config
	c := map[string]string{}
	for _, v := range []string{"id", "my", "th"} {
		c[v] = viper.GetString("index.shards.test." + v)
	}

	return c
}

func IndexESShards() map[string]string {
	c := map[string]string{}
	for _, v := range []string{"id", "my", "th"} {
		c[v] = viper.GetString("index.es.shards." + v)
	}

	return c
}

func IndexESShardsTest() map[string]string {
	c := map[string]string{}
	for _, v := range []string{"id", "my", "th"} {
		c[v] = viper.GetString("index.es.shards.test." + v)
	}

	return c
}

func IndexSuggesterESShards() map[string]string {
	c := map[string]string{}
	for _, v := range []string{"id", "my", "th"} {
		c[v] = viper.GetString("index.suggester.es.shards." + v)
	}

	return c
}

func IndexSuggesterESShardsTest() map[string]string {
	c := map[string]string{}
	for _, v := range []string{"id", "my", "th"} {
		c[v] = viper.GetString("index.suggester.es.shards.test." + v)
	}

	return c
}

func SynonymESShards() map[string]string {
	c := map[string]string{}
	for _, v := range []string{"id", "my", "th"} {
		c[v] = viper.GetString("synonym.es.shards." + v)
	}

	return c
}

func ESSynonymEnable() bool {
	return viper.GetBool("synonym.es.enable")
}

func ESSynonymEnableCountry() map[string]bool {
	e := map[string]bool{}
	for _, v := range []string{"id", "my", "th"} {
		e[v] = viper.GetBool("synonym.es.enable." + v)
	}

	return e
}

func CDNURL() string {
	return viper.GetString("cdn.url")
}

func CalculatorUseSecure() bool {
	return viper.GetBool("calculator.use_secure")
}

func CalculatorAddress() string {
	if viper.GetBool("calculator.use_secure") {
		return viper.GetString("calculator.secure.address")
	}

	return viper.GetString("calculator.address")
}

func PromotionUseSecure() bool {
	return viper.GetBool("promotion.use_secure")
}

func PromotionAddress() string {
	if viper.GetBool("promotion.use_secure") {
		return viper.GetString("promotion.secure.address")
	}

	return viper.GetString("promotion.address")
}

func PriceUseSecure() bool {
	return viper.GetBool("price.use_secure")
}

func PriceAddress() string {
	if viper.GetBool("price.use_secure") {
		return viper.GetString("price.secure.address")
	}

	return viper.GetString("price.address")
}

func CatalogUseSecure() bool {
	return viper.GetBool("catalog.use_secure")
}

func CatalogAddress() string {
	if viper.GetBool("catalog.use_secure") {
		return viper.GetString("catalog.secure.address")
	}

	return viper.GetString("catalog.address")
}

func CatalogForceCache() bool {
	return viper.GetBool("catalog.force_cache")
}

// SentryDSN :nodoc:
func SentryDSN() string {
	return viper.GetString("sentry.dsn")
}

// Version :nodoc:
func Version() string {
	return viper.GetString("version")
}

func Paperclip() paperclip.Config {
	m := viper.GetStringMap("paperclip")
	m["cdn"] = viper.GetString("paperclip.cdn")
	m["secret_key"] = viper.GetString("paperclip.secret_key")
	return paperclip.Config(m)
}

func UseTaxonSuggestion() bool {
	return viper.GetBool("index.use_taxon_suggestion")
}

func RecommendationAddress() string {
	return viper.GetString("recommendation.address")
}

func RecommendationBoostValue() float64 {
	return viper.GetFloat64("recommendation.boost_value")
}

func SprinklesAddress() string {
	return viper.GetString("sprinkles.address")
}

func OrionEnable() bool {
	return viper.GetBool("orion.enable")
}

func OrionUseSecure() bool {
	return viper.GetBool("orion.use_secure")
}

func OrionAddress() string {
	if viper.GetBool("orion.use_secure") {
		return viper.GetString("orion.secure.address")
	}

	return viper.GetString("orion.address")
}

// OrionCache DEPRECATED
// A setting to toggle Orion synonym being cached or not
func OrionCache() bool {
	return viper.GetBool("orion.cache")
}

func TrackingEnable() bool {
	return viper.GetBool("tracking.enable")
}

func DisableSync() bool {
	return viper.GetBool("sync.disable")
}

func DisableBrandFilter() bool {
	return viper.GetBool("search.brand_filter_disabled")
}

func DisableSearchSuggestion() bool {
	return viper.GetBool("suggestion.disabled")
}

func EnableGroupCachePeer() bool {
	return viper.GetBool("chacha.enable_peer")
}

func AllowedChannels() []string {
	channelString := viper.GetString("allowed_channels")
	if channelString == "" {
		return []string{}
	}

	return strings.Split(channelString, ",")
}

func FilteredTaxonIDs() []int {
	taxonIDs := viper.GetString("filtered_taxon_ids")
	if taxonIDs == "" {
		return []int{}
	}

	ts := strings.Split(taxonIDs, ",")
	filteredTaxonIDs := []int{}
	for _, t := range ts {
		id, err := strconv.Atoi(t)
		if err != nil {
			continue
		}

		filteredTaxonIDs = append(filteredTaxonIDs, id)
	}

	return filteredTaxonIDs
}

func FilteredTaxonIDsInt64() []int64 {
	taxonIDs := viper.GetString("filtered_taxon_ids")
	if taxonIDs == "" {
		return []int64{}
	}

	ts := strings.Split(taxonIDs, ",")
	filteredTaxonIDs := []int64{}
	for _, t := range ts {
		id, err := strconv.ParseInt(t, 10, 64)
		if err != nil {
			continue
		}

		filteredTaxonIDs = append(filteredTaxonIDs, id)
	}

	return filteredTaxonIDs
}

func AndroidFilteredTaxonIDsInt64() []int64 {
	taxonIDs := viper.GetString("android_filtered_taxon_ids")
	if taxonIDs == "" {
		return []int64{}
	}

	ts := strings.Split(taxonIDs, ",")
	filteredTaxonIDs := []int64{}
	for _, t := range ts {
		id, err := strconv.ParseInt(t, 10, 64)
		if err != nil {
			continue
		}

		filteredTaxonIDs = append(filteredTaxonIDs, id)
	}

	return filteredTaxonIDs
}

func RawPopularityBoostScore() float64 {
	return viper.GetFloat64("rpop_boost_score")
}

func HubbleUseSecure() bool {
	return viper.GetBool("hubble.use_secure")
}

func HubbleAddress() string {
	if viper.GetBool("hubble.use_secure") {
		return viper.GetString("hubble.secure.address")
	}

	return viper.GetString("hubble.address")
}

func MaxGlobalSLI() int64 {
	return viper.GetInt64("global.max_sli")
}

func MaxGlobalProduct() int64 {
	return viper.GetInt64("global.max_product")
}

func MaxMOQ() int64 {
	return viper.GetInt64("max_moq")
}

func MaxMOQHC() int64 {
	return viper.GetInt64("max_moq_hc")
}

func EnableGlobalPriceMap() map[string]bool {
	c := map[string]bool{}
	for _, v := range []string{"id", "my", "th"} {
		c[v] = viper.GetBool("global.price.enable." + v)
	}

	return c
}

func TaxonRankAddress() string {
	return viper.GetString("taxon_rank.address")
}

func ReplacementSuggestionsAddress() string {
	return viper.GetString("replacement_suggestions.address")
}

func NullRecommendationAddress() string {
	return viper.GetString("null_recommendation.address")
}

func IndexPopularitySource() string {
	return viper.GetString("index.popularity.source")
}

func EnabledTranslationSourceAndTarget() map[string][]string {
	m := make(map[string][]string)

	s := viper.GetString("translation.enabled_source_and_target")

	for _, scScplit := range strings.Split(s, ";") {
		cl := strings.Split(scScplit, ":")

		if len(cl) > 1 {
			m[cl[0]] = strings.Split(cl[1], ",")
		}
	}

	return m
}

func TrendingBoostWeight() float64 {
	return viper.GetFloat64("trending_boost.weight")
}

func TrendingBoostActivePeriod() int64 {
	return viper.GetInt64("trending_boost.active_period")
}

// IndexPopularityNewStoreProductThreshold config fetch number of days from config file
// example value: -90, Meaning product considered new
// when it created between current date and -90 days
// where product would considered new
// It returns number of days
func IndexPopularityNewStoreProductThreshold() int {
	return viper.GetInt("index.popularity.new_store_product_threshold")
}

func IndexPopularityExcludedNewStoreProductStoreIds() []int {
	return viper.GetIntSlice("index.popularity.excluded_new_store_product_store_ids")
}

func OtelAgentPort() string {
	return viper.GetString("otel.agent_port")
}

func init() {
	if s := os.Getenv("SANDBOX"); s != "" {
		viper.SetEnvPrefix(s)
		viper.SetConfigName(s)
	} else {
		viper.SetEnvPrefix("SSRV")
		viper.SetConfigName("config")
	}

	if s := os.Getenv("PROJECT_DIR"); s != "" {
		viper.AddConfigPath(s)
	}

	viper.AddConfigPath("/etc/ssrv/")
	viper.AddConfigPath("$HOME/.ssrv/")
	viper.AddConfigPath(".")
	viper.AddConfigPath("/Users/<USER>/Go/src/happyfresh.io/search/")
	viper.AddConfigPath("./..")
	viper.AddConfigPath("$PWD/")
	viper.AddConfigPath("$GOPATH/src/happyfresh.io/search/")

	viper.SetDefault("PORT", 4600)
	if s := os.Getenv("PORT"); s != "" {
		viper.SetDefault("PORT", s)
	}

	viper.SetDefault("env", "development")
	viper.SetDefault("database.read.dsn", []string{})
	viper.SetDefault("index.backend", "elasticsearch")
	viper.SetDefault("index.shards", map[string]string{})
	viper.SetDefault("index.es.shards", map[string]string{})
	viper.SetDefault("index.use_taxon_suggestion", false)
	viper.SetDefault("redis.dsn", WorkerQueue())
	viper.SetDefault("worker.batches", 25)
	viper.SetDefault("worker.duration", 1)
	viper.SetDefault("worker.count", runtime.GOMAXPROCS(0))
	viper.SetDefault("recommendation.boost_value", 2.0)
	viper.SetDefault("chacha.enable_peer", false)
	viper.SetDefault("orion.enable", false)
	viper.SetDefault("orion.cache", false)
	viper.SetDefault("tracking.enable", false)
	viper.SetDefault("rpop_boost_score", 2.0)
	viper.SetDefault("catalog.use_secure", false)
	viper.SetDefault("catalog.force_cache", false)
	viper.SetDefault("hubble.use_secure", false)
	viper.SetDefault("global.max_sli", 10)
	viper.SetDefault("global.max_product", 5)
	viper.SetDefault("max_moq", 50)
	viper.SetDefault("max_moq_hc", 100)
	viper.SetDefault("trending_boost.weight", 1)
	viper.SetDefault("otel.agent_port", "4317")

	if version, err := ioutil.ReadFile("VERSION"); err == nil && len(string(version)) > 0 {
		viper.Set("version", strings.TrimSpace(string(version)))
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_", "-", "_"))
	viper.AutomaticEnv()
	err := viper.MergeInConfig()
	if err != nil {
		log.For("config", "init").Infof("Error while reading config file %v", err)
	}
}
