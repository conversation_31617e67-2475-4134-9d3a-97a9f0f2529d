{"index_patterns": ["brand-summaries-th-staging-*"], "template": {"settings": {"index": {"analysis": {"analyzer": {"autocomplete": {"filter": ["lowercase"], "tokenizer": "autocomplete"}, "autocomplete_search": {"tokenizer": "lowercase"}, "autocomplete_thai": {"filter": ["lowercase", "decimal_digit", "thai_stop", "thai_edge_ngram"], "tokenizer": "thai"}, "autocomplete_search_thai": {"filter": ["lowercase", "decimal_digit", "thai_stop"], "tokenizer": "thai"}}, "filter": {"thai_edge_ngram": {"min_gram": "2", "type": "edge_ngram", "preserve_original": "true", "max_gram": "7"}, "thai_stop": {"type": "stop", "stopwords": "_thai_"}}, "tokenizer": {"autocomplete": {"token_chars": ["letter", "digit", "punctuation", "symbol"], "min_gram": "2", "type": "edge_ngram", "max_gram": "7"}}}}}, "mappings": {"properties": {"conversions": {"type": "long"}, "brand_id": {"type": "long"}, "brand_name": {"search_analyzer": "autocomplete_search", "analyzer": "autocomplete", "type": "text"}, "brand_name_locale": {"search_analyzer": "autocomplete_search_thai", "analyzer": "autocomplete_thai", "type": "text"}, "stock_location_id": {"type": "long"}, "store_category_id": {"type": "long"}, "id": {"type": "keyword"}, "supplier_id": {"type": "long"}}}, "aliases": {"brand-summaries-th-staging": {}}}, "composed_of": [], "priority": 0, "version": 1}