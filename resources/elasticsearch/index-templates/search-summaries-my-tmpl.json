{"index_patterns": ["search-summaries-my-staging-*"], "template": {"settings": {"index": {"analysis": {"analyzer": {"autocomplete": {"filter": ["lowercase"], "tokenizer": "autocomplete"}, "autocomplete_search": {"tokenizer": "lowercase"}}, "tokenizer": {"autocomplete": {"token_chars": ["letter", "digit", "punctuation", "symbol"], "min_gram": "2", "type": "edge_ngram", "max_gram": "7"}}}}}, "mappings": {"properties": {"conversions": {"type": "long"}, "query": {"search_analyzer": "autocomplete_search", "analyzer": "autocomplete", "type": "text"}, "stock_location_id": {"type": "long"}, "taxons": {"type": "nested", "properties": {"name_ms": {"type": "keyword"}, "name_th": {"type": "keyword"}, "id": {"type": "long"}, "name_id": {"type": "keyword"}, "name_en": {"type": "keyword"}}}, "store_category_id": {"type": "long"}, "id": {"type": "keyword"}, "occurences": {"type": "long"}, "locale": {"type": "keyword"}, "supplier_id": {"type": "long"}, "country_iso": {"type": "keyword"}}}, "aliases": {"search-summaries-my-staging": {}}}, "composed_of": [], "priority": 0, "version": 1}