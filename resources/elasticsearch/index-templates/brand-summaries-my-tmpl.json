{"index_patterns": ["brand-summaries-my-staging-*"], "template": {"settings": {"index": {"analysis": {"analyzer": {"autocomplete": {"filter": ["lowercase"], "tokenizer": "autocomplete"}, "autocomplete_search": {"tokenizer": "lowercase"}}, "tokenizer": {"autocomplete": {"token_chars": ["letter", "digit", "punctuation", "symbol"], "min_gram": "2", "type": "edge_ngram", "max_gram": "7"}}}}}, "mappings": {"properties": {"conversions": {"type": "long"}, "brand_id": {"type": "long"}, "brand_name": {"search_analyzer": "autocomplete_search", "analyzer": "autocomplete", "type": "text"}, "brand_name_locale": {"search_analyzer": "autocomplete_search", "analyzer": "autocomplete", "type": "text"}, "stock_location_id": {"type": "long"}, "store_category_id": {"type": "long"}, "id": {"type": "keyword"}, "supplier_id": {"type": "long"}}}, "aliases": {"brand-summaries-my-staging": {}}}, "composed_of": [], "priority": 0, "version": 1}