{"index_patterns": ["search-summaries-th-staging-*"], "template": {"settings": {"index": {"analysis": {"filter": {"thai_edge_ngram": {"min_gram": "2", "type": "edge_ngram", "preserve_original": "true", "max_gram": "7"}, "thai_stop": {"type": "stop", "stopwords": "_thai_"}}, "analyzer": {"autocomplete_search_thai": {"filter": ["lowercase", "decimal_digit", "thai_stop"], "tokenizer": "thai"}, "autocomplete_thai": {"filter": ["lowercase", "decimal_digit", "thai_stop", "thai_edge_ngram"], "tokenizer": "thai"}}}}}, "mappings": {"properties": {"conversions": {"type": "long"}, "query": {"search_analyzer": "autocomplete_search_thai", "analyzer": "autocomplete_thai", "type": "text"}, "stock_location_id": {"type": "long"}, "taxons": {"type": "nested", "properties": {"name_ms": {"type": "keyword"}, "name_th": {"type": "keyword"}, "id": {"type": "long"}, "name_id": {"type": "keyword"}, "name_en": {"type": "keyword"}}}, "id": {"type": "keyword"}, "occurences": {"type": "long"}, "locale": {"type": "keyword"}, "country_iso": {"type": "keyword"}}}, "aliases": {"search-summaries-th-staging": {}}}, "composed_of": [], "priority": 0, "version": 1}