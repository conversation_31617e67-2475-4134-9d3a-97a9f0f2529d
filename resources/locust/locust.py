import time
from locust import HttpUser, task, between

class HFUser(HttpUser):
    wait_time = between(1.0, 2.0)

    @task
    def search(self):
        for page in range(6):
            self.client.get(
                name="/search",
                url=f"/api/stock_locations/3/products/search?page={page}&per_page=10&q=orange",
                headers={
                    "Content-Type": "application/json",
                    "Locale": "EN",
                    "Country": "ID",
                    "X-Happy-Client-Type": "android",
                    "X-Happy-Client-Version": "2.13",
                    "X-Happy-Client-Build": "418"
                },
            )
            time.sleep(0.5)
    
    @task
    def browse(self):
        for page in range(6):
            self.client.get(
                name="/browse",
                url=f"/api/stock_locations/3/taxons/2/products?page={page}&per_page=10&q=orange",
                headers={
                    "Content-Type": "application/json",
                    "Locale": "EN",
                    "Country": "ID",
                    "X-Happy-Client-Type": "android",
                    "X-Happy-Client-Version": "2.13",
                    "X-Happy-Client-Build": "418"
                },
            )
            time.sleep(0.5)

    @task
    def pbv(self):
        self.client.get(
            name="/pbv",
            url="/api/stock_locations/3/product_by_variants?variant_id[]=34722&variant_id[]=297987&variant_id[]=248862&variant_id[]=26229&variant_id[]=14650&variant_id[]=29328&variant_id[]=38527&page=1&per_page=10&show_oos=true",
            headers={
                "Content-Type": "application/json",
                "Locale": "EN",
                "Country": "ID",
                "X-Happy-Client-Type": "android",
                "X-Happy-Client-Version": "2.13",
                "X-Happy-Client-Build": "418"
            },
        )
