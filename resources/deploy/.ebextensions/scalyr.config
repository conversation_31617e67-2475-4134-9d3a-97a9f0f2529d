files:
  "/tmp/scalyr.json":
    mode: "000755"
    owner: root
    group: root
    content: |
      {
        "api_key": "`{"Fn::GetOptionSetting": {"Namespace": "aws:elasticbeanstalk:application:environment", "OptionName": "SSRV_SCALYR_API_KEY", "DefaultValue": "12345678"}}`",
        "server_attributes": {
          "environment": "`{"Fn::GetOptionSetting": {"Namespace": "aws:elasticbeanstalk:application:environment", "OptionName": "SSRV_ENV", "DefaultValue": "staging"}}`"
        },
        "logs": [
          {
            "path": "/var/log/web-1.error.log",
            "attributes": {
              "service": "search",
              "parser": "json"
            },
            "sampling_rules": [
              {
                "match_expression": "\"level\":\"info\"",
                "sampling_rate": 0
              }
            ]
          },
          {
            "path": "/var/log/nginx/access.log",
            "attributes": {
              "service": "search",
              "parser": "HF-NginxAccessLog"
            }
          },
          {
            "path": "/var/log/nginx/error.log",
            "attributes": {
              "service": "search",
              "parser": "nginxErrorLog"
            }
          }
        ],
        "monitors": [
        ]
      }

commands:
  01-wget:
    command: "wget -q https://www.scalyr.com/scalyr-repo/stable/latest/scalyr-repo-bootstrap-1.2.1-1.alt.noarch.rpm"
  02-removeBootstrap:
    command: "yum remove -y scalyr-repo scalyr-repo-bootstrap # Remove any previous repository definitions, if any."
  03-installBootstrap:
    command: "yum install -y --nogpgcheck scalyr-repo-bootstrap-1.2.1-1.alt.noarch.rpm"
  04-installScalyrRepo:
    command: "yum install -y scalyr-repo"
  05-installScalyrAgent2:
    command: "yum install -y scalyr-agent-2"
  06-agentFile:
    command: "cp /tmp/scalyr.json /etc/scalyr-agent-2/agent.json"
  07-start:
    command: "scalyr-agent-2 restart"