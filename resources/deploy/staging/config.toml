env = "staging"

[index.suggester.es.shards]
id = "https://elastic:<EMAIL>:9243/search-summaries-id-staging"
my = "https://elastic:<EMAIL>:9243/search-summaries-my-staging"
th = "https://elastic:<EMAIL>:9243/search-summaries-th-staging"

[paperclip]
styles = [
    "mini",
    "small",
    "product",
    "product_hq",
    "large",
    "wide"
]

[paperclip.attachment]
path = "attachments"
name = "attachment"

[paperclip.class]
path = "spree/images"
name = "Spree::Image"

[paperclip.format]
hash_data = ":class/:attachment/:id/:style/:updated_at"
path = "/t/:transform/:class/:attachment/:hash-:style.:extension"

[paperclip.transform]
composition = "fit"

