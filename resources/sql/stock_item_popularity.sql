-- name: stock-item-popularity-by-id
SELECT v.global_id,
       si.popularity
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
WHERE v.global_id = $1
AND sl.store_id = $2;

-- name: stock-item-popularity-average-by-id
SELECT v.global_id,
       avg(si.popularity) as popularity
FROM spree_stock_items si
INNER JOIN spree_variants v on v.id = si.variant_id
WHERE v.global_id = $1
AND si.popularity > 0
GROUP BY v.global_id;

