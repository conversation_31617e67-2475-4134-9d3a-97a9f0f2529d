--name: create-search-id
INSERT INTO searchjoy_searches (
  results_count,
  query,
  normalized_query,
  search_type,
  created_at,
  country_iso,
  language,
  stock_location_id,
  user_id,
  external_id,
  properties)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, %s, $9, $10)
RETURNING id

--name: find-searchjoy-by-id
SELECT 
  query,
  normalized_query,
  country_iso,
  language,
  stock_location_id,
  user_id,
  created_at,
  external_id,
  properties
FROM searchjoy_searches
WHERE id = $1
