-- name: variant-id-sku-max-product-count-by-keyword
SELECT
	li.variant_id,
	v.sku,
	COALESCE(l.max_product_count, 0)
FROM
	search_ads_lists l
	JOIN search_ads_list_items li ON li.search_ads_list_id = l.id
	JOIN spree_variants v ON v.id = li.variant_id
WHERE
	li.search_ads_list_id IN (
		SELECT
			l.id
		FROM
			search_ads_list_items li
			JOIN search_ads_lists l ON l.id = li.search_ads_list_id
			JOIN search_ads_keywords_lists akl ON akl.search_ads_list_id = l.id
			JOIN search_ads_keywords k ON k.id = akl.search_ads_keyword_id
		WHERE
			k.keyword = $1
			AND l.expires_at > now()
			AND l.starts_at < now()
		ORDER BY
			l.priority ASC,
			l.id ASC
		LIMIT 1)
ORDER BY
	li.position ASC;