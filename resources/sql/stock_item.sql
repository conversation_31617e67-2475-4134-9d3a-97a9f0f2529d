-- name: stock-item-by-id
SELECT si.id,
       v.sku,
       p.name,
       coalesce(pt.name, '-') name_local,
       p.description,
       coalesce(pt.description, '-') description_local,
       sl.store_id,
       si.in_stock,
       si.price,
       coalesce(si.normal_price, si.price, 0) normal_price,
       si.cost,
       coalesce(si.normal_cost, si.cost, 0) normal_cost,
       si.original_promo_price,
       si.client_type_promo_price,
       si.popularity,
       si.boosting_point,
       coalesce(si.promotion_id, -1) promotion_id,
       coalesce(sl.lat, 0::float) || ',' || coalesce(sl.lon, 0.0::float) store_location,
       v.global_id,
       p.global_id,
       pty.name,
       b.global_id AS brand_id,
       b.name AS brand_name,
       b.display_name_translations -> $3 AS brand_name_local,
       p.properties,
       $1,
       si.stock_location_id,
       p.slug,
       si.deleted_at,
       coalesce(si.max_order_quantity, 0)
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl ON sl.id = si.stock_location_id
INNER JOIN spree_variants v ON v.id = si.variant_id
           AND v.is_master = TRUE
INNER JOIN spree_products p ON p.id = v.product_id
LEFT JOIN spree_brands b ON b.id = p.brand_id
LEFT JOIN spree_product_types pty ON pty.id = p.product_type_id
LEFT JOIN spree_product_translations pt ON p.id = pt.spree_product_id
          AND pt.locale = $3
WHERE si.id = $2;

-- name: stock-items-by-ids
SELECT si.id,
       v.sku,
       p.name,
       coalesce(pt.name, '-') name_local,
       p.description,
       coalesce(pt.description, '-') description_local,
       sl.store_id,
       si.in_stock,
       si.price,
       coalesce(si.normal_price, si.price, 0) normal_price,
       si.cost,
       coalesce(si.normal_cost, si.cost, 0) normal_cost,
       si.original_promo_price,
       si.client_type_promo_price,
       si.popularity,
       NULL,
       NULL,
       NULL,
       v.global_id,
       p.global_id,
       pty.name,
       b.global_id AS brand_id,
       b.name AS brand_name,
       b.display_name_translations -> $1 AS brand_name_local,
       p.properties,
       c.iso_name,
       si.stock_location_id,
       p.slug,
       si.deleted_at,
       coalesce(si.max_order_quantity, 0)
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl ON sl.id = si.stock_location_id
INNER JOIN spree_variants v ON v.id = si.variant_id
           AND v.is_master = TRUE
INNER JOIN spree_products p ON p.id = v.product_id
LEFT JOIN spree_brands b ON b.id = p.brand_id
LEFT JOIN spree_product_types pty ON pty.id = p.product_type_id
LEFT JOIN spree_product_translations pt ON p.id = pt.spree_product_id
          AND pt.locale = $1
INNER JOIN spree_countries c ON sl.country_id = c.id
WHERE si.id = $2;
