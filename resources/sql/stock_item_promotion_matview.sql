CREATE MATERIALIZED VIEW analytics.stock_item_promotions_v5 AS 
WITH promotion_usages AS (
  SELECT p.id, COUNT(*) AS usage
  FROM spree_orders o
  INNER JOIN spree_adjustments adj ON adj.order_id = o.id
  INNER JOIN spree_promotion_actions pa ON pa.id = adj.source_id AND adj.source_type = 'Spree::PromotionAction'
  INNER JOIN spree_promotions p ON p.id = pa.promotion_id
  WHERE adj.eligible = TRUE AND p.starts_at <= now() AND p.expires_at >= now() AND p.usage_limit IS NOT NULL
  GROUP BY p.id
), promotion_store_usages AS (
  SELECT p.id, SUM(ip.quantity) AS usage
  FROM spree_item_promo_trackers ipt
  INNER JOIN spree_promotions p ON p.id = ipt.promotion_id
  INNER JOIN spree_item_promos ip ON ip.item_promo_tracker_id = ipt.id
  WHERE ip.item_promo_type = 1 AND p.starts_at <= now() AND p.expires_at >= now() AND p.usage_limit IS NOT NULL
  GROUP BY p.id
), promotion_rules AS (
    SELECT DISTINCT
    p.id,
    p.country_id,
    pr1.type,
    pr1.preferences AS p_preferences,
    ppr.product_id,
    pr2.type,
    pr2.preferences AS sl_preferences,
    slpr.stock_location_id
  FROM ((((((spree_promotions p
  LEFT JOIN spree_promotion_rules pr1 ON (((pr1.promotion_id = p.id) AND ((pr1.type)::text = 'Spree::Promotion::Rules::Product'::text))))
  LEFT JOIN spree_products_promotion_rules ppr ON ((ppr.promotion_rule_id = pr1.id)))
  LEFT JOIN spree_promotion_rules pr2 ON (((pr2.promotion_id = p.id) AND ((pr2.type)::text = 'Spree::Promotion::Rules::StockLocationRule'::text))))
  LEFT JOIN spree_stock_locations_promotion_rules slpr ON ((slpr.promotion_rule_id = pr2.id)))
  LEFT JOIN spree_promotion_actions pa ON ((pa.promotion_id = p.id)))
  LEFT JOIN spree_stock_locations sl ON ((sl.id = slpr.stock_location_id)))
  LEFT JOIN promotion_usages u ON u.id = p.id
  WHERE (((pa.type)::text = 'Spree::Promotion::Actions::FreeShipping'::text) AND (p.starts_at <= now()) AND (p.expires_at >= now()) AND (sl.active = true) AND (sl.store_id IS NOT NULL)) AND (u.usage < p.usage_limit OR p.usage_limit IS NULL OR u.usage IS NULL)
), promotion_rules_sl_none AS (
  SELECT *
  FROM promotion_rules pr
  WHERE pr.sl_preferences ~ 'none'::text
)
SELECT DISTINCT
  sl.store_id,
  v.sku,
  'free_shipping'::text AS type,
  0 AS buy_x_quantity,
  0 AS get_y_quantity,
  pr.id AS promotion_id
FROM promotion_rules pr
INNER JOIN spree_variants v ON v.product_id = pr.product_id
INNER JOIN spree_stock_locations sl ON sl.id = pr.stock_location_id
WHERE pr.sl_preferences ~ 'any'::text

UNION

SELECT DISTINCT
  sl.store_id,
  v.sku,
  'free_shipping'::text AS type,
  0 AS buy_x_quantity,
  0 AS get_y_quantity,
  pr.id AS promotion_id
FROM promotion_rules_sl_none pr
INNER JOIN spree_variants v ON v.product_id = pr.product_id
INNER JOIN spree_stock_locations sl ON sl.country_id = pr.country_id
WHERE sl.id NOT IN (SELECT DISTINCT stock_location_id FROM promotion_rules_sl_none)

UNION

SELECT DISTINCT
  sl.store_id,
  v.sku,
  'buy_x_get_y'::text AS type,
  prq.quantity AS buy_x_quantity,
  pav.quantity AS get_y_quantity,
  p.id AS promotion_id
FROM spree_promotions p
LEFT JOIN spree_promotion_rules pr ON pr.promotion_id = p.id
LEFT JOIN spree_promotion_rules_variants prv ON prv.promotion_rule_id = pr.id
LEFT JOIN spree_promotion_rules_suppliers prs ON prs.promotion_rule_id = pr.id
LEFT JOIN spree_promotion_rule_quantities prq ON prq.promotion_rule_id = pr.id
LEFT JOIN spree_promotion_actions pa ON pa.promotion_id = p.id
LEFT JOIN spree_get_y_variant_promotion_actions pav ON pav.promotion_action_id = pa.id
INNER JOIN spree_variants v ON v.id = prv.variant_id
INNER JOIN spree_stock_locations sl ON sl.supplier_id = prs.supplier_id
LEFT JOIN promotion_store_usages u ON u.id = p.id
WHERE (((pr.type)::text = 'Spree::Promotion::Rules::BuyXSingleRule'::text) AND ((pa.type)::text = 'Spree::Promotion::Actions::GetYAction'::text) AND (prq.quantity IS NOT NULL) AND (pav.quantity IS NOT NULL) AND (p.starts_at <= now()) AND (p.expires_at >= now()) AND (sl.active = true) AND (sl.store_id IS NOT NULL)) AND (u.usage < p.usage_limit OR p.usage_limit IS NULL OR u.usage IS NULL)

UNION

SELECT DISTINCT
  sl.store_id,
  v.sku,
  'buy_x_get_y'::text AS type,
  prq.quantity AS buy_x_quantity,
  pav.quantity AS get_y_quantity,
  p.id AS promotion_id
FROM spree_promotions p
LEFT JOIN spree_promotion_rules pr ON pr.promotion_id = p.id
LEFT JOIN spree_promotion_rules_variants prv ON prv.promotion_rule_id = pr.id
LEFT JOIN spree_promotion_rules_stock_locations prs ON prs.promotion_rule_id = pr.id
LEFT JOIN spree_promotion_rule_quantities prq ON prq.promotion_rule_id = pr.id
LEFT JOIN spree_promotion_actions pa ON pa.promotion_id = p.id
LEFT JOIN spree_get_y_variant_promotion_actions pav ON pav.promotion_action_id = pa.id
INNER JOIN spree_variants v ON v.id = prv.variant_id
INNER JOIN spree_stock_locations sl ON sl.id = prs.stock_location_id
LEFT JOIN promotion_store_usages u ON u.id = p.id
WHERE (((pr.type)::text = 'Spree::Promotion::Rules::BuyXSingleRule'::text) AND ((pa.type)::text = 'Spree::Promotion::Actions::GetYAction'::text) AND (prq.quantity IS NOT NULL) AND (pav.quantity IS NOT NULL) AND (p.starts_at <= now()) AND (p.expires_at >= now()) AND (sl.active = true) AND (sl.store_id IS NOT NULL))  AND (u.usage < p.usage_limit OR p.usage_limit IS NULL OR u.usage IS NULL);


CREATE UNIQUE INDEX index_stock_item_promotions_on_id_and_type_and_b_x_and_g_y_v5 ON analytics.stock_item_promotions_v5(store_id int4_ops,sku text_ops,type text_ops,buy_x_quantity int4_ops,get_y_quantity int4_ops,promotion_id int4_ops);
CREATE INDEX index_stock_item_promotions_on_store_id_and_type_v5 ON analytics.stock_item_promotions_v5(store_id int4_ops,type text_ops);
CREATE INDEX index_stock_item_promotions_on_store_id_and_sku_v5 ON analytics.stock_item_promotions_v5(store_id int4_ops,sku text_ops);
