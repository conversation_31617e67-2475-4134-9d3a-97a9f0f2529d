-- name: variants-by-promotion-details-stock-location-id-any-product-any-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE (pt.product_id = any($2) OR pt.taxon_id = any($3)) AND $1 = any($4)

-- name: variants-by-promotion-details-stock-location-id-any-product-none-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE (pt.product_id = any($2) OR pt.taxon_id != all($3)) AND $1 = any($4)

-- name: variants-by-promotion-details-stock-location-id-none-product-any-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE (pt.product_id != all($2) OR pt.taxon_id = any($3)) AND $1 = any($4)

-- name: variants-by-promotion-details-stock-location-id-none-product-none-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE (pt.product_id != all($2) AND pt.taxon_id != all($3)) AND $1 = any($4)

-- name: variants-by-promotion-details-stock-location-id-any-product
SELECT DISTINCT v.global_id
FROM spree_variants v
WHERE v.product_id = any($2) AND $1 = any($3)

-- name: variants-by-promotion-details-stock-location-id-none-product
SELECT DISTINCT v.global_id
FROM spree_variants v
WHERE v.product_id != all($2) AND $1 = any($3)

-- name: variants-by-promotion-details-stock-location-id-any-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE pt.taxon_id = any($2) AND $1 = any($3)

-- name: variants-by-promotion-details-stock-location-id-none-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE pt.taxon_id != all($2) AND $1 = any($3)

-- name: variants-by-promotion-details-none-stock-location-id-any-product-any-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE (pt.product_id = any($2) OR pt.taxon_id = any($3)) AND $1 != all($4)

-- name: variants-by-promotion-details-none-stock-location-id-any-product-none-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE (pt.product_id = any($2) OR pt.taxon_id != all($3)) AND $1 != all($4)

-- name: variants-by-promotion-details-none-stock-location-id-none-product-any-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE (pt.product_id != all($2) OR pt.taxon_id = any($3)) AND $1 != all($4)

-- name: variants-by-promotion-details-none-stock-location-id-none-product-none-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE (pt.product_id != all($2) AND pt.taxon_id != all($3)) AND $1 != all($4)

-- name: variants-by-promotion-details-none-stock-location-id-any-product
SELECT DISTINCT v.global_id
FROM spree_variants v
WHERE v.product_id = any($2) AND $1 != all($3)

-- name: variants-by-promotion-details-none-stock-location-id-none-product
SELECT DISTINCT v.global_id
FROM spree_variants v
WHERE v.product_id != all($2) AND $1 != all($3)

-- name: variants-by-promotion-details-none-stock-location-id-any-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE pt.taxon_id = any($2) AND $1 != all($3)

-- name: variants-by-promotion-details-none-stock-location-id-none-taxon
SELECT DISTINCT v.global_id
FROM spree_variants v
LEFT JOIN spree_products_taxons pt ON pt.product_id = v.product_id
WHERE pt.taxon_id != all($2) AND $1 != all($3)