-- name: taxon-name-by-taxon-id
select global_id, name from spree_taxons where global_id=$1

-- name: taxon-by-product-id
SELECT itxa.global_id,
     a_stt.name,
     b_stt.name as name_locale
FROM spree_taxons itxc
LEFT JOIN spree_taxons itxa ON itxa.lft <= itxc.lft
AND itxa.rgt >= itxc.rgt
AND itxa.depth <> 0
LEFT JOIN spree_taxon_translations a_stt ON a_stt.spree_taxon_id = itxa.id
AND a_stt.locale = 'en'
LEFT join spree_taxon_translations b_stt ON b_stt.spree_taxon_id = itxa.id
AND b_stt.locale = $2
LEFT JOIN spree_products_taxons spt ON itxc.id = spt.taxon_id
INNER JOIN spree_products p ON p.id = spt.product_id
WHERE p.global_id = $1
