-- name: product-type-by-sku
SELECT coalesce(pty.global_id, pty.id) as id,
       pty.parent_id,
       pty.display_name_translations -> 'en' AS name_en,
       pty.display_name_translations -> $2 AS name_local,
       pty.name as path
FROM spree_variants v
INNER JOIN spree_products p ON p.id = v.product_id
AND v.is_master = TRUE
INNER JOIN spree_product_types pty ON pty.id = p.product_type_id and pty.depth != 0
WHERE v.sku = $1

-- name: product-type-by-product-id
SELECT coalesce(pty.global_id, pty.id) as id,
       pty.parent_id,
       pty.display_name_translations -> 'en' AS name_en,
       pty.display_name_translations -> $2 AS name_local,
       pty.name as path
FROM spree_products p
INNER JOIN spree_product_types pty ON pty.id = p.product_type_id and pty.depth != 0
WHERE p.id = $1

-- name: product-types-by-product-id
SELECT coalesce(ptya.global_id, ptya.id) as id,
       ptya.parent_id,
       ptya.display_name_translations -> 'en' AS name_en,
       ptya.display_name_translations -> $2 AS name_local,
       ptya.name as path
FROM spree_products p
INNER JOIN spree_product_types pty ON pty.id = p.product_type_id and pty.depth != 0
LEFT JOIN spree_product_types ptya ON ptya.lft <= pty.lft
AND ptya.rgt >= pty.rgt
AND ptya.depth <> 0
WHERE p.global_id = $1