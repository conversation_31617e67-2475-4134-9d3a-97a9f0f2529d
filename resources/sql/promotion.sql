-- name: promotion-detail-by-id
SELECT p.id,
       p.display_name,
       pr.type,
       pr.preferences,
       (CASE
           WHEN pr.type = 'Spree::Promotion::Rules::Product' THEN array_agg(ppr.product_id)
           WHEN pr.type = 'Spree::Promotion::Rules::TaxonRule' THEN array_agg(tb.id)
           WHEN pr.type = 'Spree::Promotion::Rules::StockLocationRule' THEN array_agg(slpr.stock_location_id)
       END),
       p.code
FROM spree_promotions p
LEFT JOIN spree_promotion_rules pr ON pr.promotion_id = p.id
LEFT JOIN spree_products_promotion_rules ppr ON ppr.promotion_rule_id = pr.id
LEFT JOIN spree_taxons_promotion_rules tpr ON tpr.promotion_rule_id = pr.id
LEFT JOIN spree_stock_locations_promotion_rules slpr ON slpr.promotion_rule_id = pr.id
LEFT JOIN spree_taxons ta ON ta.id = tpr.taxon_id
LEFT JOIN spree_taxons tb ON tb.lft >= ta.lft
AND tb.rgt <= ta.rgt
WHERE p.id = $1
GROUP BY 1,
         2,
         3,
         4