-- name: product-by-id
SELECT
	p.global_id AS id,
	p.name AS name,
	coalesce(pt.name, '') AS name_local,
	p.description AS description,
	coalesce(pt.description, '') AS description_local,
	v.global_id AS variant_id,
	v.sku AS sku,
	v.is_master AS is_master,
	v.track_inventory AS track_inventory,
	si.in_stock AS in_stock,
	coalesce(pty.name, '') AS product_type,
	b.global_id AS brand_id,
	b.name AS brand_name,
	si.id AS stock_item_id,
	p.properties AS properties,
	si.popularity AS popularity,
	si.boosting_point AS boosting_point,
	coalesce(si.promotion_id, -1) promotion_id,
	coalesce(si.max_order_quantity, 0) AS max_order_quantity,
	p.shipping_category_id AS shipping_category_id,
	p.available_on AS available_on,
    $1 AS locale
FROM spree_products p
INNER JOIN spree_variants v ON p.id = v.product_id
INNER JOIN spree_stock_items si ON v.id = si.variant_id
INNER JOIN spree_stock_locations sl ON si.stock_location_id = sl.id
LEFT JOIN spree_product_translations pt ON p.id = pt.spree_product_id
LEFT JOIN spree_product_types pty ON p.product_type_id = pty.id
LEFT JOIN spree_brands b ON p.brand_id = b.id
WHERE pt.locale = $1 AND sl.id = $2 AND p.global_id = $3;
