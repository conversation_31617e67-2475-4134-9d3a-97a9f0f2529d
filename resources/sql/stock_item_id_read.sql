-- name: stock-item-ids-by-variant-store-popularity-desc
SELECT DISTINCT si.id, si.popularity + si.boosting_point AS final_popularity
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY final_popularity DESC;

-- name: stock-item-ids-by-variant-store-name-asc
SELECT DISTINCT si.id, p.name
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
INNER JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY p.name ASC;

-- name: stock-item-ids-by-variant-store-name-desc
SELECT DISTINCT si.id, p.name
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
INNER JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY p.name DESC;

-- name: stock-item-ids-by-variant-store-price-asc
SELECT DISTINCT si.id, si.price
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY si.price ASC;

-- name: stock-item-ids-by-variant-store-price-desc
SELECT DISTINCT si.id, si.price
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY si.price DESC;

-- name: stock-item-ids-by-variant-store-unit-price-asc
SELECT DISTINCT si.id, si.unit_price
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY si.unit_price ASC;

-- name: stock-item-ids-by-variant-store-unit-price-desc
SELECT DISTINCT si.id, si.unit_price
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY si.unit_price DESC;

-- name: stock-item-ids-by-variant-store-variant-id
SELECT DISTINCT si.id, array_position($1, v.global_id) as pos
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY pos;

-- name: stock-item-ids-by-variant-store-pinned-popularity-desc
SELECT r.stock_item_id, r.final_popularity FROM (
  (
    SELECT DISTINCT
      si.id AS stock_item_id,
      array_position($5, v.global_id) AS pin_order,
      si.popularity + si.boosting_point AS final_popularity
    FROM spree_stock_items si
    INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
    INNER JOIN spree_variants v on v.id = si.variant_id
    LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
    LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
    LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
    WHERE v.global_id = ANY($5)
    AND (t.parent_id != ALL($3) AND t.id != ALL($3))
    AND sl.store_id  = $2
    AND si.in_stock = TRUE
    AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
  )
  UNION (
    SELECT DISTINCT
      si.id AS stock_item_id,
      0 AS pin_order,
      si.popularity + si.boosting_point AS final_popularity
    FROM spree_stock_items si
    INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
    INNER JOIN spree_variants v on v.id = si.variant_id
    LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
    LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
    LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
    WHERE v.global_id = ANY($1)
    AND v.global_id != ALL($5) 
    AND (t.parent_id != ALL($3) AND t.id != ALL($3))
    AND sl.store_id  = $2
    AND si.in_stock = TRUE
    AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
  )
) AS r
ORDER BY
  CASE WHEN pin_order > 0 THEN 1 WHEN pin_order = 0 THEN 2 END ASC,
  pin_order ASC,
  final_popularity DESC;

-- name: stock-item-ids-by-variant-store-discount-asc
SELECT DISTINCT si.id, coalesce((si.normal_price - si.price) / si.normal_price, 0)
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY 2 ASC;

-- name: stock-item-ids-by-variant-store-discount-desc
SELECT DISTINCT si.id, coalesce((si.normal_price - si.price) / si.normal_price, 0)
FROM spree_stock_items si
INNER JOIN spree_stock_locations sl on sl.id = si.stock_location_id
INNER JOIN spree_variants v on v.id = si.variant_id
LEFT JOIN spree_products_taxons pt on pt.product_id = v.product_id
LEFT JOIN spree_taxons t  on t.id = pt.taxon_id
LEFT JOIN spree_products p on p.id = v.product_id AND v.is_master = TRUE
WHERE v.global_id = ANY($1)
AND (t.parent_id != ALL($3) AND t.id != ALL($3))
AND sl.store_id  = $2
AND si.in_stock = TRUE
AND ((p.sellable_items != ALL($4)) OR (p.sellable_items IS NULL))
ORDER BY 2 DESC;

