-- name: stock-item-promotion-by-store-id-and-types
SELECT
    -1,
    s.store_id,
    s.sku,
    s.type,
    s.buy_x_quantity,
    s.get_y_quantity,
    s.promotion_id,
    COALESCE(s.get_p_percent, 0)
FROM analytics.stock_item_promotions_v5 s
WHERE s.store_id = $1 AND s.type = ANY($2)
LIMIT 1020;

-- name: stock-item-promotion-by-store-id-sku
SELECT
    -1,
    s.store_id,
    s.sku,
    s.type,
    s.buy_x_quantity,
    s.get_y_quantity,
    s.promotion_id,
    COALESCE(s.get_p_percent, 0)
FROM analytics.stock_item_promotions_v5 s
WHERE s.store_id = $1 AND s.sku = $2

-- name: stock-item-promotion-count-by-store-id
SELECT s.type,
    COUNT(*) AS "total"
FROM analytics.stock_item_promotions_v5 s
WHERE s.store_id = $1
GROUP BY s.type;

-- name: stock-item-promotion-by-store-id-and-is-exclusive
SELECT
    -1,
    s.store_id,
    s.sku,
    s.type,
    s.buy_x_quantity,
    s.get_y_quantity,
    s.promotion_id,
    COALESCE(s.get_p_percent, 0)
FROM analytics.stock_item_promotions_v5 s
WHERE s.store_id = $1 AND s.is_exclusive = $2
LIMIT 1020;